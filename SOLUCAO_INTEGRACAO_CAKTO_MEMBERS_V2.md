# Solução de Integração Cakto ↔ Members V2

## Visão Geral

Esta solução implementa uma integração completa entre o **Cakto Frontend/Backend** e o **Cakto Members V2**, permitindo:

1. **Criação automática de usuários produtores** quando um produto com content delivery "cakto" for criado
2. **Associação de produtos a cursos** durante a criação de cursos na área de membros
3. **Liberação automática de acesso** quando clientes comprarem produtos
4. **Sistema de liberação de acesso para testers** baseado em User IDs do Cakto

## Fluxo Implementado

### 1. Criação de Produto no Cakto Frontend

**Quando:** Usuário cria um produto com "Content Delivery: Área de membros cakto"

**O que acontece:**
- Webhook é enviado para `cakto-members-v2` em `/api/webhooks/cakto/producer-created`
- Usuário produtor é criado automaticamente no Members V2
- Organização é criada para o produtor
- Produtor é adicionado como owner da organização

**Payload do Webhook:**
```json
{
  "secret": "webhook-secret",
  "event": "producer_created",
  "data": {
    "producer": {
      "id": "user-id-from-cakto",
      "name": "John Doe",
      "email": "<EMAIL>",
      "phone": "34999999999"
    },
    "product": {
      "id": "product-id-from-cakto",
      "name": "Produto Teste",
      "contentDelivery": "cakto"
    }
  }
}
```

### 2. Criação de Curso no Members V2

**Quando:** Produtor cria um curso na área de membros

**O que acontece:**
- Novo passo "Integração com Cakto" no wizard de criação
- Campo para inserir o ID do produto do Cakto
- Botão "Ir para Cakto" que abre o produto no cakto-frontend
- Mapeamento curso ↔ produto é salvo na tabela `courseProduct`

**Interface:**
- Switch para ativar/desativar integração
- Campo para ID do produto
- Validação e feedback visual
- Instruções de como usar

### 3. Compra de Produto no Cakto

**Quando:** Cliente compra um produto com content delivery "cakto"

**O que acontece:**
- Webhook é enviado para `cakto-members-v2` em `/api/webhooks/cakto/purchase`
- Usuário é criado/atualizado no Members V2
- Acesso ao curso é liberado automaticamente
- Magic link é enviado para acesso imediato

### 4. Sistema de Liberação de Acesso para Testers

**Quando:** Administrador precisa liberar acesso para usuários testers

**O que acontece:**
- Interface administrativa em `/admin/tester-access`
- Campo para inserir array de User IDs do Cakto
- Opção para especificar curso/organização específica
- Processamento em lote com feedback detalhado

## Arquivos Implementados/Modificados

### Cakto Members V2

#### Novos Endpoints:
- `POST /api/webhooks/cakto/producer-created` - Webhook para criação de produtores
- `POST /api/courses/course/:courseId/product-mapping` - Mapeamento curso-produto
- `POST /api/admin/grant-tester-access` - Liberação de acesso para testers

#### Novos Componentes:
- `SettingsStep.tsx` - Passo de integração no wizard de criação de curso
- `TesterAccessPage.tsx` - Interface para liberação de acesso de testers

#### Modificações:
- `types.ts` - Adicionado campo `caktoProductId`
- `useCourseCreation.ts` - Integração com mapeamento de produto
- `api.ts` - Nova função `createCourseProductMapping`

### Cakto Backend

#### Modificações:
- `members_v2_producer.py` - Adicionado webhook para criação de produtores
- Integração com sistema existente de webhooks

## Estrutura de Dados

### Tabela `courseProduct` (já existente):
```sql
CREATE TABLE courseProduct (
  id VARCHAR(25) PRIMARY KEY,
  courseId VARCHAR(25) NOT NULL,
  caktoProductId VARCHAR(255) NOT NULL,
  caktoProductName VARCHAR(255),
  createdAt DATETIME DEFAULT NOW(),
  updatedAt DATETIME DEFAULT NOW(),
  UNIQUE(courseId, caktoProductId)
);
```

### Tabela `user` (modificada):
```sql
ALTER TABLE user ADD COLUMN mainAppUserId VARCHAR(255);
-- Para vincular usuários do Members V2 com usuários do Cakto
```

## Configuração Necessária

### Variáveis de Ambiente:

#### Cakto Members V2:
```env
CAKTO_WEBHOOK_SECRET=1340098d-340d-488a-af83-f80e0eaaa773
```

#### Cakto Backend:
```env
CAKTO_MEMBERS_V2_BASE_URL=https://aluno.cakto.com.br
CAKTO_MEMBERS_V2_API_KEY=1340098d-340d-488a-af83-f80e0eaaa773
```

## Como Usar

### Para Produtores:

1. **Criar produto no Cakto:**
   - Acesse `app.cakto.com.br/dashboard/products/create`
   - Selecione "Content Delivery: Área de membros cakto"
   - Produto será criado e webhook enviado automaticamente

2. **Criar curso no Members V2:**
   - Acesse `aluno.cakto.com.br/app/[organization]/courses/create`
   - Complete os passos do wizard
   - No passo "Configurações", ative "Integração com Cakto"
   - Cole o ID do produto criado no Cakto
   - Clique em "Ir para Cakto" se precisar verificar o produto

### Para Administradores:

1. **Liberar acesso para testers:**
   - Acesse `aluno.cakto.com.br/app/admin/tester-access`
   - Insira os User IDs do Cakto (um por linha)
   - Opcionalmente especifique curso/organização
   - Clique em "Liberar Acesso"

### Para Clientes:

1. **Comprar produto:**
   - Produto é comprado normalmente no Cakto
   - Acesso é liberado automaticamente no Members V2
   - Magic link é enviado para acesso imediato

## Benefícios da Solução

1. **Automação Completa:** Fluxo totalmente automatizado desde criação até acesso
2. **Integração Transparente:** Usuários não precisam entender a integração
3. **Flexibilidade:** Suporte a diferentes tipos de acesso (curso específico, organização)
4. **Rastreabilidade:** Logs detalhados de todas as operações
5. **Escalabilidade:** Processamento em lote para testers
6. **Segurança:** Validação de permissões e autenticação em todos os endpoints

## Próximos Passos

1. **Testes:** Implementar testes automatizados para todos os fluxos
2. **Monitoramento:** Adicionar métricas e alertas para webhooks
3. **Interface:** Melhorar interface de administração com listagem de mapeamentos
4. **Documentação:** Criar documentação técnica detalhada
5. **Backup:** Implementar sistema de backup para mapeamentos críticos
