name: Django CI
on:
  workflow_dispatch:
  push:
    branches:
      - main
      - staging
  pull_request:
    branches:
      - main
      - staging

env:
  BACKEND_IMAGE_NAME: cakto-backend
  REGISTRY_NAME: cakto
  REGISTRY_URL: registry.digitalocean.com

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      - name: Run tests
        run: docker compose -f docker-compose-tests.yaml up --build --exit-code-from cakto-backend-tests

  build_and_push:
    if: github.event_name != 'pull_request'
    needs: test
    runs-on: ubuntu-latest
    outputs:
      tag_name: ${{ steps.tag.outputs.tag_name }}
      complete_backend_image_name: ${{ steps.backend_image_name.outputs.complete_backend_image_name }}

    steps:
      - name: Set tag name
        id: tag
        run: echo "tag_name=$(date +'%Y-%m-%d').${{ github.run_number }}" >> $GITHUB_OUTPUT

      - name: Set complete backend image name
        id: backend_image_name
        run: echo "complete_backend_image_name=${{ env.REGISTRY_URL }}/${{ env.REGISTRY_NAME }}/${{ env.BACKEND_IMAGE_NAME }}" >> $GITHUB_OUTPUT

      - name: Install doctl
        uses: digitalocean/action-doctl@v2
        with:
          token: ${{ secrets.DIGITALOCEAN_ACCESS_TOKEN }}
          version: v1.135.0

      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Registry login
        run: doctl registry login

      - name: Build and publish docker image ${{ env.BACKEND_IMAGE_NAME }}:${{ steps.tag.outputs.tag_name }}
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./Dockerfile
          push: true
          tags: ${{ steps.backend_image_name.outputs.complete_backend_image_name }}:${{ steps.tag.outputs.tag_name }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

  deploy_to_staging:
    if: github.ref == 'refs/heads/staging'
    needs: build_and_push
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Install kubectl
        uses: azure/setup-kubectl@v4

      - uses: azure/setup-helm@v4.3.0
        with:
          version: "v3.17.3"

      - name: Set kubeconfig
        uses: azure/k8s-set-context@v4
        with:
          method: kubeconfig
          kubeconfig: ${{ secrets.KUBE_CONFIG_STAGING }}

      - name: Deploy via helm
        run: |
          helm upgrade --install cakto-backend-staging kubernetes/helm/cakto-backend \
            -f kubernetes/helm/cakto-backend/base-values.yaml \
            -f kubernetes/helm/cakto-backend/staging-values.yaml \
            --set backend.container.image=${{ needs.build_and_push.outputs.complete_backend_image_name }} \
            --set backend.container.tag=${{ needs.build_and_push.outputs.tag_name }} \
            --set backend.ingress.host=${{ secrets.BACKEND_URL_STAGING }} \
            --set backend.ingress.additionalHosts="${{ secrets.ALTERNATIVE_BACKEND_URL_LIST_STAGING }}" \
            --namespace cakto-backend-staging \
            --create-namespace

  deploy_to_prd:
    if: github.ref == 'refs/heads/main'
    needs: build_and_push
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Install kubectl
        uses: azure/setup-kubectl@v4

      - uses: azure/setup-helm@v4.3.0
        with:
          version: "v3.17.3"

      - name: Set kubeconfig
        uses: azure/k8s-set-context@v4
        with:
          method: kubeconfig
          kubeconfig: ${{ secrets.KUBE_CONFIG_PRD }}

      - name: Deploy via helm
        run: |
          helm upgrade --install cakto-backend-prd kubernetes/helm/cakto-backend \
            -f kubernetes/helm/cakto-backend/base-values.yaml \
            -f kubernetes/helm/cakto-backend/prd-values.yaml \
            --set backend.container.image=${{ needs.build_and_push.outputs.complete_backend_image_name }} \
            --set backend.container.tag=${{ needs.build_and_push.outputs.tag_name }} \
            --set backend.ingress.host=${{ secrets.BACKEND_URL_PRD }} \
            --set backend.ingress.additionalHosts="${{ secrets.ALTERNATIVE_BACKEND_URL_LIST_PRD }}" \
            --namespace cakto-backend-prd \
            --create-namespace

  deploy_to_nommi:
    if: github.ref == 'refs/heads/main'
    needs: build_and_push
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Install kubectl
        uses: azure/setup-kubectl@v4

      - uses: azure/setup-helm@v4.3.0
        with:
          version: "v3.17.3"

      - name: Set kubeconfig
        uses: azure/k8s-set-context@v4
        with:
          method: kubeconfig
          kubeconfig: ${{ secrets.KUBE_CONFIG_NOMMI }}

      - name: Deploy via helm
        run: |
          helm upgrade --install cakto-backend-prd kubernetes/helm/cakto-backend \
            -f kubernetes/helm/cakto-backend/base-values.yaml \
            -f kubernetes/helm/cakto-backend/nommi-values.yaml \
            --set backend.container.image=${{ needs.build_and_push.outputs.complete_backend_image_name }} \
            --set backend.container.tag=${{ needs.build_and_push.outputs.tag_name }} \
            --set backend.ingress.host=${{ secrets.BACKEND_URL_NOMMI }} \
            --set backend.ingress.additionalHosts="${{ secrets.ALTERNATIVE_BACKEND_URL_LIST_NOMMI }}" \
            --namespace cakto-backend-nommi \
            --create-namespace
