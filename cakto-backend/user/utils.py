import os
from datetime import datetime
from typing import Optional

import requests
from django.conf import settings
from django.contrib.auth import get_user_model
from django.contrib.auth.tokens import PasswordResetTokenGenerator
from django.db.models import Subquery
from django.utils import timezone
from django.utils.crypto import constant_time_compare, salted_hmac
from django.utils.http import base36_to_int, int_to_base36
from django_rq import job
from rest_framework.request import Request
from rest_framework_simplejwt.tokens import AccessToken, RefreshToken, Token
from rq import Retry

from gateway.utils import get_client_ip
from user.enums import UserAccessLocations
from user.models import Award, MFAMethod, User, UserAward, User<PERSON>oginHistory


@job('default', retry=Retry(max=3, interval=[5, 10, 30]))
def create_user_awards(user: User):
    sales_amount = user.totalSales()
    user_awards = UserAward.objects.filter(user=user).values('award') # Get user awards
    # Exclude user awards and filter by rank min_velue less than total sales
    awards = Award.objects.exclude(id__in=Subquery(user_awards))\
        .filter(rank__min_value__lte=sales_amount)
    # If no awards found through rank, filter by target sales
    if not awards:
        awards = Award.objects.exclude(id__in=Subquery(user_awards)).filter(target_sales__lte=sales_amount)
    return UserAward.objects.bulk_create([UserAward(user=user, award=award) for award in awards])

def get_or_create_user(
    email: str,
    cellphone: str | None = None,
    emailValidated: bool = False,
    is_producer: bool = False,
    is_customer: bool = False,
) -> User:
    from user.models import User
    user, created = User.objects.get_or_create(
        email__iexact=email,
        defaults={
            'email': email.lower(),
            'username': email,
            'viewPageAs': 'student',
            'cellphone': cellphone,
            'emailValidated': emailValidated,
            'is_producer': is_producer,
            'is_customer': is_customer,
        }
    )

    if created:
        user.set_unusable_password()
        user.save(update_fields=['password'])

        if is_producer:
            MFAMethod.get_email_method(user, confirmed=True)

    return user

class AccessTokenCustom(AccessToken):
    pass

class RefreshTokenCustom(RefreshToken):
    pass

def create_user_refreshToken(
    user: User,
    accessToken_lifetime: dict[str, int | float],
    refreshToken_lifetime: dict[str, int | float],
    access_location: UserAccessLocations,
    request: Request | None = None
) -> Token:
    """
    Create an access token for a given user with a specified lifetime.
    Args:
        user (User): The user for whom the access token is being created.
        accessToken_lifetime (dict[str, int | float]): A dictionary specifying the lifetime of the token.
            Possible keys include:
                - 'days': (int | float) Number of days.
                - 'seconds': (int | float) Number of seconds.
                - 'microseconds': (int | float) Number of microseconds.
                - 'milliseconds': (int | float) Number of milliseconds.
                - 'minutes': (int | float) Number of minutes.
                - 'hours': (int | float) Number of hours.
                - 'weeks': (int | float) Number of weeks.
        refreshToken_lifetime (dict[str, int | float]): A dictionary specifying the lifetime of the token.
            Possible keys include:
                - 'days': (int | float) Number of days.
                - 'seconds': (int | float) Number of seconds.
                - 'microseconds': (int | float) Number of microseconds.
                - 'milliseconds': (int | float) Number of milliseconds.
                - 'minutes': (int | float) Number of minutes.
                - 'hours': (int | float) Number of hours.
                - 'weeks': (int | float) Number of weeks.
    Returns:
        Token: The generated refresh token for the user.
    Usage:
        token = create_user_refreshToken(user, {'days': 1}, {'days': 7})
        accessToken = token.access_token
        refreshToken = str(token)
        print(accessToken, refreshToken)
    """

    refresh = RefreshTokenCustom
    refresh.lifetime = timezone.timedelta(**refreshToken_lifetime)

    access_token_class = AccessTokenCustom
    access_token_class.lifetime = timezone.timedelta(**accessToken_lifetime)

    refresh.access_token_class = access_token_class

    log_user_login_history(user=user, request=request, location=access_location.id)

    return refresh.for_user(user)

class UserTokenGenerator(PasswordResetTokenGenerator):
    """
    Extracted from django-trench:
    https://github.com/merixstudio/django-trench/blob/develop/trench/utils.py

    Custom token generator:
        - user pk in token
        - expires after 5 minutes
        - longer hash (40 instead of 20)
    """

    KEY_SALT = "django.contrib.auth.tokens.PasswordResetTokenGenerator"
    SECRET = settings.SECRET_KEY
    EXPIRY_TIME = 60 * 5  # seconds

    def make_token(self, user: User) -> str:
        return self._make_token_with_timestamp(user, int(datetime.now().timestamp()))

    def check_token(self, user: User, token: str) -> Optional[User]:
        user_model = get_user_model()
        if not token:
            return None
        try:
            token = str(token)
            user_pk, ts_b36, token_hash = token.rsplit("-", 2)
            ts = base36_to_int(ts_b36)
            user = User.objects.get(pk=user_pk)
        except (ValueError, TypeError, user_model.DoesNotExist):
            return None

        if (datetime.now().timestamp() - ts) > self.EXPIRY_TIME:
            return None

        if not constant_time_compare(self._make_token_with_timestamp(user, ts), token):
            return None

        return user

    def _make_token_with_timestamp(self, user: User, timestamp: int, **kwargs) -> str:
        ts_b36 = int_to_base36(timestamp)
        token_hash = salted_hmac(
            self.KEY_SALT,
            self._make_hash_value(user, timestamp),
            secret=self.SECRET,
        ).hexdigest()
        return f"{user.pk}-{ts_b36}-{token_hash}"


user_token_generator = UserTokenGenerator()


def log_user_login_history(user: User, request: Request | None = None, location: str = '') -> UserLoginHistory:
    user_agent = None
    ip = None

    if request:
        ip = get_client_ip(request=request)
        user_agent = request.META.get('HTTP_USER_AGENT', '')

    if not ip and not location:
        raise ValueError("Either 'ip' or 'location' must be provided.")

    return UserLoginHistory.objects.create(
        user=user,
        ip=ip,
        user_agent=user_agent,
        access_token_source=location
    )

class RegisterWebhookEvent:
    def send_event(self, user: User) -> None:
        url = os.getenv('REGISTER_PRODUCER_WEBHOOK_URL')
        if not url:
            return

        payload = self._get_payload(user)

        self._dispatch_event(url, payload)

    def _dispatch_event(self, url: str, payload: dict) -> None:
        requests.post(
            url,
            json=payload,
            headers={'Content-Type': 'application/json'},
            timeout=4
        )

    def _get_payload(self, user: User) -> dict:
        return {
            'name': user.get_full_name(),
            'companyName': user.companyName,
            'instagram': user.instagram,
            'email': user.email,
            'cellphone': user.get_full_phone(),
            'companyProduct': user.companyProduct,
            'mostSells': user.mostSells,
            'recurringAmount': float(user.recurringAmount or 0),
            'businessModel': user.businessModel,
            'actualPlatform': user.actualPlatform,
            'biggerPain': user.biggerPain,
            'indication': user.indication,
            'createdAt': user.createdAt.isoformat(),
            'utm_source': user.utm_source,
            'utm_medium': user.utm_medium,
            'utm_campaign': user.utm_campaign,
            'utm_term': user.utm_term,
            'utm_content': user.utm_content,
            'sck': user.sck,
        }

@job('default', retry=Retry(max=2, interval=360))
def register_webhook_job(user: User) -> None:
    event_sender = RegisterWebhookEvent()
    event_sender.send_event(user)

def send_register_webhook(user: User) -> None:
    """
    Sends a registration webhook event for the given user.
    It checks for the environment variable 'REGISTER_PRODUCER_WEBHOOK_URL'.
    If the URL is not set, it does nothing.
    If the URL is set, it dispatches the event using a background job.
    """
    url = os.getenv('REGISTER_PRODUCER_WEBHOOK_URL')
    if not url:
        return

    register_webhook_job.delay(user)
