from django.test import override_settings
from unittest import mock

import requests
import responses
from django.utils import timezone

from cakto.tests.base import BaseTestCase
from financial.enums import CompanyStatus, CompanyType
from financial.utils import handle_approval, handle_chargeback, handle_refund
from product.models import PaymentMethod
from user.models import CRMApiHistory
from user.sdk.crm import CRMSdk, send_crm_chargeback_event, send_crm_create_user_event, send_crm_refund_event, send_crm_sale_event


@override_settings(
    CRM_WEBHOOK_URL='http://localhost:8000',
    CRM_WEBHOOK_TOKEN='123'
)
class CRMSdkTests(BaseTestCase):
    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()

        cls.credit_card, _ = PaymentMethod.objects.get_or_create(type='credit_card', defaults={'name': 'Cartão de Crédito'})

        cls.product = cls.create_product(user=cls.user)

        cls.order = cls.create_order(product=cls.product)

        cls.sdk = CRMSdk()

        cls.company = cls.user.company
        cls.company.companyName = 'Company Test'
        cls.company.companyCnpj = '12345678901234'
        cls.company.type = CompanyType.COMPANY.id
        cls.company.status = CompanyStatus.APPROVED.id
        cls.company.birthDate = '1990-01-01'
        cls.company.save(update_fields=['companyName', 'companyCnpj', 'type', 'status', 'birthDate'])

    def test_get_user_phone(self):
        self.user.phoneCountryCode = '55'
        self.user.cellphone = '11999999999'
        self.user.save(update_fields=['phoneCountryCode', 'cellphone'])

        phone = self.sdk.get_user_phone(self.user)

        self.assertEqual(phone, '5511999999999')

    def test_get_user_document_cpf_and_cnpj_returns_cnpj(self):
        self.user.cpf = '12345678901'
        self.user.cnpj = '12345678901234'
        self.user.save(update_fields=['cpf', 'cnpj'])

        document = self.sdk.get_user_document(self.user)

        self.assertEqual(document, self.user.cnpj)

    def test_get_user_document_company_cnpj_and_cpf_returns_cnpj(self):
        document = self.sdk.get_user_document(self.user)

        self.assertEqual(document, self.user.company.companyCnpj)

    def test_get_user_document_returns_company_cpf(self):
        self.user.cpf = None
        self.user.cnpj = None
        self.user.save(update_fields=['cpf', 'cnpj'])

        self.company.companyCnpj = None
        self.company.cpf = '12345678901'
        self.company.save(update_fields=['cpf', 'companyCnpj'])

        document = self.sdk.get_user_document(self.user)

        self.assertEqual(document, self.company.cpf)

    def test_get_user_document_returns_company_cnpj(self):
        self.user.cpf = None
        self.user.cnpj = None
        self.user.save(update_fields=['cpf', 'cnpj'])

        self.company.companyCnpj = '12345678901234'
        self.company.cpf = None
        self.company.save(update_fields=['cpf', 'companyCnpj'])

        document = self.sdk.get_user_document(self.user)

        self.assertEqual(document, self.company.companyCnpj)

    def test_get_user_document_returns_user_cnpj(self):
        self.user.cpf = None
        self.user.cnpj = '12345678901234'
        self.user.save(update_fields=['cpf', 'cnpj'])

        self.company.companyCnpj = None
        self.company.cpf = None
        self.company.save(update_fields=['cpf', 'companyCnpj'])

        document = self.sdk.get_user_document(self.user)

        self.assertEqual(document, self.user.cnpj)

    def test_get_user_document_returns_user_cpf(self):
        self.user.cpf = '12345678901'
        self.user.cnpj = None
        self.user.save(update_fields=['cpf', 'cnpj'])

        self.company.companyCnpj = None
        self.company.cpf = None
        self.company.save(update_fields=['cpf', 'companyCnpj'])

        document = self.sdk.get_user_document(self.user)

        self.assertEqual(document, self.user.cpf)

    def test_get_seller_object(self):
        self.user.cellphone = '11999999999'
        self.user.phoneCountryCode = '55'
        self.user.save()

        expected_seller = {
            "caktoId": self.user.id,
            "email": self.user.email,
            "name": ' '.join([self.user.first_name, self.user.last_name]),
            'document': self.company.companyCnpj,
            "segment": self.user.mostSells,
            "organizationName": self.user.companyName,
            "phone": '5511999999999',
            "birthdate": None,
            "whatsapp": '5511999999999',
            "instagram": self.user.instagram,
            "url": None,
            "questions": {
                'companyProduct': self.user.companyProduct,
                'recurringAmount': float(self.user.recurringAmount or 0),
                'businessModel': self.user.businessModel,
                'actualPlatform': self.user.actualPlatform,
                'biggerPain': self.user.biggerPain,
                'indication': self.user.indication,
            }
        }

        seller = self.sdk.get_seller_object(self.user)

        self.assertEqual(seller, expected_seller)

    def test_get_create_payload(self):
        seller = self.sdk.get_seller_object(self.user)

        expected_payload = {
            'chargebacks': [],
            'refunds': [],
            'sells': [],
            **seller,
        }

        payload = self.sdk.get_create_payload(self.user)

        self.assertEqual(payload, expected_payload)

    def test_get_chargeback_payload(self):
        seller = self.sdk.get_seller_object(self.order.product.user)

        now = timezone.now()
        self.order.chargedbackAt = now
        self.order.save(update_fields=['chargedbackAt'])

        expected_payload = {
            'orderId': self.order.id,
            'total': int(round((self.order.amount or 0) * 100, 2)),  # Valor do chargeback como inteiro
            'chargebackDate': now.isoformat(),
            'productName': self.order.product.name,
            'buyerEmail': self.order.customer.email,
            'seller': seller,
        }

        payload = self.sdk.get_chargeback_payload(self.order)

        self.assertEqual(payload, expected_payload)

    def test_refund_payload(self):
        seller = self.sdk.get_seller_object(self.order.product.user)

        now = timezone.now()
        self.order.refundedAt = now
        self.order.reason = 'Test'
        self.order.save(update_fields=['refundedAt'])

        expected_payload = {
            'orderId': self.order.id,
            'total': int(round((self.order.amount or 0) * 100, 2)),
            'refundDate': now.isoformat(),
            'productName': self.order.product.name,
            'reason': self.order.refund_reason,
            'buyerEmail': self.order.customer.email,
            'seller': seller,
        }

        payload = self.sdk.get_refund_payload(self.order)

        self.assertEqual(payload, expected_payload)

    def test_get_sale_payload(self):
        seller = self.sdk.get_seller_object(self.order.product.user)

        expected_payload = {
            'orderId': self.order.id,
            'total': int(round((self.order.amount or 0) * 100, 2)),  # Valor do chargeback como inteiro
            'orderDate': self.order.createdAt.isoformat(),
            'buyerEmail': self.order.customer.email,
            'seller': seller,
        }

        payload = self.sdk.get_sale_payload(self.order)

        self.assertEqual(payload, expected_payload)

    @responses.activate
    def test_dispatch_crm_webhook(self):
        payload = {'test': 'test'}

        request_mock = responses.add(
            responses.POST,
            self.sdk.URL + '/test',
            json={}
        )

        response = self.sdk.dispatch_crm_webhook(payload, 'test')

        self.assertIsInstance(response, requests.Response)

        self.assertEqual(request_mock.call_count, 1)
        call = request_mock.calls[0]
        self.assertEqual(call.request.headers['Content-Type'], 'application/json')
        self.assertEqual(call.request.headers['X-Webhook-Secret'], self.sdk.TOKEN)

    @responses.activate
    def test_handle_response(self):
        responses.add(
            responses.POST,
            self.sdk.URL + '/test',
            json={}
        )

        dispatch_response = self.sdk.dispatch_crm_webhook({}, 'test')

        create_history_mock = mock.patch('user.sdk.crm.CRMSdk.create_history').start()

        response = self.sdk.handle_response(
            response=dispatch_response,
            action='test',
            user=self.user,
            order=self.order
        )

        self.assertIsInstance(response, requests.Response)
        create_history_mock.assert_called_once_with(
            response=dispatch_response,
            action='test',
            user=self.user,
            order=self.order
        )

    @responses.activate
    def test_handle_response_raises_exception(self):
        responses.add(
            responses.POST,
            self.sdk.URL + '/test',
            json={},
            status=400
        )

        dispatch_response = self.sdk.dispatch_crm_webhook({}, 'test')

        with self.assertRaises(
            Exception,
            msg=f'Error {dispatch_response.status_code} on CRM webhook -> '  # type:ignore
            f'{dispatch_response.content.decode()}'  # type:ignore
        ):
            self.sdk.handle_response(
                response=dispatch_response,
                action='test',
                user=self.user,
                order=self.order
            )

    @responses.activate
    def test_create_history(self):
        responses.add(
            responses.POST,
            self.sdk.URL + '/test',
            json={'test': 'test'},
            status=200
        )

        payload = {'payloadtest': 'testing'}
        dispatch_response = self.sdk.dispatch_crm_webhook(payload, 'test')

        response = self.sdk.create_history(
            response=dispatch_response,  # type:ignore
            action='test',
            user=self.user,
            order=self.order
        )

        self.assertIsInstance(response, CRMApiHistory)
        self.assertEqual(CRMApiHistory.objects.count(), 1)
        history: CRMApiHistory = CRMApiHistory.objects.first()  # type:ignore
        self.assertEqual(history.order, self.order)
        self.assertEqual(history.user, self.user)
        self.assertEqual(history.action, 'test')
        self.assertEqual(history.payload, payload)
        self.assertEqual(history.response, {'test': 'test'})
        self.assertEqual(history.response_status_code, 200)

    def test_send_crm_create_user_event(self):
        self.order.chargedbackAt = timezone.now()
        self.order.save(update_fields=['chargedbackAt'])

        dispatch_crm_webhook_mock = mock.patch(
            'user.sdk.crm.CRMSdk.dispatch_crm_webhook',
            return_value=mock.MagicMock()
        ).start()

        handle_response_mock = mock.patch(
            'user.sdk.crm.CRMSdk.handle_response',
            return_value=mock.MagicMock()
        ).start()

        response = send_crm_create_user_event(self.user)

        self.assertEqual(response, handle_response_mock.return_value)

        dispatch_crm_webhook_mock.assert_called_once_with(
            payload=self.sdk.get_create_payload(self.user),
            endpoint='/api/v1/loader'
        )

        handle_response_mock.assert_called_once_with(
            user=self.user,
            action='create_user',
            order=None,
            response=dispatch_crm_webhook_mock.return_value,
        )

    def test_send_crm_chargeback_event(self):
        self.order.chargedbackAt = timezone.now()
        self.order.save(update_fields=['chargedbackAt'])

        dispatch_crm_webhook_mock = mock.patch(
            'user.sdk.crm.CRMSdk.dispatch_crm_webhook',
            return_value=mock.MagicMock()
        ).start()

        handle_response_mock = mock.patch(
            'user.sdk.crm.CRMSdk.handle_response',
            return_value=mock.MagicMock()
        ).start()

        response = send_crm_chargeback_event(self.order)

        self.assertEqual(response, handle_response_mock.return_value)

        dispatch_crm_webhook_mock.assert_called_once_with(
            payload=self.sdk.get_chargeback_payload(self.order),
            endpoint=f'/api/v1/loader/{self.order.id}/chargeback'
        )

        handle_response_mock.assert_called_once_with(
            response=dispatch_crm_webhook_mock.return_value,
            action='chargeback',
            user=None,
            order=self.order
        )

    def test_send_send_crm_refund_event(self):
        self.order.refundedAt = timezone.now()
        self.order.save(update_fields=['refundedAt'])

        dispatch_crm_webhook_mock = mock.patch(
            'user.sdk.crm.CRMSdk.dispatch_crm_webhook',
            return_value=mock.MagicMock()
        ).start()

        handle_response_mock = mock.patch(
            'user.sdk.crm.CRMSdk.handle_response',
            return_value=mock.MagicMock()
        ).start()

        response = send_crm_refund_event(self.order)

        self.assertEqual(response, handle_response_mock.return_value)

        dispatch_crm_webhook_mock.assert_called_once_with(
            payload=self.sdk.get_refund_payload(self.order),
            endpoint=f'/api/v1/loader/{self.order.id}/refund'
        )

        handle_response_mock.assert_called_once_with(
            response=dispatch_crm_webhook_mock.return_value,
            action='refund',
            user=None,
            order=self.order
        )

    def test_send_send_crm_sale_event(self):
        dispatch_crm_webhook_mock = mock.patch(
            'user.sdk.crm.CRMSdk.dispatch_crm_webhook',
            return_value=mock.MagicMock()
        ).start()

        handle_response_mock = mock.patch(
            'user.sdk.crm.CRMSdk.handle_response',
            return_value=mock.MagicMock()
        ).start()

        response = send_crm_sale_event(self.order)

        self.assertEqual(response, handle_response_mock.return_value)

        dispatch_crm_webhook_mock.assert_called_once_with(
            payload=self.sdk.get_sale_payload(self.order),
            endpoint=f'/api/v1/loader/{self.order.id}/sell'
        )

        handle_response_mock.assert_called_once_with(
            response=dispatch_crm_webhook_mock.return_value,
            action='create_order',
            user=None,
            order=self.order
        )

    def test_handle_chargeback_calls_send_crm_chargeback_event(self):
        send_crm_chargeback_event_mock = mock.patch(
            'financial.utils.send_crm_chargeback_event.delay',
        ).start()

        mock.patch('financial.utils.dispatch_app_events').start()

        handle_chargeback(
            order=self.order,
            payment=mock.MagicMock(),
            webhook_data={}
        )

        send_crm_chargeback_event_mock.assert_called_once_with(order=self.order)

    def test_handle_refund_calls_send_crm_refund_event(self):
        send_crm_refund_event_mock = mock.patch(
            'financial.utils.send_crm_refund_event.delay',
        ).start()

        mock.patch('financial.utils.dispatch_app_events').start()

        handle_refund(
            order=self.order,
            payment=mock.MagicMock(),
            webhook_data={}
        )

        send_crm_refund_event_mock.assert_called_once_with(order=self.order)

    def test_handle_refund_calls_send_crm_sale_event(self):
        send_crm_sale_event_mock = mock.patch(
            'financial.utils.send_crm_sale_event.delay',
        ).start()

        mock.patch('financial.utils.dispatch_app_events').start()

        handle_approval(
            order=self.order,
            payment=mock.MagicMock(),
            webhook_data={}
        )

        send_crm_sale_event_mock.assert_called_once_with(order=self.order)
