from django.conf import settings
from rest_framework import permissions

from user.models import Colabor<PERSON>, User
from oauth2_provider.contrib.rest_framework.authentication import OAuth2Authentication


class UserIsValidated(permissions.BasePermission):
    """
    Allows access only to users that have email and whatsapp validated.
    """

    def has_permission(self, request, view):
        user: User = request.user

        if settings.WHATSAPP_VALIDATION_ENABLED and user.is_producer:
            whatsappValidated = user.whatsappValidated
        else:
            whatsappValidated = True

        return bool(user and user.emailValidated and whatsappValidated)

class ScopePermission(permissions.BasePermission):
    def has_permission(self, request, view):
        collaborator_id = int(request.META.get('Http_collaborator_id', 0))
        if collaborator_id:
            collaborator = Colaborator.objects.filter(pk=collaborator_id, status='approved').first()
            if collaborator is None:
                return False

            user = request.user
            if user == collaborator.owner:
                return True
            elif user != collaborator.user:
                return False

            action_maping = {
                # view.action
                'list': 'view',
                'retrieve': 'view',
                'create': 'create',
                'update': 'edit',
                'partial_update': 'edit',
                'destroy': 'delete',

                # http methods
                'GET': 'view',
                'POST': 'create',
                'PATCH': 'edit',
                'PUT': 'edit',
                'DELETE': 'delete',
            }

            action = getattr(view, 'action', request.method)
            perm_type = action_maping.get(action) or 'edit'  # 'edit' when it's a custom view action
            scope = view.scope
            permission = f'{scope}_{perm_type}'

            if collaborator.has_permission(permission):
                request.user = collaborator.owner
                return True
            return False
        return True

class UserIsSuperUser(permissions.BasePermission):
    def has_permission(self, request, view):
        return request.user and request.user.is_superuser
