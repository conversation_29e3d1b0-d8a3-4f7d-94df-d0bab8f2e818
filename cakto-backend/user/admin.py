from django import forms
from django.contrib import admin
from django.urls import reverse
from django.utils.html import format_html

from user.models import (Award, Colaborator, CRMApiHistory, ExperimentalFeature, MFAMethod, Rank, RecoveryTokenHistory,
                         User, UserAward, UserLoginHistory)


@admin.register(User)
class UserAdmin(admin.ModelAdmin):
    list_display = ('email', 'first_name', 'last_name', 'is_staff', 'is_superuser')
    search_fields = ('email', 'first_name', 'last_name')
    list_filter = ('is_staff', 'is_superuser')
    ordering = ('email',)
    readonly_fields = ('createdAt', 'updatedAt', 'company')

    def company(self, obj):
        url = reverse('admin:financial_company_change', args=[obj._company.id])
        return format_html('<a href="{}">Edit Company</a>', url)
    company.short_description = 'Company'  # type:ignore

@admin.register(Colaborator)
class ColaboratorAdmin(admin.ModelAdmin):
    list_display = ('user', 'status', 'createdAt')
    search_fields = ('user__email', 'user__first_name', 'user__last_name')
    list_filter = ('status', 'createdAt')
    ordering = ('-createdAt',)

    def get_form(self, request, obj=None, **kwargs):
        form = super(ColaboratorAdmin, self).get_form(request, obj, **kwargs)
        form.base_fields['permissions'] = forms.MultipleChoiceField(
            label='Permissões',
            help_text='Pressione "Control", ou "Command" no Mac, para selecionar multiplos valores.',
            choices=[(perm, f'{perm} | {desc}') for perm, desc in Colaborator.permited_permissions],
            widget=forms.SelectMultiple,
            required=False,
            initial=(obj.permissions if obj else [])
        )
        return form

@admin.register(Rank)
class RankAdmin(admin.ModelAdmin):
    list_display = ('code', 'label', 'min_value', 'max_value', 'status', 'createdAt', 'updatedAt')
    search_fields = ('code', 'label', 'min_value', 'max_value', 'status')
    list_filter = ('status', 'createdAt', 'updatedAt')
    ordering = ('-min_value',)
    readonly_fields = ('createdAt', 'updatedAt')

@admin.register(Award)
class AwardAdmin(admin.ModelAdmin):
    list_display = ('title', 'rank', 'target_sales', 'status', 'createdAt', 'updatedAt')
    search_fields = ('title', 'rank', 'target_sales', 'description')
    list_filter = ('status', 'createdAt', 'updatedAt')
    ordering = ('-createdAt',)
    readonly_fields = ('createdAt', 'updatedAt')

@admin.register(UserAward)
class UserAwardAdmin(admin.ModelAdmin):
    list_display = ('user', 'award', 'status', 'shipping_method', 'createdAt', 'updatedAt')
    search_fields = ('user__email', 'award__title', 'shipping_method', 'tracking_code')
    list_filter = ('status', 'shipping_method', 'createdAt', 'updatedAt')
    ordering = ('-createdAt',)
    readonly_fields = ('user', 'createdAt', 'updatedAt')

@admin.register(CRMApiHistory)
class CRMApiHistoryAdmin(admin.ModelAdmin):
    list_display = ('id', 'user', 'order', 'action', 'response_status_code', 'createdAt')
    search_fields = (
        'user__email', 'user__cpf', 'user__cnpj', 'user__username',
        'order__id', 'order__refId', 'order__externalId', 'order__customer__email', 'order__customer__docNumber',
        'payload', 'response'
    )
    list_filter = ('action', 'response_status_code', 'createdAt')
    ordering = ('-createdAt',)
    readonly_fields = ('order', 'user', 'createdAt', 'action', 'payload', 'response', 'response_status_code')

    def has_add_permission(self, request):
        return False

    def has_delete_permission(self, request, obj=None):
        return False

@admin.register(RecoveryTokenHistory)
class RecoveryTokenHistoryAdmin(admin.ModelAdmin):
    model = RecoveryTokenHistory
    list_display = ('id', 'user', 'event_type', 'timestamp', 'requester_ip_address', 'requester_user_agent')
    list_display_links = ('id', 'user')
    search_fields = ('user__email', 'user__cpf', 'user__cnpj', 'id', 'requester_ip_address', 'requester_user_agent')
    list_filter = ('event_type', 'token_expires_at', 'timestamp', 'updatedAt')
    ordering = ('-timestamp',)
    readonly_fields = [f.name for f in model._meta.concrete_fields]

    def has_add_permission(self, request):
        return False

    def has_delete_permission(self, request, obj=None):
        return False

@admin.register(MFAMethod)
class MFAMethodAdmin(admin.ModelAdmin):
    list_display = ('user', 'type', 'primary', 'confirmed')
    search_fields = ('user__email', 'user__id')
    list_filter = ('type', 'primary', 'createdAt', 'updatedAt')
    ordering = ('-createdAt',)
    readonly_fields = (
        'user', 'createdAt', 'updatedAt', 'email_device', 'totp_device',
        'app_cakto_device', 'fcm_device'
    )

    def delete_queryset(self, request, queryset):
        for mfa in queryset:
            mfa.delete()

@admin.register(ExperimentalFeature)
class BetaProgramAdmin(admin.ModelAdmin):
    list_display = ['name', 'id']
    search_fields = ['id', 'name']
    list_filter = ['id']

@admin.register(UserLoginHistory)
class UserLoginHistoryAdmin(admin.ModelAdmin):
    model = UserLoginHistory
    list_display = ('user', 'ip', 'access_token_source', 'user_agent', 'createdAt')
    search_fields = ('user__email', 'ip', 'access_token_source')
    list_filter = ('createdAt', 'access_token_source')
    ordering = ('-createdAt',)
    readonly_fields = [f.name for f in model._meta.concrete_fields]

    def has_add_permission(self, request):
        return False

    def has_delete_permission(self, request, obj=None):
        return False
