import time
import uuid

from django.conf import settings
from django.contrib.auth.models import AbstractUser
from django.core.cache import cache
from django.db import models, transaction
from django.db.models import Q
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from django_otp.models import VerifyNotAllowed
from django_otp.oath import TOTP
from django_otp.plugins.otp_email.models import EmailDevice
from django_otp.plugins.otp_totp.models import TOTPDevice
from fcm_django.models import FCMDevice
from rest_framework import status
from rest_framework.exceptions import Throttled, ValidationError
from rest_framework.throttling import UserRateThrottle

from financial.enums import CompanyStatus
from gateway.sdk.splitpay import SplitPay
from user.enums import MFAType, UserAccessLocations

from .fields import LowercaseEmailField


def next_nps_survey_date():
    return timezone.now() + timezone.timedelta(days=settings.DAYS_TO_NEXT_NPS_POLL)

class User(AbstractUser):
    username = models.CharField(
        _('username'),
        max_length=255,
        unique=True,
        help_text=_('Obrigatório. 150 caracteres ou menos. Letras, dígitos e @/./+/-/_ apenas.'),
    )
    email = LowercaseEmailField(
        _('email address'),
        blank=True,
        unique=True,
        error_messages={'unique': "Um usuário com esse endereço de email já existe.", }
    )
    cpf = models.CharField(max_length=11, blank=True, null=True)

    # Profile info
    instagram = models.CharField(max_length=255, blank=True, null=True)
    picture = models.ImageField(upload_to='picture/', blank=True, null=True)
    phoneCountryCode = models.CharField(max_length=255, default='55')
    cellphone = models.CharField(max_length=255, blank=True, null=True)
    viewPageAs = models.CharField(max_length=255, choices=(
        ('student', 'Aluno'),
        ('producer', 'Produtor'),
    ), default='student')
    indication = models.CharField(max_length=255, blank=True, null=True)
    is_customer = models.BooleanField(default=False, verbose_name='É comprador')
    is_producer = models.BooleanField(default=False, verbose_name='É produtor')
    register_profile_completed = models.BooleanField(
        default=False,
        verbose_name='Cadastro de perfil completo',
        help_text='Indica se o usuário completou o cadastro de perfil'
    )

    # Company info
    cnpj = models.CharField(max_length=14, blank=True, null=True)
    companyName = models.CharField(verbose_name='Nome da empresa', max_length=255, blank=True, null=True)
    companyProduct = models.CharField(verbose_name='Tipo de produto vendido', max_length=255, blank=True, null=True)
    mostSells = models.CharField(verbose_name='Nicho mais vendido', max_length=255, blank=True, null=True)
    recurringAmount = models.DecimalField(verbose_name='Faturamento médio', max_digits=11, decimal_places=2, blank=True, null=True)
    businessModel = models.CharField(verbose_name='Modelo de negócio', max_length=255, blank=True, null=True)
    actualPlatform = models.CharField(verbose_name='Plataforma atual', max_length=255, blank=True, null=True)
    biggerPain = models.CharField(verbose_name='Maior dor', max_length=255, blank=True, null=True)

    # Addres info
    address = models.CharField(max_length=255, blank=True, null=True)
    country = models.CharField(max_length=255, blank=True, null=True)
    state = models.CharField(max_length=255, blank=True, null=True)
    cep = models.CharField(max_length=255, blank=True, null=True)
    city = models.CharField(max_length=255, blank=True, null=True)

    # Recovery token
    recovery_password_token = models.CharField(max_length=255, blank=True, null=True)
    recovery_password_token_expires = models.DateTimeField(blank=True, null=True)

    # Validation
    emailToken = models.CharField(max_length=255, blank=True, null=True)
    emailTokenExpires = models.DateTimeField(blank=True, null=True)
    emailValidated = models.BooleanField(default=False)
    emailValidatedAt = models.DateTimeField(blank=True, null=True)
    whatsappToken = models.CharField(max_length=255, blank=True, null=True)
    whatsappTokenExpires = models.DateTimeField(blank=True, null=True)
    whatsappValidated = models.BooleanField(default=False)
    whatsappValidatedAt = models.DateTimeField(blank=True, null=True)

    manychat_id = models.CharField(max_length=255, blank=True, null=True)

    # Financial
    pixKey = models.CharField(max_length=255, blank=True, null=True)

    # Admin
    refundRequest = models.BooleanField(default=False, verbose_name='Solicitação de reembolso')
    threeDsRetryEnabled = models.BooleanField(
        default=False,
        verbose_name='Habilitar 3DS Retry',
        help_text='Caso o pagamento falhe no cartão de crédito, será tentado novamente via 3DS',
    )

    # Members
    membersV2Id = models.CharField(max_length=255, blank=True, null=True)

    experimental_features = models.ManyToManyField(
        'user.ExperimentalFeature',
        related_name='users',
        blank=True,
        verbose_name='Funcionalidades Experimentais',
        help_text='Funcionalidades em teste que o usuário tem acesso',
    )

    # Facebook Cookies
    fbp = models.CharField(
        max_length=255,
        blank=True,
        null=True,
        verbose_name='Facebook Pixel ID',
        help_text='ID do pixel do Facebook'
    )
    fbc = models.CharField(
        max_length=255,
        blank=True,
        null=True,
        verbose_name='Facebook Click ID',
        help_text='ID do clique em anúncio do pixel do Facebook'
    )

    # Utms
    utm_source = models.CharField(
        max_length=255,
        null=True,
        blank=True,
        verbose_name='Utm Source',
        help_text='Fonte da campanha de marketing (utm_source)'
    )
    utm_medium = models.CharField(
        max_length=255,
        null=True,
        blank=True,
        verbose_name='Utm Medium',
        help_text='Meio da campanha de marketing (utm_medium)'
    )
    utm_campaign = models.CharField(
        max_length=255,
        null=True,
        blank=True,
        verbose_name='Utm Campaign',
        help_text='Nome da campanha de marketing (utm_campaign)'
    )
    utm_term = models.CharField(
        max_length=255,
        null=True,
        blank=True,
        verbose_name='Utm Term',
        help_text='Termo da campanha de marketing (utm_term)'
    )
    utm_content = models.CharField(
        max_length=255,
        null=True,
        blank=True,
        verbose_name='Utm Content',
        help_text='Conteúdo da campanha de marketing (utm_content)'
    )
    sck = models.CharField(
        max_length=255,
        null=True,
        blank=True,
        verbose_name='SCK',
        help_text='Código de rastreamento de campanha (sck)'
    )

    # Dates
    createdAt = models.DateTimeField(auto_now_add=True, verbose_name='Criado em')
    updatedAt = models.DateTimeField(auto_now=True, verbose_name='Atualizado em')

    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = ['username']

    totalSales_cache_prefix = 'totalSales_'
    nextRank_cache_prefix = 'next_rank_'
    nextAward_cache_prefix = 'next_award_'

    next_nps_survey_date = models.DateTimeField(
        blank=True,
        null=True,
        default=next_nps_survey_date
    )

    app_reviewed_at = models.DateTimeField(blank=True, null=True)

    class Meta(AbstractUser.Meta):
        verbose_name = 'Usuário'
        verbose_name_plural = 'Usuários'
        ordering = ['-createdAt']

    def __str__(self):
        return self.email

    def save(self, *args, **kwargs):
        from user.utils import send_register_webhook
        raise_company_create_exception = kwargs.pop('raise_exception', False)

        # Check if user has all fields and has not completed register yet
        # to send the register webhook
        if not kwargs.get('update_fields') and \
                not self.register_profile_completed and self.profile_completed():
            self.register_profile_completed = True
            send_register_webhook(user=self)

        super().save(*args, **kwargs)
        self.get_or_create_company_account(raise_exception=raise_company_create_exception)

    def getNextRank(self):
        """Returns the next rank based on total sales where sales < rank.min_value"""
        sales = self.totalSales()

        def get_next_rank():
            next_rank = Rank.objects.filter(
                min_value__gt=sales, status='active'
                ).order_by('min_value').first()
            return next_rank

        next_rank = cache.get_or_set(f'{self.nextRank_cache_prefix}{self.pk}', get_next_rank, 60 * 60 * 24)
        return next_rank

    def getNextAward(self):
        """Returns the next award based on rank or total sales if rank has not awards linked"""
        def get_next_award():
            next_rank = self.getNextRank()
            if next_rank:
                next_awards = list(next_rank.awards.all())
                if next_awards:
                    return next_awards[0]
            return Award.objects.filter(target_sales__gt=self.totalSales(), status='active')\
                .order_by('target_sales').first()

        next_award = cache.get_or_set(f'{self.nextAward_cache_prefix}{self.pk}', get_next_award, 60 * 60 * 24)
        return next_award

    def totalSales(self):
        from gateway.models import Order

        def get_totalSales():
            return Order.get_all_user_values_from_queryset(Order.objects.filter(commissionedUsers=self, status='paid'), self)

        totalSales = cache.get_or_set(f'{self.totalSales_cache_prefix}{self.pk}', get_totalSales, 60 * 60 * 24)
        return totalSales

    def companies(self):
        return [{
            "id": colaborator.user.pk,
            "name": colaborator.user.company.companyName or colaborator.user.first_name,  # type: ignore
        } for colaborator in Colaborator.objects.filter(owner=self, status='approved')]

    @property
    def company(self):
        return self.get_or_create_company_account()

    def get_or_create_company_account(self, raise_exception=False):
        from financial.models import Company
        company, _ = Company.objects.get_or_create(user=self)
        if not company.externalId:
            split = SplitPay()
            response = split.createAccount(email=company.user.email, status=CompanyStatus.PENDING.id)

            if status.is_success(response.status_code):
                data = response.json()
                if data.get('id'):
                    company.externalId = data.get('id')
                    company.save()
            elif raise_exception:
                raise Exception(response.text)
        return company

    @property
    def has_otp_token(self) -> bool:
        if not self.recovery_password_token or not self.recovery_password_token_expires:
            return False
        token_is_not_exipred = bool(self.recovery_password_token_expires > timezone.now())
        return token_is_not_exipred

    def set_otp_token(self, expires_at: timezone.datetime | None = None) -> None:
        if not expires_at:
            expires_at = timezone.now() + timezone.timedelta(days=1)

        self.recovery_password_token = str(uuid.uuid4())
        self.recovery_password_token_expires = expires_at
        self.save()

    def handle_otp_token(self):
        if not self.has_usable_password():
            return self.set_otp_token()

    def get_full_phone(self):
        """
        Returns the full phone number with country code.
        """
        if not self.phoneCountryCode or not self.cellphone:
            return ''
        return f'{self.phoneCountryCode}{self.cellphone}'

    def profile_completed(self):
        return all([
            bool(getattr(self, field)) for field in [
                'email', 'first_name', 'companyName', 'instagram',
                'phoneCountryCode', 'cellphone', 'companyProduct', 'mostSells',
                'recurringAmount', 'businessModel', 'actualPlatform', 'biggerPain',
                'indication'
            ]
        ])

class Notification(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='notifications')
    title = models.CharField(max_length=255, verbose_name='Título')
    message = models.TextField(verbose_name='Mensagem')
    read = models.BooleanField(default=False, verbose_name='Lida')
    createdAt = models.DateTimeField(auto_now_add=True, verbose_name='Criado em')

    def __str__(self):
        return self.title

    def serialize(self):
        return {
            'id': self.pk,
            'title': self.title,
            'message': self.message,
            'read': self.read,
            'createdAt': self.createdAt.timestamp() * 1000 if self.createdAt else None
        }

class Colaborator(models.Model):
    permited_permissions = [
        ("dashboard_create", "Pode criar em Dashboard."),
        ("dashboard_view", "Pode visualizar em Dashboard."),
        ("dashboard_edit", "Pode editar em Dashboard."),
        ("dashboard_delete", "Pode deletar em Dashboard."),

        ("products_create", "Pode criar Produtos"),
        ("products_view", "Pode visualizar Produtos"),
        ("products_edit", "Pode editar Produtos"),
        ("products_delete", "Pode deletar Produtos"),

        ("coupons_create", "Pode criar Produtos"),
        ("coupons_view", "Pode visualizar Produtos"),
        ("coupons_edit", "Pode editar Produtos"),
        ("coupons_delete", "Pode deletar Produtos"),

        ("members_create", "Pode inserir Membros"),
        ("members_view", "Pode visualizar Membros"),
        ("members_edit", "Pode editar Membros"),
        ("members_delete", "Pode remover Membros"),

        ("affiliates_create", "Pode convidar e aceitar Afiliados"),
        ("affiliates_view", "Pode visualizar Afiliados"),
        ("affiliates_edit", "Pode editar Afiliados"),
        ("affiliates_delete", "Pode remover Afiliados"),

        ("orders_create", "Pode criar Pedidos"),
        ("orders_view", "Pode visualizar Pedidos"),
        ("orders_edit", "Pode editar Pedidos"),
        ("orders_delete", "Pode deletar Pedidos"),

        ("subscriptions_create", "Pode criar Assinaturas"),
        ("subscriptions_view", "Pode visualizar Assinaturas"),
        ("subscriptions_edit", "Pode editar Assinaturas"),
        ("subscriptions_delete", "Pode deletar Assinaturas"),

        ("financial_create", "Pode criar em Finanças"),
        ("financial_view", "Pode visualizar Finanças"),
        ("financial_edit", "Pode editar Finanças"),
        ("financial_delete", "Pode deletar em Finanças"),

        ("reports_create", "Pode criar Relatórios"),
        ("reports_view", "Pode visualizar Relatórios"),
        ("reports_edit", "Pode editar Relatórios"),
        ("reports_delete", "Pode deletar Relatórios"),

        ("collaborators_create", "Pode convidar Colaboradores"),
        ("collaborators_view", "Pode visualizar Colaboradores"),
        ("collaborators_edit", "Pode editar Colaboradores"),
        ("collaborators_delete", "Pode remover Colaboradores"),

        ("apps_create", "Pode criar Apps"),
        ("apps_view", "Pode visualizar Apps"),
        ("apps_edit", "Pode editar Apps"),
        ("apps_delete", "Pode deletar Apps"),

        ("referrals_create", "Pode criar em Programa de Indicações"),
        ("referrals_view", "Pode visualizar o Programa de Indicações"),
        ("referrals_edit", "Pode editar em Programa de Indicações"),
        ("referrals_delete", "Pode deletar em Programa de Indicações"),
    ]

    owner = models.ForeignKey(User, on_delete=models.CASCADE, related_name='colaborator_owner')
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='colaborators')
    permissions = models.JSONField(default=list, verbose_name="Permissões")
    status = models.CharField(
        max_length=255,
        verbose_name='Status',
        choices=(
            ('pending', 'Pendente'),
            ('approved', 'Aprovado'),
            ('rejected', 'Rejeitado')
        ),
        default='pending'
    )
    createdAt = models.DateTimeField(auto_now_add=True, verbose_name='Criado em')

    def __str__(self):
        return self.user.email

    def has_permission(self, permission: str):
        return permission in self.permissions

class Rank(models.Model):
    class Meta:
        verbose_name = 'Rank'
        verbose_name_plural = 'Ranks'
        ordering = ['-min_value']

    code = models.CharField(max_length=100, verbose_name='Código')
    label = models.CharField(max_length=255, verbose_name='Rótulo')
    min_value = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='Valor mínimo')
    max_value = models.DecimalField(
        max_digits=10, decimal_places=2, verbose_name='Valor máximo', null=True, blank=True
    )
    status = models.CharField(
        max_length=255,
        verbose_name='Status',
        choices=(
            ('active', 'Ativo'),
            ('deleted', 'Deletado'),
        ),
        default='active'
    )
    createdAt = models.DateTimeField(auto_now_add=True, verbose_name='Criado em')
    updatedAt = models.DateTimeField(auto_now=True, verbose_name='Atualizado em')

    def __str__(self):
        return str(self.code)

class Award(models.Model):
    class Meta:
        verbose_name = 'Premiação'
        verbose_name_plural = 'Premiações'
        ordering = ['-target_sales']

    rank = models.ForeignKey(
        Rank, on_delete=models.SET_NULL, verbose_name='Rank', null=True, blank=True,
        related_name='awards'
        )
    target_sales = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        verbose_name='Valor das vendas'
    )
    title = models.CharField(max_length=255, verbose_name='Título')
    description = models.TextField(verbose_name='Descrição', blank=True, null=True)
    status = models.CharField(
        max_length=255,
        verbose_name='Status',
        choices=(
            ('active', 'Ativo'),
            ('deleted', 'Deletado'),
        ),
        default='active'
    )

    createdAt = models.DateTimeField(auto_now_add=True, verbose_name='Criado em')
    updatedAt = models.DateTimeField(auto_now=True, verbose_name='Atualizado em')

    def __str__(self):
        return self.title

    def delete(self, *args, **kwargs):
        self.status = 'deleted'
        self.save()

class UserAward(models.Model):
    class Meta:
        verbose_name = 'Premiação do Usuário'
        verbose_name_plural = 'Premiações dos Usuários'
        ordering = ['-createdAt']

    user = models.ForeignKey('user.User', on_delete=models.CASCADE, related_name='awards')
    award = models.ForeignKey('user.Award', on_delete=models.CASCADE, related_name='users')
    status = models.CharField(
        max_length=255,
        verbose_name='Status',
        choices=(
            ('pending', 'Pendente'),
            ('sent', 'Enviado'),
            ('received', 'Recebido'),
        ),
        default='pending'
    )

    # Shipping data
    tracking_code = models.CharField(
        max_length=255,
        verbose_name='Código de rastreio',
        blank=True,
        null=True
    )
    shipping_method = models.CharField(
        max_length=255,
        verbose_name='Método de envio',
        blank=True,
        null=True
    )

    # Address data
    cep = models.CharField(max_length=8, null=True, blank=True)
    street = models.CharField(max_length=255, null=True, blank=True)
    number = models.CharField(max_length=255, null=True, blank=True)
    complement = models.CharField(max_length=255, null=True, blank=True)
    neighborhood = models.CharField(max_length=255, null=True, blank=True)
    city = models.CharField(max_length=255, null=True, blank=True)
    state = models.CharField(max_length=2, null=True, blank=True)

    # Dates
    createdAt = models.DateTimeField(auto_now_add=True, verbose_name='Criado em')
    updatedAt = models.DateTimeField(auto_now=True, verbose_name='Atualizado em')

    def __str__(self):
        return f'{self.user.email} - {self.award.title}'

class PushNotification(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='push_notifications')
    token = models.CharField(max_length=255, verbose_name='Token')
    os = models.CharField(max_length=255, verbose_name='Sistema operacional')
    createdAt = models.DateTimeField(auto_now_add=True, verbose_name='Criado em')

    def __str__(self):
        return self.token

class AppCaktoDevice(TOTPDevice):
    """
    Custom TOTP Device for APP Cakto that allow the user to have two totp devices.
    """

class MFAMethod(models.Model):
    user = models.ForeignKey(
        'user.User',
        on_delete=models.CASCADE,
        related_name="mfa_methods"
    )
    type = models.CharField(choices=MFAType.choices(), max_length=255)
    primary = models.BooleanField(default=False)
    confirmed = models.BooleanField(default=False)
    email_device = models.OneToOneField(
        EmailDevice,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )
    totp_device = models.OneToOneField(
        TOTPDevice,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )
    app_cakto_device = models.OneToOneField(
        AppCaktoDevice,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='app_cakto_device'
    )
    fcm_device = models.ForeignKey(
        FCMDevice,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='mfa_methods'
    )

    createdAt = models.DateTimeField(auto_now_add=True, verbose_name='Criado em', help_text='Data de criação')
    updatedAt = models.DateTimeField(auto_now=True, verbose_name='Atualizado em', help_text='Data de atualização')

    class Meta:
        verbose_name = ("MFA Method")
        verbose_name_plural = ("MFA Methods")
        ordering = ('-primary',)
        constraints = (
            models.UniqueConstraint(
                fields=("user", "type"),
                name="unique_user_method_type",
            ),
            models.UniqueConstraint(
                fields=("user",),
                condition=Q(primary=True),
                name="unique_user_primary",
            ),
        )

    def __str__(self) -> str:
        return f"{self.type} (User {self.user.pk})"

    def save(self, *args, **kwargs) -> None:
        devices = sum([bool(self.email_device), bool(self.totp_device), bool(self.app_cakto_device)])
        if devices > 1:
            raise ValueError("MFA Method cannot have more than one devices")
        super().save(*args, **kwargs)

    def delete(self, *args, **kwargs) -> None:
        super().delete(*args, **kwargs)
        if self.device:
            self.device.delete()

    @classmethod
    def user_has_primary_method(cls, user: User) -> bool:
        return cls.objects.filter(user=user, primary=True).exists()

    @classmethod
    def user_has_confirmed_mfa(cls, user: User) -> bool:
        return cls.objects.filter(user=user, confirmed=True).exists()

    @classmethod
    @transaction.atomic
    def get_email_method(cls, user: User, confirmed: bool | None = None, primary=True) -> 'MFAMethod':
        mfa_method, mfa_created = MFAMethod.objects.get_or_create(user=user, type=MFAType.EMAIL.id)
        email_device, _ = EmailDevice.objects.get_or_create(
            user=user,
            defaults={'name': f'Email - {user.email[:50]}'}
        )

        if confirmed is True:
            mfa_method.confirmed = confirmed
            email_device.confirmed = confirmed

        mfa_method.email_device = email_device
        mfa_method.primary = primary if mfa_created and not cls.user_has_primary_method(user) else mfa_method.primary
        mfa_method.save(update_fields=['email_device', 'primary', 'confirmed'])
        email_device.save(update_fields=['confirmed'])

        return mfa_method

    @classmethod
    @transaction.atomic
    def get_totp_method(cls, user: User) -> 'MFAMethod':
        mfa_method, mfa_created = MFAMethod.objects.get_or_create(user=user, type=MFAType.TOTP.id)
        totp_device, _ = TOTPDevice.objects.get_or_create(
            user=user,
            defaults={'name': f'TOTP - {user.email[:50]}'}
        )

        mfa_method.totp_device = totp_device
        mfa_method.primary = True if mfa_created and not cls.user_has_primary_method(user) else mfa_method.primary
        mfa_method.save(update_fields=['totp_device', 'primary'])

        return mfa_method

    @classmethod
    @transaction.atomic
    def get_app_cakto_method(cls, user: User) -> 'MFAMethod':
        mfa_method, mfa_created = MFAMethod.objects.get_or_create(user=user, type=MFAType.APP_CAKTO.id)
        app_cakto_device, _ = AppCaktoDevice.objects.get_or_create(
            user=user,
            defaults={'name': f'APP Cakto - {user.email[:50]}'}
        )

        mfa_method.app_cakto_device = app_cakto_device
        mfa_method.primary = True if mfa_created and not cls.user_has_primary_method(user) else mfa_method.primary
        mfa_method.save(update_fields=['app_cakto_device', 'primary'])

        return mfa_method

    @classmethod
    def verify_user_token(cls, user: User, code: str) -> bool:
        mfa_methods = MFAMethod.objects.filter(user=user, confirmed=True)
        for mfa_method in mfa_methods:
            if mfa_method.verify_token(code):
                return True
        return False

    @property
    def device(self) -> EmailDevice | TOTPDevice | AppCaktoDevice:
        if self.email_device:
            return self.email_device
        elif self.totp_device:
            return self.totp_device
        elif self.app_cakto_device:
            return self.app_cakto_device

        raise ValueError("No device found")

    def send_token(self) -> bool:
        self.check_verify_is_allowed()

        if self.email_device:
            return self.send_email_token()

        if self.app_cakto_device:
            return self.send_fcm_itoken_notification()

        return False

    def check_verify_is_allowed(self) -> None:
        verify_allowed, data = self.verify_is_allowed()

        if verify_allowed:
            return

        clear_at = data.get('locked_until')
        wait = clear_at.timestamp() - timezone.now().timestamp()  # type: ignore
        raise Throttled(wait=wait)

    def send_email_token(self, user: User | None = None, title: str | None = None, message: str | None = None) -> bool:
        from email_service.mail import send_otp_token_email

        if not self.email_device:
            return False

        title = title or 'Código de Verificação - Cakto'
        message = message or 'Este é o seu código de verificação:'
        user = user or self.user

        self.generate_token()

        email_sent = send_otp_token_email(user, title, message, self.email_device.token)

        return bool(email_sent)

    def send_fcm_itoken_notification(self) -> bool:
        from user.notification import send_device_notification

        if not self.app_cakto_device:
            raise Exception('Method allowed only for App Cakto devices')
        if not self.fcm_device:
            raise ValidationError('Dispositivo FCM não encontrado.')

        send_device_notification(
            self.user,
            self.fcm_device,
            'Ação Necessária',
            'Digite seu iToken no navegador para completar o login',
            data={'screen': 'ITOKEN'},
        )

        return True

    def generate_token(self) -> None:
        if self.email_device:
            return self.email_device.generate_token()

    def verify_is_allowed(self) -> tuple[bool, dict]:
        """
        Custom method most cloned from django_otp.models.py::ThrottlingMixin::verify_is_allowed
        to set the maximum throttle to MAX_MFA_THROTTLE_SECONDS.
        """
        device = self.device

        if (
            device.throttling_enabled
            and device.throttling_failure_count > 0
            and device.throttling_failure_timestamp is not None
        ):
            now = timezone.now()
            delay = (now - device.throttling_failure_timestamp).total_seconds()
            # Required delays should be 1, 2, 4, 8 ...
            delay_required = device.get_throttle_factor() * (
                2 ** (device.throttling_failure_count - 1)
            )

            # Set the maximum throttle
            delay_required = min(delay_required, settings.MAX_MFA_THROTTLE_SECONDS)

            if delay < delay_required:
                return (
                    False,
                    {
                        'reason': VerifyNotAllowed.N_FAILED_ATTEMPTS,
                        'failure_count': device.throttling_failure_count,
                        'locked_until': device.throttling_failure_timestamp
                        + timezone.timedelta(seconds=delay_required),
                    },
                )

        return True, {}

    def verify_token(self, code: str) -> bool:
        if self.email_device:
            verified = self.verify_email_token(code)
        elif self.totp_device:
            verified = self.verify_totp_token(code)
        elif self.app_cakto_device:
            verified = self.verify_cakto_app_token(code)
        else:
            raise ValueError("No device found")

        if verified and (self.email_device or self.app_cakto_device):
            self.reset_send_code_trottle()

        return verified

    def reset_send_code_trottle(self) -> None:
        cache_key = UserRateThrottle.cache_format % {
            'scope': 'MFASendToken',
            'ident': self.user.pk
        }
        cache.delete(cache_key)

    def get_totp_config_url(self) -> str:
        if self.totp_device:
            return self.totp_device.config_url
        elif self.app_cakto_device:
            return self.app_cakto_device.config_url

        raise ValueError("No TOTP device found")

    def verify_totp_token(self, token: str) -> bool:
        """
        Customized method from django_otp.plugins.otp_totp.models.py::TOTPDevice::verify_token.

        Changes made:
            - Use this class verify_is_allowed method wich is also customized intead of the parent class.
        """

        device = self.totp_device
        if not device:
            raise ValueError("No TOTP device found")

        OTP_TOTP_SYNC = getattr(settings, 'OTP_TOTP_SYNC', True)

        verify_allowed, _ = self.verify_is_allowed()
        if not verify_allowed:
            return False

        try:
            token = int(token)  # type: ignore
        except Exception:
            verified = False
        else:
            key = device.bin_key

            totp = TOTP(key, device.step, device.t0, device.digits, device.drift)
            totp.time = time.time()

            verified = totp.verify(token, device.tolerance, device.last_t + 1)
            if verified:
                device.last_t = totp.t()
                if OTP_TOTP_SYNC:
                    device.drift = totp.drift
                device.throttle_reset(commit=False)
                device.set_last_used_timestamp(commit=False)
                device.save()

        if not verified:
            device.throttle_increment(commit=True)

        return verified

    def verify_cakto_app_token(self, token: str) -> bool:
        """
        Method cloned from django_otp.plugins.otp_totp.models.py::TOTPDevice::verify_token.

        Changes made:
            - Use this class verify_is_allowed method wich is also customized intead of the parent class.
            - Removed the sync that where trougth the OTP_TOTP_SYNC config.
            - Removed the drift verification.
            - Changed the last_t(last token time verified correctly) verification to accept any token
            that is equals or higher than the last_t.
                The parent class only accepts tokens that are higher than the last_t.
        """
        device = self.app_cakto_device
        if not device:
            raise ValueError("No App Cakto device found")

        # OTP_TOTP_SYNC = getattr(settings, 'OTP_TOTP_SYNC', True)

        verify_allowed, _ = self.verify_is_allowed()
        if not verify_allowed:
            return False

        try:
            token = int(token)  # type: ignore
        except Exception:
            verified = False
        else:
            key = device.bin_key

            totp = TOTP(key, device.step, device.t0, device.digits, drift=0)  # changed the drift arg to 0
            totp.time = time.time()

            # changed from device.last_t + 1 to device.last_t
            verified = totp.verify(token, device.tolerance, device.last_t)
            if verified:
                device.last_t = totp.t()
                # if OTP_TOTP_SYNC:
                #     device.drift = totp.drift
                device.throttle_reset(commit=False)
                device.set_last_used_timestamp(commit=False)
                device.save()

        if not verified:
            device.throttle_increment(commit=True)

        return verified

    def verify_email_token(self, token: str) -> bool:
        """
        Customized method from django_otp.plugins.otp_email.models.py::EmailDevice::verify_token.

        Changes made:
            - Use this class verify_is_allowed method wich is also customized intead of the parent class.
        """
        device = self.email_device
        if not device:
            raise ValueError("No Email device found")

        verify_allowed, _ = self.verify_is_allowed()
        if verify_allowed:
            verified = super(EmailDevice, device).verify_token(token)

            if verified:
                device.throttle_reset(commit=False)
                device.set_last_used_timestamp(commit=False)
                device.save()
            else:
                device.throttle_increment()
        else:
            verified = False

        return verified

def is_valid_nps_note(note: int) -> bool:
    return note in [1, 2, 3, 4, 5]

def _validate_nps_note(note: int):
    if not is_valid_nps_note(note):
        raise ValidationError(f'Invalid value: {note}')

class NPSSurvey(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    note = models.PositiveSmallIntegerField(validators=[_validate_nps_note])
    justification = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = "Net Promoter Score"
        verbose_name_plural = "Net Promoter Scores"
        ordering = ['-created_at']

    def __str__(self):
        return f"NPS {self.score} em {self.date}"

class CRMApiHistory(models.Model):
    order = models.ForeignKey('gateway.Order', on_delete=models.CASCADE, related_name='crm_histories', blank=True, null=True)
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='crm_histories', blank=True, null=True)

    # Request data
    action = models.CharField(max_length=255)
    payload = models.JSONField()
    response = models.JSONField()
    response_status_code = models.IntegerField()
    createdAt = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-createdAt']
        verbose_name = 'Histórico CRM'
        verbose_name_plural = 'Históricos CRM'

    def __str__(self):
        return f'{self.action} - {self.createdAt}'

class RecoveryTokenHistory(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='recovery_token_histories', verbose_name='Usuário')
    token = models.CharField(max_length=255, verbose_name='Token')
    token_expires_at = models.DateTimeField(verbose_name='Data de expiração do Token')
    event_type = models.CharField(max_length=255, verbose_name='Tipo do evento', choices=(
        ('created', 'Token Solicitado'),
        ('used', 'Token Usado'),
    ))
    requester_ip_address = models.GenericIPAddressField(verbose_name='IP do solicitante')
    requester_user_agent = models.TextField(verbose_name='User Agent do solicitante')
    timestamp = models.DateTimeField(auto_now_add=True, verbose_name='Data do evento')
    updatedAt = models.DateTimeField(auto_now=True, verbose_name='Última atualização')  # Just for make shure that the register isn't updated

    class Meta:
        verbose_name = 'Histórico de Tokens de Recuperação'
        verbose_name_plural = 'Históricos de Tokens de Recuperação'
        ordering = ['-timestamp']

class ExperimentalFeature(models.Model):
    class Meta:
        verbose_name = 'Função Experimental'
        verbose_name_plural = 'Funções Experimentais'
        ordering = ['name']

    id = models.CharField(
        verbose_name='Identificador',
        help_text='Identificador da funcionalidade no sistema, ex.: "openfinance_nubank", "membersV2", etc.',
        max_length=80,
        primary_key=True,
        unique=True,
        db_index=True,
    )
    name = models.CharField(
        verbose_name='Nome',
        help_text='Nome descritivo da funcionalidade no sistema, ex.: "Método de pagamento Nubank", "Área de membros v2", etc.',
        max_length=255,
        db_index=True,
    )

    def __str__(self):
        return f'{self.name} ({self.id})'

class UserLoginHistory(models.Model):
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='producer_histories',
    )
    ip = models.CharField(
        max_length=255,
        blank=True,
        null=True,
        db_index=True,
        help_text='IP do usuário no momento do registro',
    )
    user_agent = models.TextField(
        blank=True,
        null=True,
        verbose_name='User Agent',
        help_text='Informações do navegador do usuário no momento do registro',
    )
    access_token_source = models.CharField(
        choices=UserAccessLocations.choices(),
        max_length=255,
        blank=True,
        db_index=True,
        verbose_name='Local da API',
        help_text='Parte do código que gerou o token de acesso, ex.: "login", "purchase_email", "refund_otp_view", etc.',
    )
    createdAt = models.DateTimeField(auto_now_add=True, db_index=True)

    class Meta:
        verbose_name = 'Histórico de Login de Usuário'
        verbose_name_plural = 'Histórico de Login de Usuários'
        ordering = ['-createdAt']

    def __str__(self):
        return f'{self.user.email} - {self.createdAt.strftime("%Y-%m-%d %H:%M:%S")}'
