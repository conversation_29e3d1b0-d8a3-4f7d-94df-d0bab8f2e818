from django.test import override_settings
import logging
from unittest import mock

from django.urls import reverse
from django.utils import timezone

from cakto.tests.base import BaseTestCase
from email_service.mail import get_customer_name
from email_service.models import EmailLog
from user.enums import UserAccessLocations
from user.models import UserLoginHistory


class TestCustomerEmailLogin(BaseTestCase):
    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()

        cls.user.is_customer = True
        cls.user.is_producer = False
        cls.user.save(update_fields=['is_customer', 'is_producer'])

        cls.url = reverse('user:request-login-link-email')

    def test_request_login_link_email(self):
        payload = {'email': self.user.email}

        expected_email_data = {
            'userName': get_customer_name(user=self.user),
            'accessUrl': mock.ANY,
        }

        mock.patch('email_service.mail.send_mail', return_value=1).start()

        brand_name = "new_brand"
        brand_readable = "New Brand"

        with override_settings(BRAND=brand_name):
            result = self.client.post(self.url, data=payload, format='json')

        self.assertEqual(result.status_code, 200, result.content.decode())
        self.assertEqual(EmailLog.objects.count(), 1, 'Email log should be created')

        email_log: EmailLog = EmailLog.objects.first()  # type:ignore
        self.assertEqual(email_log.subject, f'Link de Acesso - {brand_readable}')
        self.assertEqual(email_log.message, f'Link de Acesso - {brand_readable}')
        self.assertEqual(email_log.to, self.user.email)
        self.assertEqual(email_log.template_name, 'customer_login_link_email.html')
        self.assertEqual(email_log.data, expected_email_data)
        self.assertEqual(email_log.is_sent, True)

    def test_request_login_link_email_raises_error_when_email_not_sended(self):
        payload = {'email': self.user.email}

        mock.patch('email_service.mail.send_mail', return_value=0).start()

        self.logger = logging.getLogger('django.request')
        previous_logging_level = self.logger.getEffectiveLevel()
        self.logger.setLevel(logging.CRITICAL)

        with self.assertRaises(Exception, msg='Error sending Login Link Email'):
            self.client.post(self.url, data=payload, format='json')

        self.logger.setLevel(previous_logging_level)

    def test_request_login_link_email_returns_400_when_user_is_produces(self):
        self.user.is_producer = True
        self.user.save(update_fields=['is_producer'])

        payload = {'email': self.user.email}

        result = self.client.post(self.url, data=payload, format='json')

        self.assertEqual(result.status_code, 400, result.content.decode())
        self.assertEqual(result.json()['detail'], 'Usuário com vendas deve realizar login como "Produtor".')

    def test_request_login_link_email_throttle(self):
        payload = {'email': self.user.email}

        result_1 = self.client.post(self.url, data=payload, format='json')
        result_2 = self.client.post(self.url, data=payload, format='json')

        self.assertEqual(result_1.status_code, 200, result_1.content.decode())
        self.assertEqual(result_2.status_code, 429, result_1.content.decode())

    def test_request_login_link_email_creates_user_login_history(self):
        UserLoginHistory.objects.all().delete()  # Clear existing login history

        mock.patch('email_service.mail.send_mail', return_value=1).start()

        payload = {'email': self.user.email}

        ip = '*********'
        user_agent = 'TestUserAgent'

        result = self.client.post(
            self.url,
            data=payload,
            format='json',
            REMOTE_ADDR=ip,
            HTTP_USER_AGENT=user_agent,
        )

        self.assertEqual(result.status_code, 200, result.content.decode())

        self.assertEqual(UserLoginHistory.objects.count(), 1, "User login history should have one entry")

        history: UserLoginHistory = UserLoginHistory.objects.first()  # type:ignore

        self.assertEqual(history.user, self.user)
        self.assertEqual(history.ip, ip)
        self.assertEqual(history.user_agent, user_agent)
        self.assertEqual(
            history.access_token_source,
            UserAccessLocations.LOGIN_EMAIL.id
        )
        self.assertAlmostEqual(
            history.createdAt,
            timezone.now(),
            delta=timezone.timedelta(seconds=2)
        )
