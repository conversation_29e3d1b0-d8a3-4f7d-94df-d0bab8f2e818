import json
from decimal import Decimal

from cakto.tests.base import BaseTestCase
from user.models import Award, Rank
from user.utils import create_user_awards


class TestRankFeature(BaseTestCase):
    def test_rank_request_user_must_be_authenticated(self):
        respose = self.client.get("/api/ranks/")
        self.assertEqual(respose.status_code, 403)

    def test_rank_request_must_return_200_and_list_results(self):
        self.setUpTestData()

        response = self.client.get("/api/ranks/", headers=self.build_user_auth_headers())

        payload = json.loads(response.content)
        response_results = payload['results']

        self.assertEqual(response.status_code, 200)
        self.assertIsInstance(response_results, list)

    def test_rank_request_must_return_list_of_ranks(self):
        self.setUpTestData()

        instance1 = Rank.objects.create(
            code="beginner", label="Início", min_value=0, max_value=10000
            )
        instance2 = Rank.objects.create(
            code="mirage", label="Mirage", min_value=10000, max_value=100000
            )

        expected_result = [
            {
                "code": instance2.code,
                "label": instance2.label,
                "min_value": f"{float(instance2.min_value):.2f}",
                "max_value": f"{float(instance2.max_value):.2f}" if instance2.max_value else None,
            },
            {
                "code": instance1.code,
                "label": instance1.label,
                "min_value": f"{float(instance1.min_value):.2f}",
                "max_value": f"{float(instance1.max_value):.2f}" if instance1.max_value else None,
            },
        ]

        response = self.client.get("/api/ranks/", headers=self.build_user_auth_headers())

        payload = json.loads(response.content)
        response_results = payload['results']

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response_results, expected_result)

    def test_get_user_endpoint_must_return_target_rank(self):
        self.setUpTestData()

        response = self.client.get("/api/user/", headers=self.build_user_auth_headers())

        payload = json.loads(response.content)
        response_results = payload.keys()

        self.assertIn('target_rank', response_results)

    def test_user_target_rank_must_match_with_next_rank(self):
        self.setUpTestData()

        _headers = self.build_user_auth_headers()

        Rank.objects.create(
            code="beginner", label="Início", min_value=0, max_value=10000
            )
        Rank.objects.create(
            code="mirage", label="Mirage", min_value=10000, max_value=100000
            )

        response = self.client.get("/api/user/", headers=_headers)

        payload = json.loads(response.content)
        response_result = payload['target_rank']

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response_result, 'mirage')

    def test_user_next_award_must_match_with_next_rank_award(self):
        self.setUpTestData()

        _headers = self.build_user_auth_headers()

        Rank.objects.create(
            code="beginner", label="Início", min_value=0, max_value=10000
            )
        next_rank = Rank.objects.create(
            code="mirage", label="Mirage", min_value=10000, max_value=100000
            )

        Award.objects.create(
            rank=next_rank,
            title="Mirage Award",
            target_sales=10000
            )

        response = self.client.get("/api/user/", headers=_headers)

        payload = json.loads(response.content)
        response_result = payload['nextAward']

        next_award = next_rank.awards.first()

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response_result['title'], next_award.title)

    def test_user_awards_must_be_created_by_rank_excluding_already_achieved(self):
        self.setUpTestData()

        _headers = self.build_user_auth_headers()

        Rank.objects.create(
            code="beginner", label="Início", min_value=0, max_value=10000
            )
        rank_achieved = Rank.objects.create(
            code="mirage", label="Mirage", min_value=10000, max_value=100000
            )

        Award.objects.create(
            rank=rank_achieved,
            title="Mirage Award",
            target_sales=10000
            )

        product = self.create_product(price=Decimal(11000), user=self.user)
        self.create_order(product=product, status="paid", amount=Decimal(11000), baseAmount=Decimal(11000))

        self.assertEqual(self.user.totalSales(), Decimal(11000))

        response = create_user_awards(self.user)
        first_award = response[0]

        self.assertIsInstance(response, list)
        self.assertEqual(first_award.user, self.user)
        self.assertEqual(first_award.award, rank_achieved.awards.first())

        response = create_user_awards(self.user)
        self.assertEqual(len(response), 0)
