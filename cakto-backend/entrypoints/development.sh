#!/bin/bash

SCRIPT_DIR="$(dirname "$(readlink -f "$0")")"
source "${SCRIPT_DIR}/shared_functions.sh"

# Check if DJANGO_SETTINGS_MODULE is set
if [ -z "${DJANGO_SETTINGS_MODULE}" ]; then
    echo "DJANGO_SETTINGS_MODULE environment variable is not set"
    exit 1
fi

echo "!!! WARNING !!! Development script detected. This should not be used in production."
echo "DJANGO_SETTINGS_MODULE = ${DJANGO_SETTINGS_MODULE}"

if [ ${INSTALL_DEV_DEPS} ]; then
    install_dev_apps
fi

# Create S3 bucket.
awslocal s3api create-bucket --bucket "${AWS_STORAGE_BUCKET_NAME}"

# Install dependencies
poetry install

# exec migration
poetry run python manage.py migrate --no-input

# start app
if [ ${START_DEV_APP} ]; then
    start_backend_apps_dev
fi