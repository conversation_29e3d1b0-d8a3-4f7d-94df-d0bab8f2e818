aiohttp==3.9.3 ; python_version == "3.11"
aiosignal==1.3.1 ; python_version == "3.11"
annotated-types==0.7.0 ; python_version == "3.11"
anyio==4.4.0 ; python_version == "3.11"
apscheduler==3.10.4 ; python_version == "3.11"
asgiref==3.7.2 ; python_version == "3.11"
async-timeout==4.0.3 ; python_version == "3.11"
attrs==23.1.0 ; python_version == "3.11"
autobahn==23.6.2 ; python_version == "3.11"
automat==22.10.0 ; python_version == "3.11"
babel==2.14.0 ; python_version == "3.11"
boto3==1.33.9 ; python_version == "3.11"
botocore==1.33.9 ; python_version == "3.11"
cachecontrol==0.13.1 ; python_version == "3.11"
cachetools==5.3.2 ; python_version == "3.11"
certifi==2023.7.22 ; python_version == "3.11"
cffi==1.15.1 ; python_version == "3.11"
charset-normalizer==3.2.0 ; python_version == "3.11"
click==8.1.7 ; python_version == "3.11"
colorama==0.4.6 ; python_version == "3.11"
constantly==15.1.0 ; python_version == "3.11"
crontab==1.0.1 ; python_version == "3.11"
cryptography==41.0.4 ; python_version == "3.11"
curlify==2.2.1 ; python_version == "3.11"
daphne==4.0.0 ; python_version == "3.11"
defusedxml==0.7.1 ; python_version == "3.11"
deprecated==1.2.14 ; python_version == "3.11"
dj-rest-auth==5.0.1 ; python_version == "3.11"
django-allauth==0.57.0 ; python_version == "3.11"
django-apscheduler==0.6.2 ; python_version == "3.11"
django-cors-headers==4.2.0 ; python_version == "3.11"
django-debug-toolbar==4.3.0 ; python_version == "3.11"
django-filter==24.2 ; python_version == "3.11"
django-lifecycle==1.2.4 ; python_version == "3.11"
django-ninja==1.4.3 ; python_version == "3.11"
django-oauth-toolkit==3.0.1 ; python_version == "3.11"
django-otp==1.5.4 ; python_version == "3.11"
django-phonenumber-field==8.1.0 ; python_version == "3.11"
django-prometheus==2.3.1 ; python_version == "3.11"
django-redis==5.4.0 ; python_version == "3.11"
django-route-decorator==0.1.1 ; python_version == "3.11"
django-rq==2.10.1 ; python_version == "3.11"
django-storages==1.14.2 ; python_version == "3.11"
django-waffle==5.0.0 ; python_version == "3.11"
django==4.2.5 ; python_version == "3.11"
djangorestframework-csv==2.1.1 ; python_version == "3.11"
djangorestframework-simplejwt==5.3.0 ; python_version == "3.11"
djangorestframework==3.14.0 ; python_version == "3.11"
dnspython==2.6.1 ; python_version == "3.11"
drf-excel==2.4.0 ; python_version == "3.11"
drf-flex-fields==1.0.2 ; python_version == "3.11"
email-validator==2.2.0 ; python_version == "3.11"
enum34==1.1.10 ; python_version == "3.11"
et-xmlfile==1.1.0 ; python_version == "3.11"
facebook-business==19.0.0 ; python_version == "3.11"
fakeredis==2.23.2 ; python_version == "3.11"
fastapi-cli==0.0.4 ; python_version == "3.11"
fastapi==0.111.0 ; python_version == "3.11"
fcm-django==2.2.1 ; python_version == "3.11"
firebase-admin==6.2.0 ; python_version == "3.11"
freezegun==1.4.0 ; python_version == "3.11"
frozenlist==1.4.1 ; python_version == "3.11"
google-api-core==2.13.0 ; python_version == "3.11"
google-api-python-client==2.107.0 ; python_version == "3.11"
google-auth-httplib2==0.1.1 ; python_version == "3.11"
google-auth==2.23.4 ; python_version == "3.11"
google-cloud-core==2.3.3 ; python_version == "3.11"
google-cloud-firestore==2.13.1 ; python_version == "3.11"
google-cloud-recaptcha-enterprise==1.20.0 ; python_version == "3.11"
google-cloud-storage==2.13.0 ; python_version == "3.11"
google-crc32c==1.5.0 ; python_version == "3.11"
google-resumable-media==2.6.0 ; python_version == "3.11"
googleapis-common-protos==1.61.0 ; python_version == "3.11"
grpcio-status==1.59.2 ; python_version == "3.11"
grpcio==1.59.2 ; python_version == "3.11"
gunicorn==21.2.0 ; python_version == "3.11"
h11==0.14.0 ; python_version == "3.11"
httpcore==1.0.5 ; python_version == "3.11"
httplib2==0.22.0 ; python_version == "3.11"
httptools==0.6.1 ; python_version == "3.11"
httpx==0.27.0 ; python_version == "3.11"
hyperlink==21.0.0 ; python_version == "3.11"
idna==3.4 ; python_version == "3.11"
importlib-resources==6.4.0 ; python_version == "3.11"
incremental==22.10.0 ; python_version == "3.11"
iniconfig==2.0.0 ; python_version == "3.11"
jinja2==3.1.4 ; python_version == "3.11"
jmespath==1.0.1 ; python_version == "3.11"
jwcrypto==1.5.6 ; python_version == "3.11"
limits==3.13.0 ; python_version == "3.11"
markdown-it-py==3.0.0 ; python_version == "3.11"
markupsafe==2.1.5 ; python_version == "3.11"
mdurl==0.1.2 ; python_version == "3.11"
msgpack==1.0.7 ; python_version == "3.11"
multidict==6.0.5 ; python_version == "3.11"
oauthlib==3.2.2 ; python_version == "3.11"
openpyxl==3.1.2 ; python_version == "3.11"
orjson==3.10.5 ; python_version == "3.11"
packaging==23.1 ; python_version == "3.11"
phonenumbers==9.0.10 ; python_version == "3.11"
pillow==10.0.1 ; python_version == "3.11"
prometheus-client==0.20.0 ; python_version == "3.11"
proto-plus==1.22.3 ; python_version == "3.11"
protobuf==4.25.0 ; python_version == "3.11"
psycopg2==2.9.7 ; python_version == "3.11"
pyasn1-modules==0.3.0 ; python_version == "3.11"
pyasn1==0.5.0 ; python_version == "3.11"
pycountry==23.12.11 ; python_version == "3.11"
pycparser==2.21 ; python_version == "3.11"
pydantic-core==2.18.4 ; python_version == "3.11"
pydantic==2.7.4 ; python_version == "3.11"
pygments==2.18.0 ; python_version == "3.11"
pyjwt==2.8.0 ; python_version == "3.11"
pyopenssl==23.2.0 ; python_version == "3.11"
pyparsing==3.1.1 ; python_version == "3.11"
pypng==0.20220715.0 ; python_version == "3.11"
python-dateutil==2.8.2 ; python_version == "3.11"
python-dotenv==1.0.0 ; python_version == "3.11"
python-multipart==0.0.9 ; python_version == "3.11"
python-telegram-bot==21.6 ; python_version == "3.11"
python3-openid==3.2.0 ; python_version == "3.11"
pytz==2023.3.post1 ; python_version == "3.11"
pyyaml==6.0.1 ; python_version == "3.11"
qrcode==7.4.2 ; python_version == "3.11"
redis==5.0.1 ; python_version == "3.11"
requests-oauthlib==1.3.1 ; python_version == "3.11"
requests==2.31.0 ; python_version == "3.11"
responses==0.25.3 ; python_version == "3.11"
rich==13.7.1 ; python_version == "3.11"
rq-exporter==2.1.2 ; python_version == "3.11"
rq-scheduler==0.13.1 ; python_version == "3.11"
rq==1.15.1 ; python_version == "3.11"
rsa==4.9 ; python_version == "3.11"
s3transfer==0.8.2 ; python_version == "3.11"
service-identity==23.1.0 ; python_version == "3.11"
setuptools==70.1.1 ; python_version == "3.11"
shellingham==1.5.4 ; python_version == "3.11"
shortuuid==1.0.11 ; python_version == "3.11"
six==1.16.0 ; python_version == "3.11"
slowapi==0.1.9 ; python_version == "3.11"
sniffio==1.3.1 ; python_version == "3.11"
sortedcontainers==2.4.0 ; python_version == "3.11"
sqlparse==0.4.4 ; python_version == "3.11"
starlette==0.37.2 ; python_version == "3.11"
swapper==1.4.0 ; python_version == "3.11"
twisted-iocpsupport==1.0.4 ; python_version == "3.11" and platform_system == "Windows"
twisted==23.8.0 ; python_version == "3.11"
txaio==23.1.1 ; python_version == "3.11"
typer==0.12.3 ; python_version == "3.11"
types-requests==2.31.0.10 ; python_version == "3.11"
typing-extensions==4.8.0 ; python_version == "3.11"
tzdata==2023.3 ; python_version == "3.11"
tzlocal==5.2 ; python_version == "3.11"
ujson==5.10.0 ; python_version == "3.11"
unicodecsv==0.14.1 ; python_version == "3.11"
uritemplate==4.1.1 ; python_version == "3.11"
urllib3==2.0.5 ; python_version == "3.11"
uvicorn==0.23.2 ; python_version == "3.11"
uvloop==0.19.0 ; sys_platform != "win32" and sys_platform != "cygwin" and platform_python_implementation != "PyPy" and python_version == "3.11"
watchfiles==0.22.0 ; python_version == "3.11"
websockets==12.0 ; python_version == "3.11"
whitenoise==6.5.0 ; python_version == "3.11"
wrapt==1.16.0 ; python_version == "3.11"
yarl==1.9.4 ; python_version == "3.11"
zope-interface==6.0 ; python_version == "3.11"
