import datetime
from unittest import mock
from unittest.mock import patch

from django.utils import timezone

from cakto.tests.base import BaseTestCase
from financial.utils import (add_external_accesses, handle_approval, handle_chargeback, handle_med, handle_new_subscription,
                             handle_purchase_approved_notification, handle_refund, handle_subscription_renewed,
                             remove_content_access_based_on_order)
from gateway.models import Payment, PaymentStatus
from product.enums import ProductType
from product.models import Checkout, ContentDelivery, DeliveryAccess, ProductDelivery


class TestFinancialUtils(BaseTestCase):
    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()
        cls.create_paymentMethods()

        cls.product = cls.create_product(user=cls.user)
        cls.offer = cls.product.offers.first()
        cls.offer.interval = 1
        cls.offer.intervalType = 'month'
        cls.offer.save(update_fields=['interval', 'intervalType'])

        cls.customer_user = cls.create_user()
        cls.customer = cls.create_customer(email=cls.customer_user.email)

        cls.order = cls.create_order(product=cls.product, customer=cls.customer)

        cls.payment = Payment.objects.create(
            amount=cls.order.amount,
            paymentMethod=cls.order.paymentMethod,
        )

        cls.telegram_delivery, _ = ContentDelivery.objects.get_or_create(type='telegram', name='Telegram')
        cls.productDelivery = ProductDelivery.objects.create(product=cls.product, contentDelivery=cls.telegram_delivery, name='Telegram', status='active')

        cls.deliveryAccess = DeliveryAccess.objects.create(
            order=cls.order,
            user=cls.customer_user,
            productDelivery=cls.productDelivery,
            status='active',
            expiresAt=timezone.datetime(timezone.now().year + 1, 1, 1, 0, 0, 0, tzinfo=datetime.timezone.utc),
        )

    def test_add_external_accesses_update_existent_deliveryAccess_expiresAt(self):
        old_expiry = self.deliveryAccess.expiresAt
        offer = self.deliveryAccess.order.offer

        offer.recurrence_period = 30
        offer.type = 'subscription'
        offer.save(update_fields=['recurrence_period', 'type'])

        add_external_accesses(self.order, self.customer_user)

        self.deliveryAccess.refresh_from_db()

        self.assertEqual(self.deliveryAccess.expiresAt, old_expiry + timezone.timedelta(days=offer.recurrence_period), 'expiresAt should be updated')  # type:ignore

    def test_add_external_access_update_existent_deliveryAccess_with_lifetime_offer(self):
        self.offer.intervalType = 'lifetime'
        self.offer.save(update_fields=['intervalType'])

        self.deliveryAccess.expiresAt = None
        self.deliveryAccess.save(update_fields=['expiresAt'])

        add_external_accesses(self.order, self.customer_user)

        self.deliveryAccess.refresh_from_db()

        self.assertIsNone(self.deliveryAccess.expiresAt, 'expiresAt should stay None')

    def test_add_external_access_update_existent_deliveryAccess_with_updated_offer_lifetime_offer(self):
        self.offer.intervalType = 'lifetime'
        self.offer.save(update_fields=['intervalType'])

        self.deliveryAccess.expiresAt = timezone.now() + timezone.timedelta(days=30)
        self.deliveryAccess.save(update_fields=['expiresAt'])

        add_external_accesses(self.order, self.customer_user)
        self.deliveryAccess.refresh_from_db()

        self.assertIsNone(self.deliveryAccess.expiresAt, 'expiresAt should be None')

    def test_add_external_access_create_default_deliveryAccess(self):
        DeliveryAccess.objects.all().delete()

        add_external_accesses(self.order, self.customer_user)

        deliveryAccess = self.productDelivery.accesses.filter(user=self.customer_user).first()

        self.assertIsNotNone(deliveryAccess, 'deliveryAccess should be created')
        self.assertEqual(deliveryAccess.status, 'waiting_config', 'status should be waiting_config')
        self.assertEqual(deliveryAccess.order, self.order, 'order should be the same')

    def test_add_external_access_create_default_deliveryAccess_when_one_is_expired(self):
        DeliveryAccess.objects.exclude(id=self.deliveryAccess.id).all().delete()
        self.deliveryAccess.status = 'expired'
        self.deliveryAccess.save(update_fields=['status'])

        add_external_accesses(self.order, self.customer_user)

        deliveryAccess = self.productDelivery.accesses.filter(user=self.customer_user).first()

        self.assertIsNotNone(deliveryAccess, 'deliveryAccess should be created')
        self.assertNotEqual(deliveryAccess, self.deliveryAccess, 'deliveryAccess should not be the same')
        self.assertEqual(deliveryAccess.status, 'waiting_config', 'status should be waiting_config')
        self.assertEqual(deliveryAccess.order, self.order, 'order should be the same')

    def test_add_external_access_create_default_deliveryAccess_when_one_is_canceled(self):
        DeliveryAccess.objects.exclude(id=self.deliveryAccess.id).all().delete()
        self.deliveryAccess.status = 'canceled'
        self.deliveryAccess.save(update_fields=['status'])

        add_external_accesses(self.order, self.customer_user)

        deliveryAccess = self.productDelivery.accesses.filter(user=self.customer_user).first()

        self.assertIsNotNone(deliveryAccess, 'deliveryAccess should be created')
        self.assertNotEqual(deliveryAccess, self.deliveryAccess, 'deliveryAccess should not be the same')
        self.assertEqual(deliveryAccess.status, 'waiting_config', 'status should be waiting_config')
        self.assertEqual(deliveryAccess.order, self.order, 'order should be the same')

    def test_handle_new_subscription_with_supported_paymentMethod(self):
        self.order.paymentMethod = self.pix
        self.order.save(update_fields=['paymentMethod'])

        create_subscription_mock = mock.patch(
            'gateway.services.payment_factory.PixStrategy.create_subscription'
        ).start()

        dispatch_event_mock = mock.patch('financial.utils.dispatch_app_events').start()

        payment_mock = mock.MagicMock()

        handle_new_subscription(
            order=self.order,
            payment=payment_mock,
            webhook_data={}
        )

        create_subscription_mock.assert_called_once_with(self.order)
        dispatch_event_mock.assert_called_once_with(
            'subscription_created',
            order=self.order,
            payment=payment_mock,
        )

    def test_handle_new_subscription_with_not_supported_paymentMethod(self):
        self.order.paymentMethod = self.googlepay
        self.order.save(update_fields=['paymentMethod'])

        with self.assertRaises(NotImplementedError):
            handle_new_subscription(
                order=self.order,
                payment=mock.MagicMock(),
                webhook_data={}
            )

    def test_handle_subscription_renewed_is_called_on_handle_approval(self):
        mock.patch('financial.utils.get_or_create_user').start()
        mock.patch('financial.utils.handle_purchase_course_access').start()
        mock.patch('financial.utils.handle_commissionedUsers_purchase_approval').start()
        mock.patch('financial.utils.handle_purchase_approved_notification').start()
        mock.patch('financial.utils.calculate_product_metrics.delay').start()
        mock.patch('financial.utils.send_crm_sale_event.delay').start()
        mock.patch('gateway.services.email_marketing_service.send_user_to_email_marketing').start()
        mock.patch('financial.utils.update_checkout_sales_count.delay').start()

        order_mock = mock.MagicMock(
            type=ProductType.SUBSCRIPTION.id,
            subscription=mock.MagicMock(externalId='123')
        )

        payment_mock = mock.MagicMock()

        handle_subscription_renewed_mock = mock.patch(
            'financial.utils.handle_subscription_renewed'
        ).start()

        # Call
        handle_approval(order_mock, payment_mock, {})

        handle_subscription_renewed_mock.assert_called_once_with(
            order=order_mock,
            payment=payment_mock,
            webhook_data={}
        )

    def test_handle_subscription_update_recurrence_metrics_is_called_on_handle_approval(self):
        mock.patch('financial.utils.get_or_create_user').start()
        mock.patch('financial.utils.handle_purchase_course_access').start()
        mock.patch('financial.utils.handle_commissionedUsers_purchase_approval').start()
        mock.patch('financial.utils.handle_purchase_approved_notification').start()
        mock.patch('financial.utils.calculate_product_metrics.delay').start()
        mock.patch('financial.utils.send_crm_sale_event.delay').start()
        mock.patch('gateway.services.email_marketing_service.send_user_to_email_marketing').start()
        mock.patch('financial.utils.handle_subscription_renewed').start()
        mock.patch('financial.utils.update_checkout_sales_count.delay').start()

        order_mock = mock.MagicMock(
            type=ProductType.SUBSCRIPTION.id,
            subscription=mock.MagicMock(externalId='123')
        )

        payment_mock = mock.MagicMock()

        # Call
        handle_approval(order_mock, payment_mock, {})

        order_mock.subscription.update_recurrence_metrics.assert_called_once()

    def test_update_checkout_sales_count_is_called_on_handle_approval(self):
        update_checkout_sales_count_mock = mock.patch(
            'financial.utils.update_checkout_sales_count.delay'
        ).start()

        # Call
        handle_approval(self.order, self.payment, {})

        update_checkout_sales_count_mock.assert_called_once_with(
            offer=self.order.offer,
            checkout=self.order.checkout,
        )

    def test_handle_subscription_renewed(self):
        order_mock = mock.MagicMock(
            type=ProductType.SUBSCRIPTION.id,
            subscription=mock.MagicMock(externalId='123')
        )

        payment_mock = mock.MagicMock()

        dispatch_event_mock = mock.patch('financial.utils.dispatch_app_events').start()

        send_subscription_renewed_email_mock = mock.patch(
            'financial.utils.send_subscription_renewed_email.delay'
        ).start()

        # Call
        handle_subscription_renewed(order_mock, payment_mock, {})

        # Assertions
        dispatch_event_mock.assert_called_once_with(
            'subscription_renewed',
            payment=payment_mock,
            order=order_mock,
        )

        send_subscription_renewed_email_mock.assert_called_once_with(
            order=order_mock,
            payment=payment_mock,
        )

    def test_handle_purchase_approved_notification_unique_payment(self):
        method_mapping = {
            (self.credit_card, 'Venda aprovada no Cartão!'),
            (self.pix, 'Venda aprovada no Pix!'),
            (self.boleto, 'Venda aprovada no Boleto!'),
            (self.picpay, 'Venda aprovada no PicPay!'),
            (self.googlepay, 'Venda aprovada no Google Pay!'),
            (self.applepay, 'Venda aprovada no Apple Pay!'),
            (self.openfinance_nubank, 'Venda aprovada no Nubank!'),
        }

        for payment_method, expected_title in method_mapping:
            with self.subTest(payment_method=payment_method):
                self.order.paymentMethod = payment_method
                self.order.save(update_fields=['paymentMethod'])

                send_notofication_mock = mock.patch(
                    'financial.utils.send_notification'
                ).start()

                # Call
                handle_purchase_approved_notification(
                    order=self.order,
                    is_subscription_payment=False,
                    is_new_subscription=False,
                )

                commission = sum(split.totalAmount for split in self.order.splits.filter(user=self.user))

                send_notofication_mock.assert_called_once_with(
                    self.user,
                    expected_title,
                    'Sua comissão: {amount}',
                    commission,
                )

    def test_handle_purchase_approved_notification_subscription_created(self):
        self.order.paymentMethod = self.credit_card
        self.order.save(update_fields=['paymentMethod'])

        send_notofication_mock = mock.patch(
            'financial.utils.send_notification'
        ).start()

        # Call
        handle_purchase_approved_notification(
            order=self.order,
            is_subscription_payment=True,
            is_new_subscription=True,
        )

        expected_title = 'Assinatura aprovada no Cartão!'

        commission = sum(split.totalAmount for split in self.order.splits.filter(user=self.user))

        send_notofication_mock.assert_called_once_with(
            self.user,
            expected_title,
            'Sua comissão: {amount}',
            commission,
        )

    def test_handle_purchase_approved_notification_subscription_renewed(self):
        self.order.paymentMethod = self.credit_card
        self.order.save(update_fields=['paymentMethod'])

        send_notofication_mock = mock.patch(
            'financial.utils.send_notification'
        ).start()

        # Call
        handle_purchase_approved_notification(
            order=self.order,
            is_subscription_payment=True,
            is_new_subscription=False,
        )

        expected_title = 'Assinatura renovada no Cartão!'

        commission = sum(split.totalAmount for split in self.order.splits.filter(user=self.user))

        send_notofication_mock.assert_called_once_with(
            self.user,
            expected_title,
            'Sua comissão: {amount}',
            commission,
        )

    def test_update_checkout_sales_count_is_called_on_handle_refund(self):
        checkout = Checkout.objects.create(
            product=self.product,
            name='Test Checkout',
        )
        checkout.offers.add(self.offer)
        checkout.save()

        self.order.checkout = checkout
        self.order.status = PaymentStatus.PAID.id
        self.order.save()

        update_checkout_sales_count_mock = mock.patch(
            'financial.utils.update_checkout_sales_count.delay'
        ).start()

        # Call
        handle_refund(self.order, self.payment, {})

        update_checkout_sales_count_mock.assert_called_once_with(
            offer=self.offer,
            checkout=checkout,
        )

    def test_update_checkout_sales_count_is_called_on_handle_med(self):
        checkout = Checkout.objects.create(
            product=self.product,
            name='Test Checkout',
        )
        checkout.offers.add(self.offer)
        checkout.save()

        self.order.checkout = checkout
        self.order.status = PaymentStatus.PAID.id
        self.order.save()

        update_checkout_sales_count_mock = mock.patch(
            'financial.utils.update_checkout_sales_count.delay'
        ).start()

        # Call
        handle_med(self.order, self.payment, {})

        update_checkout_sales_count_mock.assert_called_once_with(
            offer=self.offer,
            checkout=checkout,
        )

    def test_update_checkout_sales_count_is_called_on_handle_chargeback(self):
        checkout = Checkout.objects.create(
            product=self.product,
            name='Test Checkout',
        )
        checkout.offers.add(self.offer)
        checkout.save()

        self.order.checkout = checkout
        self.order.status = PaymentStatus.PAID.id
        self.order.save()

        update_checkout_sales_count_mock = mock.patch(
            'financial.utils.update_checkout_sales_count.delay'
        ).start()

        # Call
        handle_chargeback(self.order, self.payment, {})

        update_checkout_sales_count_mock.assert_called_once_with(
            offer=self.offer,
            checkout=checkout,
        )


class TestRemoveContentAccess(BaseTestCase):
    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()
        cls.create_paymentMethods()

        cls.product = cls.create_product(user=cls.user)

        owner_customer = cls.create_customer(email=cls.user.email)

        cls.order_owner = cls.create_order(
            product=cls.product,
            customer=owner_customer,
            status="refunded",
        )

        buyer_user = cls.create_user(email="<EMAIL>")
        buyer_customer = cls.create_customer(email=buyer_user.email)

        cls.paid_order = cls.create_order(
            product=cls.product,
            customer=buyer_customer,
            status="paid",
        )

        cls.order_with_two_orders = cls.create_order(
            product=cls.product,
            customer=buyer_customer,
            status="refunded",
        )

    def test_owner_does_not_lose_access(self):
        with (
            patch("financial.utils.handle_members_course_removal_access.delay") as m1,
            patch("financial.utils.remove_instagram_access_from_order.delay") as m2,
            patch("financial.utils.remove_telegram_access_from_order.delay") as m3,
            patch("financial.utils.remove_discord_access_from_order.delay") as m4,
        ):
            remove_content_access_based_on_order(self.order_owner)

        m1.assert_not_called()
        m2.assert_not_called()
        m3.assert_not_called()
        m4.assert_not_called()

    def test_multiple_paid_orders_keeps_access(self):
        with (
            patch("financial.utils.handle_members_course_removal_access.delay") as m1,
            patch("financial.utils.remove_instagram_access_from_order.delay") as m2,
            patch("financial.utils.remove_telegram_access_from_order.delay") as m3,
            patch("financial.utils.remove_discord_access_from_order.delay") as m4,
        ):
            remove_content_access_based_on_order(self.order_with_two_orders)

        m1.assert_not_called()
        m2.assert_not_called()
        m3.assert_not_called()
        m4.assert_not_called()
