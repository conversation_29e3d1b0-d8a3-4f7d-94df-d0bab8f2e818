FROM python:3.11-bookworm


# Create the user
ARG USERNAME=vscode
ARG USER_UID=1001
ARG USER_GID=$USER_UID

RUN groupadd --gid $USER_GID $USERNAME \
&& useradd --uid $USER_UID --gid $USER_GID -s /bin/bash -m $USERNAME \

# Add sudo support.
&& apt-get update \
&& apt-get install -y sudo postgresql-client \
&& echo $USERNAME ALL=\(root\) NOPASSWD:ALL > /etc/sudoers.d/$USERNAME \
&& chmod 0440 /etc/sudoers.d/$USERNAME

# Install UV package manager a pip replacement
RUN curl -LsSf https://astral.sh/uv/0.7.2/install.sh | UV_INSTALL_DIR=/usr/bin sh \
    && sh -c "$(curl --location https://taskfile.dev/install.sh)" -- -d -b /usr/bin v3.43.3

ENV POETRY_VERSION=2.1.3
RUN uv pip install awscli-local poetry==${POETRY_VERSION} pytest pytest-cov ruff  --upgrade --system

USER $USERNAME