// For format details, see https://aka.ms/vscode-remote/devcontainer.json or this file's README at:
// https://github.com/microsoft/vscode-dev-containers/tree/v0.202.3/containers/python-3
{
	"name": "cakto-backend",
	"dockerComposeFile": [
		"./docker-compose.yml"
	],
	"service": "cakto-backend",
	"shutdownAction": "stopCompose",
	"workspaceFolder": "/workspace",
	"features": {
		"ghcr.io/devcontainers/features/aws-cli:1": {}
	},
	"customizations": {
		"vscode": {
			"extensions": [
				"ms-python.python",
				"ms-python.vscode-pylance",
				"charliermarsh.ruff",
				"johnpapa.vscode-peacock",
				"shardulm94.trailing-spaces",
				"humao.rest-client",
				"mhutchie.git-graph",
				"donjayamanne.githistory",
				"codeium.codeium",
				"github.vscode-github-actions",
				"github.vscode-pull-request-github"
			],
			"settings": {
				"terminal.integrated.profiles.linux": {
					"bash": {
						"path": "/bin/bash"
					}
				},
				"[python]": {
					"editor.formatOnSave": false,
					"editor.defaultFormatter": "charliermarsh.ruff",
					"editor.formatOnSaveMode": "file",
					"editor.rulers": [
						100
					]
				},
				"workbench.colorCustomizations": {
					"editorRuler.foreground": "#ff4081"
				},
				"python.terminal.activateEnvironment": true,
				"python.defaultInterpreterPath": "./.venv/bin/python",
				"python.languageServer": "Pylance",
				"python.analysis.typeCheckingMode": "basic",
				"python.analysis.diagnosticMode": "openFilesOnly",
				"python.analysis.completeFunctionParens": true,
				"python.testing.pytestArgs": [
					"."
				],
				"python.testing.unittestEnabled": false,
				"python.testing.pytestEnabled": true,
				"ruff.nativeServer": "on",
				"ruff.lineLength": 100
			}
		}
	},
	"remoteEnv": {},
	"mounts": [],
	"forwardPorts": [
		"cakto-backend:8000",
		"cakto-backend-checkout:8080",
		"redis:6379",
		"db:5432"
	],
	"postCreateCommand": "bash entrypoints/development.sh"
}