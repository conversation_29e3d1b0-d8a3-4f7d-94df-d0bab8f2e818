<!doctype html>
<html lang="pt-BR">
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <meta http-equiv="X-UA-Compatible" content="ie=edge" />
        <title>Cakto - Login</title>
        <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
        <link
            href="https://fonts.googleapis.com/css2?family=Geist:wght@100..900&display=swap"
            rel="stylesheet"
        />
        <style>
            .font-sans {
                font-family: "Geist", sans-serif;
                font-optical-sizing: auto;
                font-weight: 400;
                font-style: normal;
            }
            .bg-gradient {
                background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
            }
            .bg-sidebar {
                background-color: #161c24;
            }
            .login-container {
                box-shadow:
                    0 10px 25px -5px rgba(0, 0, 0, 0.1),
                    0 8px 10px -6px rgba(0, 0, 0, 0.1);
            }
        </style>
    </head>

    <body class="bg-white font-sans antialiased w-full min-h-screen">
        <main class="w-full min-h-screen flex flex-col md:flex-row">
            <!-- Left side - Login form -->
            <div
                class="w-full md:w-2/5 flex flex-col items-start justify-center p-6 md:p-12 lg:p-20"
            >
                <div class="w-full mx-auto">
                    <img
                        src="https://app.cakto.com.br/logo/green-text-logo-transparent-background-login.png"
                        alt="Cakto Logo"
                        class="w-32 h-auto mb-10"
                    />

                    <h1
                        class="text-2xl md:text-3xl text-gray-900 font-bold mb-2"
                    >
                        Entrar na sua conta
                    </h1>
                    <p class="text-sm text-gray-600 font-normal mb-8">
                        Insira seus dados abaixo para continuar sua jornada na
                        Cakto
                    </p>

                    {% if request.session.user_id %}
                    <div class="w-full mb-6 rounded-md border border-gray-200 bg-gray-50 p-4">
                        <p class="text-sm text-gray-700 font-medium">
                            <span>Você já está entrando como</span
                        </p>
                    {% endif %}
                    {% if users %}
                        {% for id, user in users %}
                            {% if request.session.user_id == id or not request.session.user_id %}
                            <!-- col -->
                            <div class="bg-white rounded-xl shadow-lg mt-3 p-4 mb-2 space-y-3 {% if not request.session.user_id %}hover:bg-gray-200 transition-all duration-300 cursor-pointer{% endif %}" {% if not request.session.user_id %}onclick="this.querySelector('#user-form-{{ id }}').submit()"{% endif %}>
                                    <form
                                        id="user-form-{{ id }}"
                                        method="POST"
                                        action=""
                                        class="flex items-center justify-between"
                                    >
                                    {% csrf_token %}
                                <!-- Current User -->
                                <div class="flex items-center gap-4 {% if other_accounts %}border-b border-gray-200 pb-3{% endif %}">
                                    <img
                                    src="{% if user.image  %}{{user.image}}{% else %}https://www.gravatar.com/avatar/<EMAIL>?d=identicon&size=100{% endif %}"
                                    alt="Avatar"
                                    class="w-12 h-12 rounded-full"
                                    />
                                    <div>
                                    <p class="text-sm font-semibold text-gray-900">{{ user.full_name }}</p>
                                    <p class="text-sm text-gray-600">{{ user.email }}</p>
                                    </div>
                                    <input
                                    type="hidden"
                                    name="user_id"
                                    value="{{ id }}"
                                    />
                                    </form>
                                </div>
                            </div>
                            {% endif %}
                        {% endfor %}
                        {% if request.session.user_id %}
                        <p class="text-sm text-gray-600">
                            <span>Deseja entrar com outra conta?</span>
                            <a
                                href="/accounts/login/"
                                class="text-emerald-600 hover:text-emerald-700 font-medium"
                            >Sair</a>
                        </p>
                        {% endif %}
                      <!-- Other Accounts -->
                      <!-- <div class="pt-3 space-y-2">
                        <div class="flex items-center gap-3 p-2 rounded hover:bg-gray-100 cursor-pointer">
                          <img
                            src="https://www.gravatar.com/avatar/<EMAIL>?d=identicon&size=100"
                            class="w-10 h-10 rounded-full"
                            alt="Avatar"
                          />
                          <div>
                            <p class="text-sm font-medium text-gray-900">Conta 2</p>
                            <p class="text-xs text-gray-500"><EMAIL></p>
                          </div>
                        </div>
                      </div> -->

                      <!-- Footer -->
                      <!-- <div class="pt-4 space-y-2">
                        <div class="text-sm text-blue-600 hover:underline cursor-pointer">
                          Adicionar outra conta
                        </div>
                        <div class="text-sm text-red-600 hover:underline cursor-pointer">
                          Sair de todas as contas
                        </div>
                      </div> -->
                    </div>

                    {% endif %}

                    {% if error %}
                    <div
                        class="w-full mb-6 rounded-md border border-red-500 bg-red-50 p-4"
                    >
                        <p class="text-sm text-red-600 font-medium">
                            {{ error }}
                        </p>
                    </div>
                    {% endif %}

                    <!--
                    <div class="flex flex-col gap-3 mb-6">

                        <button class="w-full h-12 flex items-center cursor-pointer justify-center gap-3 border border-gray-300 rounded-md text-sm text-gray-700 font-medium hover:bg-gray-50 transition-all duration-300">
                            <svg xmlns="http://www.w3.org/2000/svg" width="18" class="sso-providers__button__icon svg-inline--fa" viewBox="-3 0 262 262" preserveAspectRatio="xMidYMid">
                                <path d="M255.878 133.451c0-10.734-.871-18.567-2.756-26.69H130.55v48.448h71.947c-1.45 12.04-9.283 30.172-26.69 42.356l-.244 1.622 38.755 30.023 2.685.268c24.659-22.774 38.875-56.282 38.875-96.027" fill="#4285F4"></path><path d="M130.55 261.1c35.248 0 64.839-11.605 86.453-31.622l-41.196-31.913c-11.024 7.688-25.82 13.055-45.257 13.055-34.523 0-63.824-22.773-74.269-54.25l-1.531.13-40.298 31.187-.527 1.465C35.393 231.798 79.49 261.1 130.55 261.1" fill="#34A853"></path><path d="M56.281 156.37c-2.756-8.123-4.351-16.827-4.351-25.82 0-8.994 1.595-17.697 4.206-25.82l-.073-1.73L15.26 71.312l-1.335.635C5.077 89.644 0 109.517 0 130.55s5.077 40.905 13.925 58.602l42.356-32.782" fill="#FBBC05"></path><path d="M130.55 50.479c24.514 0 41.05 10.589 50.479 19.438l36.844-35.974C195.245 12.91 165.798 0 130.55 0 79.49 0 35.393 29.301 13.925 71.947l42.211 32.783c10.59-31.477 39.891-54.251 74.414-54.251" fill="#EB4335"></path>
                            </svg>
                            Entrar com Google
                        </button>
                        <button class="w-full h-12 flex cursor-pointer items-center justify-center gap-3 border border-gray-300 rounded-md text-sm text-gray-700 font-medium hover:bg-gray-50 transition-all duration-300">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" class="sso-providers__button__icon icon-white svg-inline--fa" viewBox="0 0 17 21" fill="currentColor">
                                <path d="M14.2182 10.6686C14.2093 9.01443 14.9501 7.76593 16.4496 6.84646C15.6106 5.63402 14.3432 4.96695 12.6696 4.83624C11.0853 4.71004 9.35373 5.76923 8.72001 5.76923C8.05059 5.76923 6.51537 4.88131 5.31041 4.88131C2.82016 4.92188 0.173706 6.88702 0.173706 10.8849C0.173706 12.0658 0.387921 13.2858 0.816352 14.5448C1.38759 16.1989 3.44942 20.2554 5.6005 20.1878C6.72513 20.1608 7.51951 19.381 8.98331 19.381C10.4025 19.381 11.1389 20.1878 12.3929 20.1878C14.5618 20.1563 16.4273 16.4694 16.9718 14.8107C14.062 13.427 14.2182 10.7542 14.2182 10.6686ZM11.6922 3.26773C12.9106 1.80739 12.799 0.477765 12.7633 0C11.6878 0.063101 10.4427 0.739183 9.73307 1.57302C8.95208 2.46545 8.4924 3.56971 8.59059 4.8137C9.75538 4.90385 10.8175 4.29988 11.6922 3.26773Z"></path>
                            </svg>
                            Entrar com Apple
                        </button>
                    </div>


                    <div class="flex items-center gap-3 my-6">
                        <div class="h-px bg-gray-200 flex-1"></div>
                        <span class="text-sm text-gray-500">ou</span>
                        <div class="h-px bg-gray-200 flex-1"></div>
                    </div> -->

                    <form
                        method="POST"
                        action="{{ request.path }}"
                        class="w-full flex flex-col gap-4"
                    >
                        {% csrf_token %}

                        {% if form.non_field_errors %}
                        <div
                            class="w-full mb-2 rounded-md border border-red-500 bg-red-50 p-4"

                        >
                            {% for error in form.non_field_errors %}
                            <p class="text-sm text-red-600 font-small">
                                {{ error }}
                            </p>
                            {% endfor %}
                        </div>
                        {% endif %}
                        {% if not form.get_user and not sent %}
                        {% if users %}
                        <div class="flex items-center gap-3 my-6">
                            <div class="h-px bg-gray-200 flex-1"></div>
                            <span class="text-sm text-gray-500">ou</span>
                            <div class="h-px bg-gray-200 flex-1"></div>
                        </div>
                        {% endif %}
                        <div class="w-full flex flex-col gap-2">
                            <label
                                for="email"
                                class="text-sm text-gray-700 font-medium"
                                >E-mail</label
                            >
                            <input
                                type="text"
                                id="email"
                                name="username"
                                placeholder="Digite seu e-mail"
                                class="w-full h-12 bg-white border border-gray-300 rounded-md px-4 text-sm text-gray-900 outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-all duration-300"
                                required
                            />
                            <small class="text-red-600 text-sm">{{ form.username.errors }}</small>
                        </div>

                        <div class="w-full flex flex-col gap-2">
                            <div class="flex justify-between items-center">
                                <label
                                    for="password"
                                    class="text-sm text-gray-700 font-medium"
                                    >Senha</label
                                >
                                <a
                                    href="https://app.cakto.com.br/auth/reset-password"
                                    target="_blank"
                                    class="text-xs text-emerald-600 hover:text-emerald-700 font-medium"
                                    >Esqueceu a senha?</a
                                >
                            </div>
                            <input
                                type="password"
                                id="password"
                                name="password"
                                placeholder="Sua senha"
                                class="w-full h-12 bg-white border border-gray-300 rounded-md px-4 text-sm text-gray-900 outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-all duration-300"
                                required
                            />
                            <small class="text-red-600 text-sm">{{ form.password.errors }}</small>
                        </div>
                        {% endif %}

                        <!-- Lista de dispositivos como botões -->
                        {% if form.get_user and not sent %}
                        <div class="w-full flex flex-col">
                          <label class="text-sm text-gray-700 font-medium">Escolha o dispositivo para validar</label>
                            <select
                            name="otp_device"
                            id="otp_device"
                            class="w-full md:w-auto h-12 bg-white border border-gray-300 rounded-md px-4 text-sm text-gray-900 outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-all duration-300 mb-2"
                            >
                                {% for mfa in mfa_methods %}
                                <option value="{{ mfa.id }}" {% if mfa.device.id == mfa.primary %}selected{% endif %}>
                                    {% if mfa.primary %}⭐{% endif %} {{ mfa.device.name }}
                                </option>
                                {% endfor %}
                            </select>

                          {% if form.otp_device.errors %}
                            <p class="text-sm text-red-600 mt-2">{{ form.otp_device.errors }}</p>
                          {% endif %}
                        </div>

                        <div class="form-row">
                            <input
                                class="w-full h-12 mt-2 bg-emerald-600 rounded-md text-lg text-white font-medium hover:bg-emerald-700 transition-all duration-300 cursor-pointer"
                                type="submit"
                                name="otp_challenge"
                                value="Enviar código"
                            />
                        </div>
                        {% endif %}

                        {% if sent %}
                        <label
                            for="otp_token"
                            class="mt-2 text-sm text-gray-700 font-medium"
                            >Digite o código</label
                        >
                        <input
                            type="text"
                            name="otp_token"
                            id="otp_token"
                            placeholder="Digite o código enviado via {{ sent.type|title }}"
                            class="w-full h-12 bg-white border border-gray-300 rounded-md px-4 text-sm text-gray-900 outline-none focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-all duration-300"
                            {% if form.get_user %}required{% endif %}
                            required
                        />
                        {{ form.otp_token.errors }}
                        {% endif %}

                        {% if sent or not form.get_user %}
                        <button
                            type="submit"
                            class="w-full cursor-pointer h-12 mt-2 bg-emerald-600 rounded-md text-lg text-white font-medium hover:bg-emerald-700 transition-all duration-300 flex items-center justify-center"
                        >
                            Entrar
                        </button>
                        {% endif %}
                    </form>

                    <p class="text-sm text-gray-600 text-center mt-8">
                        Não tem uma conta?
                        <a
                            href="#"
                            class="text-emerald-600 hover:text-emerald-700 font-medium"
                            >Cadastre-se</a
                        >
                    </p>
                </div>
            </div>

            <!-- Right side - Image -->
            <div
                class="hidden md:block md:w-3/5 relative overflow-hidden bg-sidebar"
            >
                <img
                    src="https://cdn-checkout.cakto.com.br/banners/01---ARTElogin.png"
                    alt="Cakto"
                    class="w-full h-full object-cover object-center"
                />
            </div>
        </main>
    </body>
</html>
