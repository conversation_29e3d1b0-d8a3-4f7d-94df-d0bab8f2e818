DJANGO_SETTINGS_MODULE=cakto.settings_local
SESSION_COOKIE_DOMAIN=localhost
REDIRECT_DOMAIN=backend.dev.teste
ALLOWED_HOSTS=backend.dev.teste,frontend.dev.teste,localhost
CORS_ORIGIN_WHITELIST=http://localhost:3000,http://localhost:3001,http://localhost:3002,https://frontend.dev.teste,https://backend.dev.teste
CSRF_TRUSTED_ORIGINS=http://localhost:3000,http://localhost:8000,http://localhost:3001,http://localhost:3002,https://frontend.dev.teste,https://backend.dev.teste

BACKEND_BASE_URL=localhost:8000/
FRONT_END_BASE_URL=https://app.cakto.com.br
CHECKOUT_BASE_URL=http://localhost:3001

# Members
MEMBERS_URL=
MEMBERS_ADMIN_PASSWORD=
COURSE_ACCESS_BASE_URL=https://members.cakto.com.br/
VIDEO_HOST_APIKEY=
VIDEO_CDN_HOST_NAME=
VIDEO_LIBRARY_ID=

# Members V2
MEMBERS_V2_BASE_URL=
MEMBERS_V2_API_TOKEN=
MEMBERS_V2_MAX_IMAGE_SIZE=10
MEMBERS_V2_MAX_FILE_SIZE=1024
MEMBERS_V2_TOKEN_CACHE_TIMEOUT=1380

# Gateway
NOX_SECRET_KEY=
NOX_PUBLIC_KEY=
GATEWAY_URL=

REDIS_URL_QUEUE=redis://localhost:6379
WEBHOOK_SECRETS=secret1,secret2,secret3

MEMBERS_AWS_ACCESS_KEY_ID=
MEMBERS_AWS_SECRET_ACCESS_KEY=
MEMBERS_AWS_S3_REGION_NAME=
MEMBERS_AWS_STORAGE_BUCKET_NAME=
MEMBERS_AWS_S3_ENDPOINT_URL=

# RECAPTCHA
RECAPTCHA_SITE_KEY=
RECAPTCHA_PROJECT_ID=
RECAPTCHA_THRESHOLD=0.1

MANYCHAT_TOKEN=
MANYCHAT_AUTO_PHONE_ID=123456

INSTAGRAM_BASE_URL=
INSTAGRAM_AUTH_TOKEN=

# true=enabled, false=disabled
WHATSAPP_VALIDATION_ENABLED=false
ENABLE_DJANGO_ADMIN_MFA=true
ENABLE_FAKE_REDIS_CONN=false

DISCORD_APP_ID=
DISCORD_SECRET=
DISCORD_BOT_TOKEN=

# APPLE PAY
APPLE_PAY_MERCHANT_IDENTIFIER=
APPLE_PAY_DOMAIN_NAME=
APPLE_PAY_DISPLAY_NAME=
APPLE_PAY_CERT=""
APPLE_PAY_KEY=""

INFOBIP_BASE_URL=
INFOBIP_API_TOKEN=
INFOBIP_WHATSAPP_NUMBER=
INFOBIP_AUTH_TEMPLATE_NAME=autenticacao_cakto

CRM_WEBHOOK_URL=
CRM_WEBHOOK_TOKEN=

AI_COMPLIANCE_URL=
AI_COMPLIANCE_API_KEY=

# Facebook Pixel for cakto custom conversion events (login)
FACEBOOK_PIXEL_ID=
FACEBOOK_ACCESS_TOKEN=

# MailChimp
MAILCHIMP_API_KEY=
MAILCHIMP_SERVER_PREFIX=
MAILCHIMP_AUDIENCE_ID=

#TheMembers
THEMEMBERS_DEV_TOKEN =

# Register producer webhook - url to receive producer registration events
REGISTER_PRODUCER_WEBHOOK_URL=
