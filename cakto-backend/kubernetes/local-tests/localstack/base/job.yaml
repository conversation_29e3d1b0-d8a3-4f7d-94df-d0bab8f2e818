apiVersion: batch/v1
kind: Job
metadata:
  name: criar-localstack-secrets-job
spec:
  backoffLimit: 3
  template:
    spec:
      restartPolicy: OnFailure
      containers:
      - name: init-secrets
        image: amazon/aws-cli:2.27.24
        command: ["/bin/sh", "-c"]
        args:
          - |
            echo "Esperando LocalStack ($LOCALSTACK_BASE_URL) subir...";
            until curl -s $LOCALSTACK_BASE_URL/_localstack/health; do sleep 1; done;
            echo "LocalStack está pronto!";
            aws configure set aws_access_key_id $AWS_ACCESS_KEY_ID && \
            aws configure set aws_secret_access_key $AWS_SECRET_ACCESS_KEY && \
            aws configure set region $AWS_REGION

            aws --endpoint-url=$LOCALSTACK_BASE_URL secretsmanager create-secret \
              --name cakto-backend-local/app-secrets \
              --secret-string file:///mnt/secret/app-secrets.json;

            echo "Secret cakto-backend-local/app-secrets criada!";

            aws --endpoint-url=$LOCALSTACK_BASE_URL secretsmanager create-secret \
              --name global/datadog-secrets \
              --secret-string file:///mnt/secret/datadog-secrets.json;

            echo "Secret global/datadog-secrets criada!";

            echo "Fim da criação das secrets";
            exit 0
        volumeMounts:
          - name: aws-secrets-json
            mountPath: /mnt/secret
        env:
          - name: AWS_ACCESS_KEY_ID
            value: test
          - name: AWS_SECRET_ACCESS_KEY
            value: test
          - name: AWS_REGION
            value: us-east-2
          - name: LOCALSTACK_BASE_URL
            value: http://localstack:4566
      volumes:
        - name: aws-secrets-json
          configMap:
            name: aws-secrets-json
