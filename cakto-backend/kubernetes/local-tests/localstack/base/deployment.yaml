# 02-localstack-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: localstack
spec:
  replicas: 1
  selector:
    matchLabels:
      app: localstack
  template:
    metadata:
      labels:
        app: localstack
    spec:
      containers:
        - name: localstack
          image: localstack/localstack:4.4.0
          env:
            - name: SERVICES
              value: secretsmanager
            - name: DEFAULT_REGION
              value: us-east-2
          ports:
            - containerPort: 4566
          readinessProbe:
            httpGet:
              path: /_localstack/health
              port: 4566
            initialDelaySeconds: 15
            periodSeconds: 5