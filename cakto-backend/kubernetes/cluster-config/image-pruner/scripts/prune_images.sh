#!/bin/bash

if [[ ! "$SLEEP_SECONDS_BETWEEN_RUNS" =~ ^[0-9]+$ ]]; then
  printf '[%(%Y-%m-%d %H:%M:%S)T] ERROR: A variável SLEEP_SECONDS_BETWEEN_RUNS deve conter o número de segundos de pausa entre as execuções.\n' -1
  exit 1
fi

printf '[%(%Y-%m-%d %H:%M:%S)T] Segundos de pausa entre as execuções: %s\n' -1 "$SLEEP_SECONDS_BETWEEN_RUNS"

if [[ -z "$CRICTL_VERSION" ]]; then
  printf '[%(%Y-%m-%d %H:%M:%S)T] ERROR: A variável CRICTL_VERSION está vazia, mas deve conter a versão a ser instalada da aplicação crictl.\n' -1
  exit 1
fi


printf '[%(%Y-%m-%d %H:%M:%S)T] Versão a ser instalada de crictl: %s\n' -1 "$CRICTL_VERSION"


apt-get update && apt-get install -y wget
wget https://github.com/kubernetes-sigs/cri-tools/releases/download/$CRICTL_VERSION/crictl-$CRICTL_VERSION-linux-amd64.tar.gz
tar zxvf crictl-$CRICTL_VERSION-linux-amd64.tar.gz -C /usr/local/bin
rm -f crictl-$CRICTL_VERSION-linux-amd64.tar.gz

while true; do
  printf '[%(%Y-%m-%d %H:%M:%S)T] Iniciando limpeza...\n' -1
  crictl rmi --prune
  printf '[%(%Y-%m-%d %H:%M:%S)T] Limpeza concluída!\n' -1
  printf '[%(%Y-%m-%d %H:%M:%S)T] Aguardando %s segundos até a próxima execução...\n' -1 "$SLEEP_SECONDS_BETWEEN_RUNS"
  sleep $SLEEP_SECONDS_BETWEEN_RUNS
done

exit 0