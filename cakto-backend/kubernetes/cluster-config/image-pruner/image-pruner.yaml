apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: image-pruner-on-all-nodes
spec:
  selector:
    matchLabels:
      app: image-pruner
  template:
    metadata:
      labels:
        app: image-pruner
    spec:
      hostPID: true
      containers:
        - name: image-pruner
          image: bitnami/kubectl:1.32-debian-12
          env:
            - name: CRICTL_VERSION # define a versão da ferramenta crictl
              value: "v1.32.0"
            - name: CONTAINER_RUNTIME_ENDPOINT
              value: "unix:///run/containerd/containerd.sock"
            - name: SLEEP_SECONDS_BETWEEN_RUNS # define a quantidade de tempo a esperar, em segundos, antes de executar a próxima limpeza
              value: "43200" # 12 horas (12 * 60 * 60) = 43200
          command:
            - /bin/bash
            - -c
            - /scripts/prune_images.sh
          volumeMounts:
            - name: containerd-socket
              mountPath: /run/containerd
            - name: scripts-volume
              mountPath: /scripts
          securityContext:
            privileged: true
            runAsUser: 0
      volumes:
      - name: containerd-socket
        hostPath:
          path: /run/containerd
          type: Directory
      - name: scripts-volume
        configMap:
          name: image-pruner-config
          defaultMode: 0755
