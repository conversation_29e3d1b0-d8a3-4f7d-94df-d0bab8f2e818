datadog:
  enabled: true
  clustername: backend-staging
  features:
    logCollection:
      enabled: true
      containerCollectAll: false
    apm:
      enabled: true
    externalMetricsServer:
      enabled: false
    orchestratorExplorer:
      enabled: true

backend:
  container:
    image: cakto-backend
    tag: latest
  ingress:
    host: apistg-k8s.cakto.com.br
    # additionalHosts:
    #   - cakto-stg.app
    #   - cakto-stg.xyz
    #   - cakto-stg.net
    useTls: true

checkout_api:
  container:
    # annotations:
    #   ad.datadoghq.com/cakto-backend-checkout-api.logs: '
    #     [
    #       {
    #         "source": "python",
    #         "service": "cakto-backend-checkout-api",
    #         "log_processing_rules":[
    #           {
    #             "type":"exclude_at_match",
    #             "name":"exclude_health_check_logs",
    #             "pattern":"GET /docs HTTP/1\\.1"
    #           }
    #         ]
    #       }
    #     ]'
    labels:
      tags.datadoghq.com/env: staging
      # tags.datadoghq.com/service: cakto-backend-checkout-api
      # admission.datadoghq.com/enabled: "true"
    resources:
      requests:
        memory: "512Mi"
        cpu: "500m"
      limits:
        memory: "1.5Gi"
        cpu: "1000m"
    # readinessProbe:
    #   httpGet:
    #     path: /docs
    #     port: http
    #     httpHeaders:
    #       - name: Host
    #         value: "pod.localhost"
    #   initialDelaySeconds: 5
    #   periodSeconds: 5
    # livenessProbe:
    #   httpGet:
    #     path: /docs
    #     port: http
    #     httpHeaders:
    #       - name: Host
    #         value: "pod.localhost"
    #   timeoutSeconds: 10
    #   periodSeconds: 10
    #   failureThreshold: 10
    # startupProbe:
    #   httpGet:
    #     path: /docs
    #     port: http
    #     httpHeaders:
    #       - name: Host
    #         value: "pod.localhost"
    #   failureThreshold: 150
    #   periodSeconds: 2
    # extra_envs:
    # - name: DD_LOGS_INJECTION
    #   value: "true"
  hpa:
    minReplicas: 1
    maxReplicas: 2
    averageUtilization: 80
  # ingress:
    # annotations:
    #   nginx.ingress.kubernetes.io/proxy-body-size: "50m"


main_api:
  container:
    # annotations:
    #   ad.datadoghq.com/cakto-backend-main-api.logs: '
    #     [
    #       {
    #         "source": "python",
    #         "service": "cakto-backend-main-api",
    #         "log_processing_rules":[{"type":"exclude_at_match","name":"main_api_exclude_health_check_logs","pattern":"GET /health HTTP/1\\.1"}]
    #       }
    #     ]'
    labels:
      tags.datadoghq.com/env: staging
      # tags.datadoghq.com/service: cakto-backend-main-api
      # admission.datadoghq.com/enabled: "true"
    resources:
      requests:
        memory: "512Mi"
        cpu: "500m"
      limits:
        memory: "1.5Gi"
        cpu: "1000m"
    # readinessProbe:
    #   httpGet:
    #     path: /health
    #     port: http
    #     httpHeaders:
    #       - name: Host
    #         value: "pod.localhost"
    #   initialDelaySeconds: 5
    #   periodSeconds: 5
    # livenessProbe:
    #   httpGet:
    #     path: /health
    #     port: http
    #     httpHeaders:
    #       - name: Host
    #         value: "pod.localhost"
    #   timeoutSeconds: 10
    #   periodSeconds: 10
    #   failureThreshold: 10
    # startupProbe:
    #   httpGet:
    #     path: /health
    #     port: http
    #     httpHeaders:
    #       - name: Host
    #         value: "pod.localhost"
    #   failureThreshold: 150
    #   periodSeconds: 2
    # extra_envs:
    # - name: DD_LOGS_INJECTION
    #   value: "true"
  hpa:
    minReplicas: 1
    maxReplicas: 2
    averageUtilization: 80
  # ingress:
  #   annotations:
  #     nginx.ingress.kubernetes.io/proxy-body-size: "50m"

threeds_api:
  container:
    # annotations:
    #   ad.datadoghq.com/cakto-backend-3ds-api.logs: '
    #     [
    #       {
    #         "source": "python",
    #         "service": "cakto-backend-3ds-api",
    #         "log_processing_rules":[{"type":"exclude_at_match","name":"main_api_exclude_health_check_logs","pattern":"GET /health HTTP/1\\.1"}]
    #       }
    #     ]'
    labels:
      tags.datadoghq.com/env: staging
      # tags.datadoghq.com/service: cakto-backend-3ds-api
      # admission.datadoghq.com/enabled: "true"
    resources:
      requests:
        memory: "512Mi"
        cpu: "500m"
      limits:
        memory: "1.5Gi"
        cpu: "1000m"
    # readinessProbe:
    #   httpGet:
    #     path: /health
    #     port: http
    #     httpHeaders:
    #       - name: Host
    #         value: "pod.localhost"
    #   initialDelaySeconds: 5
    #   periodSeconds: 5
    # livenessProbe:
    #   httpGet:
    #     path: /health
    #     port: http
    #     httpHeaders:
    #       - name: Host
    #         value: "pod.localhost"
    #   timeoutSeconds: 10
    #   periodSeconds: 10
    #   failureThreshold: 10
    # startupProbe:
    #   httpGet:
    #     path: /health
    #     port: http
    #     httpHeaders:
    #       - name: Host
    #         value: "pod.localhost"
    #   failureThreshold: 150
    #   periodSeconds: 2
    # extra_envs:
    # - name: DD_LOGS_INJECTION
    #   value: "true"
  hpa:
    minReplicas: 1
    maxReplicas: 2
    averageUtilization: 90
  # ingress:
  #   annotations:
  #     nginx.ingress.kubernetes.io/proxy-body-size: "50m"


postgres:
  create: false
  # container:
  #   imageAndTag: postgres:14.13-alpine3.20
  #   resources:
  #     requests:
  #       memory: "128Mi"
  #       cpu: "100m"
  #     limits:
  #       memory: "256Mi"
  #       cpu: "500m"


redis:
  create: true
  container:
    imageAndTag: redis:7.4.0-alpine3.20
    resources:
      requests:
        memory: "64Mi"
        cpu: "100m"
      limits:
        memory: "512Mi"
        cpu: "250m"
  volumeClaimTemplates:
    spec:
      accessModes:
      - ReadWriteOnce
      resources:
        requests:
          storage: 2Gi
      storageClassName: do-block-storage



# runapscheduler:
#   container:
#     replicas: 1
#     resources:
#       requests:
#         memory: "256Mi"
#         cpu: "150m"
#       limits:
#         memory: "512Mi"
#         cpu: "300m"

rqworker:
  # container:
  #   resources:
  #     requests:
  #       memory: "256Mi"
  #       cpu: "150m"
  #     limits:
  #       memory: "512Mi"
  #       cpu: "300m"
  scaledObject:
    minReplicaCount: 1
    maxReplicaCount:  2
    pollingInterval: 10
    metadata:
      listLength: "10"
      enableTLS: "false"
      unsafeSsl: "true"
      databaseIndex: "0"

rqworker_metrics:
  # container:
  #   resources:
  #     requests:
  #       memory: "256Mi"
  #       cpu: "150m"
  #     limits:
  #       memory: "512Mi"
  #       cpu: "300m"
  scaledObject:
    minReplicaCount: 1
    maxReplicaCount:  2
    pollingInterval: 10
    metadata:
      listLength: "10"
      enableTLS: "false"
      unsafeSsl: "true"
      databaseIndex: "0"

rqworker_pixel_events:
  # container:
  #   resources:
  #     requests:
  #       memory: "256Mi"
  #       cpu: "150m"
  #     limits:
  #       memory: "512Mi"
  #       cpu: "300m"
  scaledObject:
    minReplicaCount: 1
    maxReplicaCount:  2
    pollingInterval: 10
    metadata:
      pixel_events:
        listLength: "10"
        enableTLS: "false"
        unsafeSsl: "true"
        databaseIndex: "0"
      cakto_pixel:
        listLength: "10"
        enableTLS: "false"
        unsafeSsl: "true"
        databaseIndex: "0"

secrets:
  externalSecret:
    aws:
      prefix: cakto-backend-staging