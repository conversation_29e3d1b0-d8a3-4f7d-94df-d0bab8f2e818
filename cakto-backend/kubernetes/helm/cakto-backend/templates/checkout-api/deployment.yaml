apiVersion: apps/v1
kind: Deployment
metadata:
  name: cakto-backend-checkout-api
  labels:
    {{- include "common.labels" . | nindent 4 }}
    app.kubernetes.io/name: cakto-backend-checkout-api
spec:
  selector:
    matchLabels:
      app.kubernetes.io/name: cakto-backend-checkout-api
  template:
    metadata:
      labels:
        {{- include "common.labels" . | nindent 8 }}
        app.kubernetes.io/name: cakto-backend-checkout-api
        {{- with .Values.checkout_api.container.labels }}
          {{- toYaml . | nindent 8 }}
        {{- end }}
      {{- with .Values.checkout_api.container.annotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
    spec:
      containers:
      - name: cakto-backend-checkout-api
        image: "{{ .Values.backend.container.image }}:{{ .Values.backend.container.tag }}"
        imagePullPolicy: IfNotPresent
        {{- with .Values.checkout_api.container.resources }}
        resources:
          {{- toYaml . | nindent 10 }}
        {{- end }}
        envFrom:
        - secretRef:
            name: app-secrets
        env:
          - name: BACKEND_APP
            value: checkout-api
        {{- with .Values.checkout_api.container.extra_envs }}
          {{- toYaml . | nindent 10 }}
        {{- end }}
        ports:
        - containerPort: 8000
          name: http
        {{- with .Values.checkout_api.container.readinessProbe }}
        readinessProbe:
          {{- toYaml . | nindent 10 }}
        {{- end }}
        {{- with .Values.checkout_api.container.livenessProbe }}
        livenessProbe:
          {{- toYaml . | nindent 10 }}
        {{- end }}
        {{- with .Values.checkout_api.container.startupProbe }}
        startupProbe:
          {{- toYaml . | nindent 10 }}
        {{- end }}