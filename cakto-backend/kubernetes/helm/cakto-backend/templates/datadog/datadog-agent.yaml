{{- if (default false .Values.datadog.enabled) }}
apiVersion: datadoghq.com/v2alpha1
kind: DatadogAgent
metadata:
  name: datadog-agent
  namespace: datadog
spec:
  global:
    clusterName: {{.Values.datadog.clustername}}
    site: datadoghq.com
    credentials:
      apiSecret:
        secretName: datadog-secrets
        keyName: api-key
  {{- with .Values.datadog.override }}
  override:
    {{- toYaml . | nindent 4 }}
  {{- end }}
    {{- with .Values.datadog.features }}
  features:
    {{- toYaml . | nindent 4 }}
  {{- end }}
{{- end }}