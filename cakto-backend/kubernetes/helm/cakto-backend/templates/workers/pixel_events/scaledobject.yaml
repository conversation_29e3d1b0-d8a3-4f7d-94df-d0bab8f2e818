apiVersion: keda.sh/v1alpha1
kind: ScaledObject
metadata:
  name: cakto-backend-rqworker-pixel-events-scaledobject
  labels:
    {{- include "common.labels" . | nindent 4 }}
spec:
  scaleTargetRef:
    name: cakto-backend-rqworker-pixel-events
  minReplicaCount:  {{ .Values.rqworker_pixel_events.scaledObject.minReplicaCount }}
  maxReplicaCount:  {{ .Values.rqworker_pixel_events.scaledObject.maxReplicaCount }}
  pollingInterval: {{ .Values.rqworker_pixel_events.scaledObject.pollingInterval }}
  triggers:
  - type: redis
    metadata:
      usernameFromEnv: REDIS_USER
      passwordFromEnv: REDIS_PASSWORD
      addressFromEnv: REDIS_ADDRESS
      listName: rq:queue:pixel_events
      {{- with .Values.rqworker_pixel_events.scaledObject.metadata.pixel_events }}
        {{- toYaml . | nindent 6 }}
      {{- end }}
  - type: redis
    metadata:
      usernameFromEnv: REDIS_USER
      passwordFromEnv: REDIS_PASSWORD
      addressFromEnv: REDIS_ADDRESS
      listName: rq:queue:cakto_pixel
      {{- with .Values.rqworker_pixel_events.scaledObject.metadata.cakto_pixel }}
        {{- toYaml . | nindent 6 }}
      {{- end }}
