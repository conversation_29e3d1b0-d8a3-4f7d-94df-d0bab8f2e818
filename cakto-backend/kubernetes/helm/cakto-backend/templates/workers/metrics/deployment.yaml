apiVersion: apps/v1
kind: Deployment
metadata:
  name: cakto-backend-rqworker-metrics
  labels:
    app.kubernetes.io/name: cakto-backend-rqworker-metrics
    {{- include "common.labels" . | nindent 4 }}
spec:
  selector:
    matchLabels:
      app.kubernetes.io/name: cakto-backend-rqworker-metrics
  template:
    metadata:
      labels:
        app.kubernetes.io/name: cakto-backend-rqworker-metrics
        {{- include "common.labels" . | nindent 8 }}
    spec:
      containers:
      - name: cakto-backend-rqworker-metrics
        image:  "{{ .Values.backend.container.image }}:{{ .Values.backend.container.tag }}"
        imagePullPolicy: IfNotPresent
        {{- with .Values.rqworker_metrics.container.resources }}
        resources:
          {{- toYaml . | nindent 10 }}
        {{- end }}
        envFrom:
        - secretRef:
            name: app-secrets
        env:
          - name: BACKEND_APP
            value: metrics