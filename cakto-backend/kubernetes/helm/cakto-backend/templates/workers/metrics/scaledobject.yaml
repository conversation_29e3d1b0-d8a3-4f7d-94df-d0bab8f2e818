apiVersion: keda.sh/v1alpha1
kind: ScaledObject
metadata:
  name: cakto-backend-rqworker-metrics-scaledobject
  labels:
    {{- include "common.labels" . | nindent 4 }}
spec:
  scaleTargetRef:
    name: cakto-backend-rqworker-metrics
  minReplicaCount:  {{ .Values.rqworker_metrics.scaledObject.minReplicaCount }}
  maxReplicaCount:  {{ .Values.rqworker_metrics.scaledObject.maxReplicaCount }}
  pollingInterval: {{ .Values.rqworker_metrics.scaledObject.pollingInterval }}
  triggers:
  - type: redis
    metadata:
      usernameFromEnv: REDIS_USER
      passwordFromEnv: REDIS_PASSWORD
      addressFromEnv: REDIS_ADDRESS
      listName: rq:queue:metrics
      {{- with .Values.rqworker_metrics.scaledObject.metadata }}
        {{- toYaml . | nindent 6 }}
      {{- end }}