{{- if (default false .Values.backend.ingress.useTls) }}
apiVersion: cert-manager.io/v1
kind: Certificate
metadata:
  name: main-api-certificate
  labels:
    {{- include "common.labels" . | nindent 4 }}
spec:
  secretName: main-api-tls
  dnsNames:
  - {{.Values.backend.ingress.host}}
  issuerRef:
    name: letsencrypt-issuer
    # We can reference ClusterIssuers by changing the kind here.
    # The default value is Issuer (i.e. a locally namespaced Issuer)
    kind: ClusterIssuer
    group: cert-manager.io
{{- end }}