apiVersion: external-secrets.io/v1beta1
kind: ExternalSecret
metadata:
  name: app-secrets-external-secret
  labels:
    {{- include "common.labels" . | nindent 4 }}
spec:
  refreshInterval: 10s
  secretStoreRef:
    name: aws-cluster-store
    kind: ClusterSecretStore
  target:
    name: app-secrets
    creationPolicy: Owner
  dataFrom:
  - extract:
      key: "{{ .Values.secrets.externalSecret.aws.prefix }}/app-secrets"