apiVersion: apps/v1
kind: Deployment
metadata:
  name: cakto-backend-3ds-api
  labels:
    {{- include "common.labels" . | nindent 4 }}
    app.kubernetes.io/name: cakto-backend-3ds-api
spec:
  selector:
    matchLabels:
      app.kubernetes.io/name: cakto-backend-3ds-api
  template:
    metadata:
      labels:
        {{- include "common.labels" . | nindent 8 }}
        app.kubernetes.io/name: cakto-backend-3ds-api
        {{- with .Values.threeds_api.container.labels }}
          {{- toYaml . | nindent 8 }}
        {{- end }}
      {{- with .Values.threeds_api.container.annotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
    spec:
      containers:
      - name: cakto-backend-3ds-api
        image: "{{ .Values.backend.container.image }}:{{ .Values.backend.container.tag }}"
        imagePullPolicy: IfNotPresent
        {{- with .Values.threeds_api.container.resources }}
        resources:
          {{- toYaml . | nindent 10 }}
        {{- end }}
        envFrom:
        - secretRef:
            name: app-secrets
        env:
          - name: BACKEND_APP
            value: api
        {{- with .Values.threeds_api.container.extra_envs }}
          {{- toYaml . | nindent 10 }}
        {{- end }}
        ports:
        - containerPort: 8000
          name: http
        {{- with .Values.threeds_api.container.readinessProbe }}
        readinessProbe:
          {{- toYaml . | nindent 10 }}
        {{- end }}
        {{- with .Values.threeds_api.container.livenessProbe }}
        livenessProbe:
          {{- toYaml . | nindent 10 }}
        {{- end }}
        {{- with .Values.threeds_api.container.startupProbe }}
        startupProbe:
          {{- toYaml . | nindent 10 }}
        {{- end }}