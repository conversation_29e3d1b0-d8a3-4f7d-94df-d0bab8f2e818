apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: cakto-backend-3ds-api-hpa
  labels:
    {{- include "common.labels" . | nindent 4 }}
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: cakto-backend-3ds-api
  minReplicas: {{ .Values.threeds_api.hpa.minReplicas }}
  maxReplicas: {{ .Values.threeds_api.hpa.maxReplicas }}
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: {{ .Values.threeds_api.hpa.averageUtilization }}