apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: cakto-backend-3ds-api
  labels:
    {{- include "common.labels" . | nindent 4 }}
  {{- with .Values.threeds_api.ingress.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  {{- if (default false .Values.backend.ingress.useTls) }}
  tls:
    - hosts:
      - {{.Values.backend.ingress.host}}
      secretName: main-api-tls
  {{- end }}
  ingressClassName: nginx
  rules:
    - host: {{ .Values.backend.ingress.host }}
      http:
        paths:
          - pathType: Prefix
            path: /api/financial/cielo/3ds/token
            backend:
              service:
                name: cakto-backend-3ds-api
                port:
                  number: 80