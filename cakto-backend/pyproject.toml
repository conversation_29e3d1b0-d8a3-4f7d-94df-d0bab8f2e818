[tool.poetry]
name = "cakto-backend"
version = "0.1.0"
description = ""
authors = ["Your Name <<EMAIL>>"]
readme = "README.md"
package-mode = false

[tool.poetry.dependencies]
python = ">=3.11,<3.12"
google-cloud-recaptcha-enterprise = "1.20.0"
aiohttp = "3.9.3"
aiosignal = "1.3.1"
asgiref = "3.7.2"
async-timeout = "4.0.3"
attrs = "23.1.0"
autobahn = "23.6.2"
automat = "22.10.0"
babel = "2.14.0"
boto3 = "1.33.9"
botocore = "1.33.9"
cachecontrol = "0.13.1"
cachetools = "5.3.2"
certifi = "2023.7.22"
cffi = "1.15.1"
charset-normalizer = "3.2.0"
click = "8.1.7"
colorama = "0.4.6"
constantly = "15.1.0"
crontab = "1.0.1"
cryptography = "41.0.4"
curlify = "2.2.1"
daphne = "4.0.0"
defusedxml = "0.7.1"
dj-rest-auth = "5.0.1"
django = "4.2.5"
django-allauth = "0.57.0"
django-cors-headers = "4.2.0"
django-debug-toolbar = "4.3.0"
django-prometheus = "2.3.1"
django-redis = "5.4.0"
django-route-decorator = "0.1.1"
django-rq = "2.10.1"
django-storages = "1.14.2"
djangorestframework = "3.14.0"
djangorestframework-csv = "2.1.1"
djangorestframework-simplejwt = "5.3.0"
dnspython = "2.6.1"
drf-excel = "2.4.0"
enum34 = "1.1.10"
et-xmlfile = "1.1.0"
facebook-business = "19.0.0"
firebase-admin = "6.2.0"
freezegun = "1.4.0"
frozenlist = "1.4.1"
google-api-core = "2.13.0"
google-api-python-client = "2.107.0"
google-auth = "2.23.4"
google-auth-httplib2 = "0.1.1"
google-cloud-core = "2.3.3"
google-cloud-firestore = "2.13.1"
google-cloud-storage = "2.13.0"
google-crc32c = "1.5.0"
google-resumable-media = "2.6.0"
googleapis-common-protos = "1.61.0"
grpcio = "1.59.2"
grpcio-status = "1.59.2"
gunicorn = "21.2.0"
h11 = "0.14.0"
httplib2 = "0.22.0"
hyperlink = "21.0.0"
idna = "3.4"
incremental = "22.10.0"
iniconfig = "2.0.0"
jmespath = "1.0.1"
msgpack = "1.0.7"
multidict = "6.0.5"
oauthlib = "3.2.2"
openpyxl = "3.1.2"
packaging = "23.1"
pillow = "10.0.1"
prometheus-client = "0.20.0"
proto-plus = "1.22.3"
protobuf = "4.25.0"
psycopg2 = "2.9.7"
pyasn1 = "0.5.0"
pyasn1-modules = "0.3.0"
pycountry = "23.12.11"
pycparser = "2.21"
pyjwt = "2.8.0"
pyopenssl = "23.2.0"
pyparsing = "3.1.1"
pypng = "0.20220715.0"
python-dateutil = "2.8.2"
python-dotenv = "1.0.0"
python3-openid = "3.2.0"
pytz = "2023.3.post1"
qrcode = "7.4.2"
redis = "5.0.1"
requests = "2.31.0"
requests-oauthlib = "1.3.1"
rq = "1.15.1"
rq-exporter = "2.1.2"
rq-scheduler = "0.13.1"
rsa = "4.9"
s3transfer = "0.8.2"
service-identity = "23.1.0"
shortuuid = "1.0.11"
six = "1.16.0"
sqlparse = "0.4.4"
twisted = "23.8.0"
txaio = "23.1.1"
types-requests = "2.31.0.10"
typing-extensions = "4.8.0"
tzdata = "2023.3"
unicodecsv = "0.14.1"
uritemplate = "4.1.1"
urllib3 = "2.0.5"
uvicorn = "0.23.2"
whitenoise = "6.5.0"
yarl = "1.9.4"
zope-interface = "6.0"
fastapi = "^0.111.0"
slowapi = "^0.1.9"
responses = "^0.25.3"
fakeredis = "^2.23.2"
django-filter = "^24.2"
django-lifecycle = "^1.2.4"
fcm-django = "^2.2.1"
django-apscheduler = "^0.6.2"
python-telegram-bot = "^21.6"
django-otp = "^1.5.4"
drf-flex-fields = "^1.0.2"
django-oauth-toolkit = "^3.0.1"
django-waffle = "^5.0.0"
django-phonenumber-field = {extras = ["phonenumbers"], version = "^8.1.0"}
django-ninja = "^1.4.3"

[tool.poetry.group.dev.dependencies]
flake8-pyproject = "^1.2.3"
flake8 = "^7.1.0"
model-bakery = "^1.20.0"
faker = "^33.1.0"
pytest-mock = "^3.14.0"
pytest-watch = "^4.2.0"
pytest-xdist = "^3.6.1"
pluggy = "^1.5.0"
pytest-asyncio = "^0.25.3"
pytest-subtests = "^0.14.1"
pytest-django = "^4.10.0"
django-extensions = "^4.1"

[tool.isort]
line_length = 120

[tool.mypy]
ignore_missing_imports = true
check_untyped_defs = true

[tool.pyright]
reportAttributeAccessIssue = false
reportIncompatibleMethodOverride = false
reportFunctionMemberAccess = false

[tool.flake8]
extend-ignore = ["E302", "W503"]
max-line-length = 200
interpreter = "C:\\python_projects\\cakto-backend\\venv\\Scripts\\python.exe"

[tool.autopep8]
max_line_length = 200
ignore = "E302,W503"

[tool.pytest.ini_options]
DJANGO_SETTINGS_MODULE = "cakto.settings_local"
python_files = [
    "test.py",
    "tests.py",
    "test_*.py",
    "tests_*.py",
    "*_test.py",
    "*_tests.py",
    ]
addopts = "--strict-markers -rP"
markers = ["slow: Run tests that are slow", "fast: Run fast tests"]
filterwarnings = [
    "ignore:.*pkg_resources\\.declare_namespace.*:DeprecationWarning",
    "ignore:.*pkg_resources is deprecated.*:DeprecationWarning",
    ]
asyncio_default_fixture_loop_scope = 'function'

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
