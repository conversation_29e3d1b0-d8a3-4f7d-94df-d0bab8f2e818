import hashlib
import json
import os
import time
from typing import Iterable, Sequence

from django.utils import timezone
from django_rq import job
from facebook_business.adobjects.adspixel import AdsPixel
from facebook_business.adobjects.serverside.action_source import ActionSource
from facebook_business.adobjects.serverside.content import Content
from facebook_business.adobjects.serverside.custom_data import CustomData
from facebook_business.adobjects.serverside.event import Event
from facebook_business.adobjects.serverside.event_request import EventRequest
from facebook_business.adobjects.serverside.event_response import EventResponse
from facebook_business.adobjects.serverside.user_data import UserData
from facebook_business.api import FacebookAdsApi
from facebook_business.exceptions import FacebookRequestError
from rq import Retry

from gateway.models import OfferType, Order
from product.models import Offer, Product
from product.pixel_strategies.base import PixelEvents, PixelProcessingError
from product.pixel_strategies.types import PaymentTriggerMapping
from user.models import User


class FacebookPixelEvents(PixelEvents):
    # doc: https://developers.facebook.com/docs/marketing-api/conversions-api/parameters

    def pix_gerado(self) -> dict:
        orders = self.kwargs['orders']
        order = next(order for order in orders if order.offer_type == 'main') or orders[0]

        totalAmount = float(sum(order.amount for order in orders))

        event_time = int(order.createdAt.timestamp())

        if self.tracking_pixels.fbPixPurchaseTrigger:
            amount = totalAmount * (self.tracking_pixels.fbPixConversionValue / 100)
            return self.purchase_approved(amount=amount, event_time=event_time)

        data = self.get_data(
            orders=orders,
            amount=totalAmount,
            event_name='pix_gerado',
            event_time=event_time,
        )
        res = self.send_event({'data': [json.dumps(data)]})

        return self.get_log_data(orders, [data], res)

    def boleto_gerado(self) -> dict:
        orders = self.kwargs['orders']
        order = next(order for order in orders if order.offer_type == 'main') or orders[0]

        totalAmount = float(sum(order.amount for order in orders))

        event_time = int(order.createdAt.timestamp())

        if self.tracking_pixels.fbBoletoPurchaseTrigger:
            amount = totalAmount * (self.tracking_pixels.fbBoletoConversionValue / 100)
            return self.purchase_approved(amount=amount, event_time=event_time)

        data = self.get_data(
            orders=orders,
            amount=totalAmount,
            event_name='boleto_gerado',
            event_time=event_time,
        )
        res = self.send_event({'data': [json.dumps(data)]})

        return self.get_log_data(orders, [data], res)

    def picpay_gerado(self) -> dict:
        orders = self.kwargs['orders']
        order = next(order for order in orders if order.offer_type == 'main') or orders[0]

        totalAmount = float(sum(order.amount for order in orders))

        event_time = int(order.createdAt.timestamp())

        if self.tracking_pixels.fbPicpayPurchaseTrigger:
            amount = totalAmount * (self.tracking_pixels.fbPicpayConversionValue / 100)
            return self.purchase_approved(amount=amount, event_time=event_time)

        data = self.get_data(
            orders=orders,
            amount=totalAmount,
            event_name='picpay_gerado',
            event_time=event_time,
        )
        res = self.send_event({'data': [json.dumps(data)]})

        return self.get_log_data(orders, [data], res)

    def openfinance_nubank_gerado(self) -> dict:
        orders = self.kwargs['orders']
        order = next(order for order in orders if order.offer_type == 'main') or orders[0]

        totalAmount = float(sum(order.amount for order in orders))

        event_time = int(order.createdAt.timestamp())

        if self.tracking_pixels.fbNubankPurchaseTrigger:
            amount = totalAmount * (self.tracking_pixels.fbNubankConversionValue / 100)
            return self.purchase_approved(amount=amount, event_time=event_time)

        data = self.get_data(
            orders=orders,
            amount=totalAmount,
            event_name='openfinance_nubank_gerado',
            event_time=event_time,
        )
        res = self.send_event({'data': [json.dumps(data)]})

        return self.get_log_data(orders, [data], res)

    def purchase_approved(self, amount: float | None = None, event_time: int | None = None) -> dict:
        orders = self.kwargs['orders']
        order = next(order for order in orders if order.offer_type == 'main')

        paymentMethod = order.paymentMethodType

        event_triggered = self.get_already_triggered_event(order=order)
        if event_triggered:
            return {'event_sent': False, 'reason': f'Event already sent by {event_triggered}'}

        if not amount:
            amount = float(sum(order.amount for order in orders))

        if not event_time:
            event_time = int(order.paidAt.timestamp() if order.paidAt else timezone.now().timestamp())

        purchase_event_data = self.get_data(
            orders=orders,
            amount=amount,
            event_name='Purchase',
            event_time=event_time,
        )

        additional_event_data = self.get_additional_event_data(
            amount=amount,
            orders=orders,
            paymentMethod=paymentMethod,
            event_time=event_time,
        )

        params = {'data': [json.dumps(purchase_event_data)]}
        if additional_event_data:
            params['data'].append(json.dumps(additional_event_data))  # type:ignore

        print(params)
        res = self.send_event(params)

        return self.get_log_data(orders, params['data'], res)

    def initiate_checkout(self) -> dict:
        offer: Offer = self.kwargs['offer']
        pixelEventId = self.kwargs['pixelEventId']
        event_time = self.kwargs.get('event_time', int(timezone.now().timestamp()))

        email = (self.kwargs.get('email', '') or '').strip().lower()
        phone = (self.kwargs.get('phone', '') or '').strip().lower()
        phone = '55' + phone if phone and not phone.startswith('55') else phone

        customer_email = hashlib.sha256(email.encode()).hexdigest()
        customer_phone = hashlib.sha256(phone.encode()).hexdigest()

        customer_name = (self.kwargs.get('name', '') or '').split() or ['']

        customer_first_name = hashlib.sha256(customer_name[0].encode()).hexdigest()
        customer_last_name = hashlib.sha256((customer_name[-1] if len(customer_name) > 1 else '').encode()).hexdigest()

        data = {
            'action_source': 'website',
            'event_name': 'InitiateCheckout',
            'event_id': pixelEventId,
            'event_source_url': self.kwargs.get('checkoutUrl', '') or '',
            'referrer_url': self.kwargs.get('refererUrl', '') or '',
            'data_processing_options': [],
            'data_processing_options_country': 0,
            'event_time': event_time,
            'user_data': {
                'em': customer_email,
                'ph': customer_phone,
                'fn': customer_first_name,
                'ln': customer_last_name,
                'client_ip_address': self.kwargs.get('client_ip', '') or '',
                'client_user_agent': self.kwargs.get('client_user_agent', '') or '',
                'fbc': self.kwargs.get('fbc', '') or '',
                'fbp': self.kwargs.get('fbp', '') or '',
            },
            'custom_data': {
                'content_type': 'product',
                'content_ids': [offer.id],
                'contents': [{'id': offer.id, 'quantity': 1, 'item_price': float(offer.price)}],
                'currency': 'BRL',
                'value': float(offer.price),
            }
        }
        params = {'data': [json.dumps(data)]}

        res = self.send_event(params)
        res_data = getattr(res, '_data', {})
        event_sent = res_data.get('events_received', 0) > 0

        return {
            'event_sent': event_sent,
            'offer': offer.id,
            'apiToken_used': self.pixel.apiToken,
            'facebook_response': res_data,
            'payload_sended': params,
        }

    def get_data(self, orders: list[Order], amount, event_name, event_time: int) -> dict:
        order = next(order for order in orders if order.offer_type == 'main')

        customer = order.customer

        customer_email = self._hash_content(customer.email)

        customer_phone = self._hash_content(customer.phone or '')

        customer_first_name = self._hash_content(customer.name.split()[0])
        customer_last_name = self._hash_content((customer.name.split()[-1] if len(customer.name.split()) > 1 else ''))

        customer_birthDate = customer.birthDate.strftime('%Y%m%d') if customer.birthDate else ''
        customer_birthDate = self._hash_content(customer_birthDate)

        contents = [
            {'id': order.offer.id, 'quantity': 1, 'item_price': float(order.amount or order.offer.price)}
            for order in orders
        ]

        data = {
            'action_source': 'website',
            'event_id': order.id,
            'event_source_url': order.checkoutUrl,
            'referrer_url': order.refererUrl,
            'event_name': event_name,
            'event_time': event_time,
            'user_data': {
                'em': customer_email,
                'ph': customer_phone,
                'fn': customer_first_name,
                'ln': customer_last_name,
                'db': customer_birthDate,
                'external_id': customer.id,
                'client_ip_address': order.client_ip,
                'client_user_agent': order.client_user_agent,
                'fbc': self.kwargs.get('fbc', ''),
                'fbp': self.kwargs.get('fbp', ''),
            },
            'custom_data': {
                'content_type': 'product',
                'content_ids': [order.offer.id for order in orders],
                'contents': contents,
                'currency': 'BRL',
                'value': float(amount),
                'order_id': order.refId,
            }
        }

        return data

    def send_event(self, params):
        FacebookAdsApi.init(access_token=self.pixel.apiToken, timeout=2)
        try:
            return AdsPixel(self.pixel.pixelId).create_event(params=params)
        except FacebookRequestError as exception:
            self.process_pixel_errors(params, exception)

    def process_pixel_errors(self, params, exception):
        product = self.pixel.tracking_pixels.product
        err_code = exception._api_error_code

        if err_code == 190:
            self.set_pixel_error(
                error='Token de API inválido.',
                notification_title=f'Erro no pixel Facebook de {product.name[:20]}',
            )
        elif err_code == 100 and exception._api_error_subcode == 33:
            self.set_pixel_error(
                error='Pixel não encontrado. Verifique os dados do pixel.',
                notification_title=f'Erro no pixel Facebook de {product.name[:20]}',
            )
        else:
            self.raise_pixel_error(params, exception)

    def raise_pixel_error(self, payload_sended, facebook_exception):
        orders = self.kwargs.get('orders', [])
        payment = self.kwargs.get('payment')

        raise PixelProcessingError({
            'product_name': self.pixel.tracking_pixels.product.name,
            'product': self.pixel.tracking_pixels.product.pk,
            'pixel_id': self.pixel.pk,
            'tracking_pixel_id': self.pixel.tracking_pixels.pk,
            'orders': [order.refId for order in orders],
            'payment': payment.pk if payment else None,
            'apiToken_used': self.pixel.apiToken,
            'facebook_response': facebook_exception._api_error_message,
            'facebook_error_code': facebook_exception._api_error_code,
            'facebook_error_subcode': facebook_exception._api_error_subcode,
            'payload_sended': [json.loads(item) for item in payload_sended['data']],
        })

    def get_log_data(self, orders: Iterable[Order], data: list, res):
        res_data = getattr(res, '_data', {})
        event_sent = res_data.get('events_received', 0) > 0

        return {
            'event_sent': event_sent,
            'orders': [order.refId for order in orders],
            'payment': self.kwargs['payment'].pk,
            'apiToken_used': self.pixel.apiToken,
            'facebook_response': res_data,
            'payload_sended': data,
        }

    def get_already_triggered_event(self, order: Order) -> str | bool:
        payment_trigger_mapping: PaymentTriggerMapping = {
            'pix': (self.tracking_pixels.fbPixPurchaseTrigger, 'pix_gerado'),
            'openfinance_nubank': (self.tracking_pixels.fbNubankPurchaseTrigger, 'openfinance_nubank_gerado'),
            'picpay': (self.tracking_pixels.fbPicpayPurchaseTrigger, 'picpay_gerado'),
            'boleto': (self.tracking_pixels.fbBoletoPurchaseTrigger, 'boleto_gerado'),
        }

        return super()._get_already_triggered_event(order, payment_trigger_mapping)


class FacebookConversionEvent:
    def __init__(self, *args, **kwargs) -> None:
        access_token = os.getenv('FACEBOOK_ACCESS_TOKEN')
        self.pixel_id = os.getenv('FACEBOOK_PIXEL_ID')

        FacebookAdsApi.init(access_token=access_token)

    def _hash_data(self, data: str) -> str:
        return hashlib.sha256(data.strip().lower().encode('utf-8')).hexdigest()

    def _get_user_phone(self, user: User) -> str:
        return f"{user.phoneCountryCode or ''}{user.cellphone or ''}"

    def _get_user_data(
        self,
        user: User,
        user_agent: str = '',
        ip: str = '',
    ):
        return UserData(
            emails=[self._hash_data(user.email)],
            phones=[self._hash_data(self._get_user_phone(user))],
            client_ip_address=ip,
            client_user_agent=user_agent,
            external_id=str(user.id),
            first_names=[user.first_name],
            last_names=[user.last_name],
            fbc=user.fbc or '',
            fbp=user.fbp or '',
        )

    def _get_order_contents(self, orders):
        return [
            Content(
                product_id=order.offer.id,
                title=order.offer.name,
                quantity=1,
                item_price=float(order.amount or order.offer.price),
            )
            for order in orders
        ]

    def _get_main_order(self, orders: Sequence[Order]) -> Order:
        return next(
            order for order in orders
            if order.offer_type == OfferType.MAIN.id
            or order.offer_type == OfferType.UPSELL.id  # upsell is processes separately
        )

    def _send_event(self, events: list[Event]) -> EventResponse:
        return EventRequest(
            # test_event_code='TEST070707',
            events=events,
            pixel_id=self.pixel_id,  # type:ignore
        ).execute()

@job('cakto_pixel', retry=Retry(max=3, interval=10))
def send_login_event_to_fb(
    user,
    ip: str = '',
    user_agent: str = ''
) -> EventResponse:
    event_tracker = FacebookConversionEvent()

    user_data = event_tracker._get_user_data(user=user, user_agent=user_agent, ip=ip)

    event = Event(
        event_name='Login',
        event_time=int(time.time()),
        user_data=user_data,
        action_source=ActionSource.WEBSITE,
    )

    return event_tracker._send_event([event])

@job('cakto_pixel', retry=Retry(max=3, interval=10))
def send_register_event_to_fb(
    user: User,
    ip: str = '',
    user_agent: str = ''
) -> EventResponse:
    event_tracker = FacebookConversionEvent()

    user_data = event_tracker._get_user_data(user=user, user_agent=user_agent, ip=ip)

    event = Event(
        event_name='CompleteRegistration',
        event_time=int(time.time()),
        user_data=user_data,
        action_source=ActionSource.WEBSITE,
    )

    return event_tracker._send_event([event])

@job('cakto_pixel', retry=Retry(max=3, interval=10))
def send_purchase_event_to_fb(orders: Sequence[Order]) -> EventResponse:
    event_tracker = FacebookConversionEvent()

    main_order = event_tracker._get_main_order(orders) \
        if len(orders) > 1 else orders[0]

    user_data = event_tracker._get_user_data(user=main_order.product.user)

    contents = event_tracker._get_order_contents(orders)

    custom_data = CustomData(  # type:ignore
        value=sum(float(order.amount or order.offer.price) for order in orders),
        currency='BRL',
        content_name=main_order.offer.name,
        content_ids=[order.offer.id for order in orders],
        contents=contents,
        content_type='product',
        order_id=main_order.refId,
        num_items=len(orders),
        custom_properties=dict(
            baseAmount=sum(float(order.offer.price) for order in orders),
            discount=sum(float(order.discount or 0) for order in orders),
            fees=sum(float(order.fees or 0) for order in orders),
            couponCode=main_order.couponCode or '',
            offer_type=main_order.offer_type,
            paymentMethod=main_order.paymentMethodType,
            paymentStatus=main_order.status,
            installments=main_order.installments or 1,
            affiliateId=main_order.affiliate.id if main_order.affiliate else '',
        ),
    )

    event = Event(
        event_name='Purchase',
        event_time=int(time.time()),
        user_data=user_data,
        action_source=ActionSource.WEBSITE,
        custom_data=custom_data,
    )

    return event_tracker._send_event([event])

@job('cakto_pixel', retry=Retry(max=3, interval=10))
def send_create_product_to_fb(product: Product) -> EventResponse:
    event_tracker = FacebookConversionEvent()

    user_data = event_tracker._get_user_data(user=product.user)

    custom_data = CustomData(  # type:ignore
        value=float(product.price),
        currency='BRL',
        content_name=product.name,
        content_ids=[product.id],
        content_type='product',
        custom_properties=dict(
            description=product.description or '',
            productType=product.type or '',
            contentDeliveries=list(product.contentDeliveries.values_list('pk', flat=True)),
        ),
    )

    event = Event(
        event_name='ProductCreated',
        event_time=int(time.time()),
        user_data=user_data,
        action_source=ActionSource.WEBSITE,
        custom_data=custom_data,
    )

    return event_tracker._send_event([event])
