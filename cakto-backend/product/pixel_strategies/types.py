from typing import Iterable, TypedDict, Union

from apps.services.types import InitiateCheckoutEventKwargs
from gateway.models import Order, Payment
from product.models import FacebookPixel, TikTokPixel, TrackingPixels


class PixelPaymentEventKwargs(TypedDict):
    orders: Iterable[Order]
    payment: Payment
    tracking_pixels: TrackingPixels
    pixel: Union[FacebookPixel, TikTokPixel]

class PixelInitiateCheckoutEventKwargs(InitiateCheckoutEventKwargs):
    tracking_pixels: TrackingPixels
    pixel: Union[FacebookPixel, TikTokPixel]

class PaymentTriggerMapping(TypedDict, total=False):
    # paymentMethod: (trigger attr, event_name)
    pix: tuple[bool, str]
    openfinance_nubank: tuple[bool, str]
    picpay: tuple[bool, str]
    boleto: tuple[bool, str]
