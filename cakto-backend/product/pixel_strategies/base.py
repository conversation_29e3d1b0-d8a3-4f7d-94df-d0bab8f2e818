import hashlib
import pprint
from abc import ABC, abstractmethod
from typing import Literal, Union, Unpack, overload

from gateway.models import Order
from product.models import FacebookPixel, TikTokPixel, TrackingPixels
from product.pixel_strategies.types import (PaymentTriggerMapping, PixelInitiateCheckoutEventKwargs,
                                            PixelPaymentEventKwargs)
from user.notification import send_notification


class PixelProcessingError(Exception):
    def __init__(self, message):
        super().__init__(pprint.pformat(message, sort_dicts=False, width=200))

class PixelEventsABC(ABC):
    @abstractmethod
    def boleto_gerado(self) -> dict:
        ...

    @abstractmethod
    def pix_gerado(self) -> dict:
        ...

    @abstractmethod
    def picpay_gerado(self) -> dict:
        ...

    @abstractmethod
    def openfinance_nubank_gerado(self) -> dict:
        ...

    @abstractmethod
    def purchase_approved(self) -> dict:
        ...

    @abstractmethod
    def initiate_checkout(self) -> dict:
        ...

class PixelEventsBase:
    @overload
    def __init__(self, *args, **kwargs: Unpack[PixelPaymentEventKwargs]) -> None: ...

    @overload
    def __init__(self, *args, **kwargs: Unpack[PixelInitiateCheckoutEventKwargs]) -> None: ...

    def __init__(self, *args, **kwargs) -> None:
        self.kwargs: dict = kwargs
        self.pixel: Union[FacebookPixel, TikTokPixel] = self.kwargs.get('pixel')
        self.tracking_pixels: TrackingPixels = self.kwargs.get('tracking_pixels')

    def check_prerequisites(self, **kwargs) -> bool:
        return bool(self.pixel.apiToken and not self.pixel.error)

    def get_additional_event_data(self, amount: float, orders: list[Order], paymentMethod: str, event_time: int) -> dict:
        event_names = (
            'credit_card', 'boleto', 'pix', 'picpay', 'openfinance_nubank',
            'googlepay', 'applepay'
        )

        if paymentMethod not in event_names:
            return {}

        return self.get_data(
            orders=orders,
            amount=amount,
            event_name=paymentMethod,
            event_time=event_time,
        )

    def set_pixel_error(
        self,
        error: str,
        notification_title: str,
        notification_message: str = 'Verifique as configurações de pixel do produto.',
    ) -> None:
        self.pixel.error = error
        self.pixel.save()

        product = self.tracking_pixels.product
        send_notification(
            product.user,
            notification_title,
            notification_message,
        )

    def _get_already_triggered_event(
        self,
        order: Order,
        payment_trigger_mapping: PaymentTriggerMapping,
    ) -> str | Literal[False]:
        """
        Check if the payment event was already sent.
        Returns:
            (str) the event name if triggered or (False).
        """
        orderPaid = (order.status == 'paid')

        # If the order is not paid, means that the event is "purchase_approved"
        # and was triggered by a "_gerado" event.
        if orderPaid is False:
            return False

        order_paymentMethod = order.paymentMethodType

        if payment_trigger_mapping.get(order_paymentMethod, (False, False))[0]:
            return payment_trigger_mapping[order_paymentMethod][1]

        return False

    @staticmethod
    def _hash_content(value: str) -> str:
        return hashlib.sha256(value.strip().lower().encode()).hexdigest()

class PixelEvents(PixelEventsABC, PixelEventsBase):
    ...
