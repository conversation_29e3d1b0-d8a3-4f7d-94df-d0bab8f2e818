import re
from typing import Iterable

import requests
from django.utils import timezone
from rest_framework import status

from gateway.models import Order
from product.pixel_strategies.base import PaymentTriggerMapping, PixelEvents, PixelProcessingError
from user.utils import get_or_create_user

TIKTOK_BASE_URL = "https://business-api.tiktok.com/open_api/v1.3/event/track/"

class TikTokPixelEvents(PixelEvents):
    # doc: https://business-api.tiktok.com/portal/docs?id=1771100865818625

    def pix_gerado(self) -> dict:
        orders = self.kwargs['orders']
        order = next(order for order in orders if order.offer_type == 'main') or orders[0]

        totalAmount = float(sum(order.amount for order in orders))

        event_time = int(order.createdAt.timestamp())

        if self.tracking_pixels.tiktokPixPurchaseTrigger:
            amount = totalAmount * (self.tracking_pixels.tiktokPixConversionValue / 100)
            return self.purchase_approved(amount=amount, event_time=event_time)

        data = self.get_data(
            orders=orders,
            amount=totalAmount,
            event_time=event_time,
            event_name='pix_gerado'
        )

        res = self.send_event([data])

        return self.get_log_data(orders, [data], res)

    def boleto_gerado(self) -> dict:
        orders = self.kwargs['orders']
        order = next(order for order in orders if order.offer_type == 'main') or orders[0]

        totalAmount = float(sum(order.amount for order in orders))

        event_time = int(order.createdAt.timestamp())

        if self.tracking_pixels.tiktokBoletoPurchaseTrigger:
            amount = totalAmount * (self.tracking_pixels.tiktokBoletoConversionValue / 100)
            return self.purchase_approved(amount=amount, event_time=event_time)

        data = self.get_data(
            orders=orders,
            amount=totalAmount,
            event_time=event_time,
            event_name='boleto_gerado'
        )

        res = self.send_event([data])

        return self.get_log_data(orders, [data], res)

    def picpay_gerado(self) -> dict:
        orders = self.kwargs['orders']
        order = next(order for order in orders if order.offer_type == 'main') or orders[0]

        totalAmount = float(sum(order.amount for order in orders))

        event_time = int(order.createdAt.timestamp())

        if self.tracking_pixels.tiktokPicpayPurchaseTrigger:
            amount = totalAmount * (self.tracking_pixels.tiktokPicpayConversionValue / 100)
            return self.purchase_approved(amount=amount, event_time=event_time)

        data = self.get_data(
            orders=orders,
            amount=totalAmount,
            event_time=event_time,
            event_name='picpay_gerado'
        )
        res = self.send_event([data])

        return self.get_log_data(orders, [data], res)

    def openfinance_nubank_gerado(self) -> dict:
        orders = self.kwargs['orders']
        order = next(order for order in orders if order.offer_type == 'main') or orders[0]

        totalAmount = float(sum(order.amount for order in orders))

        event_time = int(order.createdAt.timestamp())

        if self.tracking_pixels.tiktokNubankPurchaseTrigger:
            amount = totalAmount * (self.tracking_pixels.tiktokNubankConversionValue / 100)
            return self.purchase_approved(amount=amount, event_time=event_time)

        data = self.get_data(
            orders=orders,
            amount=totalAmount,
            event_time=event_time,
            event_name='openfinance_nubank_gerado'
        )

        res = self.send_event([data])

        return self.get_log_data(orders, [data], res)

    def purchase_approved(self, amount: float | None = None, event_time: int | None = None) -> dict:
        orders = self.kwargs['orders']
        order = next(order for order in orders if order.offer_type == 'main') or orders[0]

        event_triggered = self.get_already_triggered_event(order=order)
        if event_triggered:
            return {'event_sent': False, 'reason': f'Event already sent by {event_triggered}'}

        paymentMethod = order.paymentMethodType

        if not amount:
            amount = float(sum(order.amount for order in orders))

        if not event_time:
            event_time = int(order.paidAt.timestamp() if order.paidAt else timezone.now().timestamp())

        purchase_event_data = self.get_data(
            orders=orders,
            amount=amount,
            event_name='CompletePayment',
            event_time=event_time,
        )

        additional_event_data = self.get_additional_event_data(
            amount=amount,
            orders=orders,
            paymentMethod=paymentMethod,
            event_time=event_time,
        )

        data = [purchase_event_data]

        if additional_event_data:
            data.append(additional_event_data)  # type:ignore

        res = self.send_event(data)

        return self.get_log_data(orders, data, res)

    def initiate_checkout(self) -> dict:
        # Payload helper https://business-api.tiktok.com/portal/docs?id=1797165504155649&lang=en
        # Supported Events https://business-api.tiktok.com/portal/docs?id=1771101186666498
        # Parameters https://business-api.tiktok.com/portal/docs?id=1771101151059969

        offer = self.kwargs['offer']
        product = self.kwargs['offer'].product

        event_time = self.kwargs.get('event_time') or int(timezone.now().timestamp())

        pixelEventId = self.kwargs['pixelEventId']

        email = self.kwargs.get('email') or ''
        phone = self.kwargs.get('phone') or ''

        customer_email = self._hash_content(email)
        customer_phone = self._hash_content(phone)

        customer_split_name = (self.kwargs.get('name') or '').split(' ')
        customer_first_name = self._hash_content(customer_split_name[0])
        customer_last_name = self._hash_content(customer_split_name[-1] if len(customer_split_name) > 1 else '')

        data = {
            'event': 'InitiateCheckout',
            'properties': {
                'content_ids': [offer.id],
                'value': float(offer.price),
                'currency': 'BRL',
                'content_type': 'product',
                'contents': [
                    {
                        'content_id': offer.id,
                        'content_name': product.name,
                        'content_category': product.category.name,
                        'price': float(offer.price),
                        'quantity': 1,
                    },
                ],
                'description': product.description,
            },
            'event_id': re.sub(r'[^0-9a-zA-Z]', '', pixelEventId),
            'event_time': event_time,
            'page': {
                'url': self.kwargs.get('checkoutUrl') or '',
                'referer': self.kwargs.get('refererUrl') or '',
            },
            'user': {
                'email': customer_email,
                'phone': customer_phone,
                'ip': self.kwargs.get('client_ip') or '',
                'user_agent': self.kwargs.get('client_user_agent') or '',
                'first_name': customer_first_name,
                'last_name': customer_last_name,
            },
        }

        res = self.send_event([data])

        return {
            'event_sent': status.is_success(res.status_code),
            'offer': offer.id,
            'apiToken_used': self.pixel.apiToken,
            'tiktok_response': res.json(),
            'payload_sended': data,
        }

    def get_data(self, orders: list[Order], amount: float, event_time: int, event_name: str) -> dict:
        # Payload helper https://business-api.tiktok.com/portal/docs?id=1797165504155649&lang=en
        # Supported Events https://business-api.tiktok.com/portal/docs?id=1771101186666498
        # Parameters https://business-api.tiktok.com/portal/docs?id=1771101151059969

        order = next(order for order in orders if order.offer_type == 'main') or orders[0]

        customer = order.customer

        customer_email = self._hash_content(customer.email)

        customer_phone = self._hash_content(customer.phone or '')

        customer_external_id = self._hash_content(str(get_or_create_user(customer.email).id))

        customer_split_name = customer.name.split(' ') if customer.name else ''
        customer_first_name = self._hash_content(customer_split_name[0])
        customer_last_name = self._hash_content(customer_split_name[-1] if len(customer_split_name) > 1 else '')

        contents = [
            {
                'content_id': order.offer.id,
                'content_name': order.product.name,
                'content_category': order.product.category.name if order.product.category else '',
                'price': float(order.amount or order.offer.price),
                'quantity': 1,
            }
            for order in orders
        ]

        data = {
            'event': event_name,
            'properties': {
                'content_ids': [order.offer.id for order in orders],
                'content_type': 'product',
                'contents': contents,
                'currency': 'BRL',
                'value': amount,
                'description': order.product.description,
            },
            'event_id': order.id,
            'event_time': event_time,
            'page': {
                'url': order.checkoutUrl or '',
                'referer': order.refererUrl or '',
            },
            'user': {
                'email': customer_email,
                'phone': customer_phone,
                'ip': order.client_ip or '',
                'user_agent': order.client_user_agent or '',
                'external_id': customer_external_id,
                'first_name': customer_first_name,
                'last_name': customer_last_name,
            },
        }

        return data

    def send_event(self, data: list[dict]) -> requests.Response:
        payload = {
            'event_source': 'web',
            'event_source_id': self.pixel.pixelId,
            'data': data
        }

        headers = {
            "Content-Type": "application/json",
            "Access-Token": self.pixel.apiToken
        }

        response = requests.post(TIKTOK_BASE_URL, json=payload, headers=headers)

        if not status.is_success(response.status_code):
            self.process_pixel_errors(data, response)

        return response

    def process_pixel_errors(self, data: list[dict], response: requests.Response) -> None:
        product = self.pixel.tracking_pixels.product

        if response.status_code == 401:
            self.set_pixel_error(
                error='Token de API inválido.',
                notification_title=f'Erro no pixel TikTok do {product.name[:30]}',
            )
        elif response.status_code == 404:
            self.set_pixel_error(
                error='Pixel não encontrado. Verifique os dados do pixel.',
                notification_title=f'Erro no pixel TikTok do {product.name[:30]}',
            )

        self.raise_pixel_error(data, response)

    def raise_pixel_error(self, payload_sended: list[dict], response: requests.Response) -> None:
        orders = self.kwargs.get('orders', [])
        payment = self.kwargs.get('payment')

        raise PixelProcessingError({
            'product_name': self.pixel.tracking_pixels.product.name,
            'product': self.pixel.tracking_pixels.product.pk,
            'pixel_id': self.pixel.pk,
            'tracking_pixel_id': self.pixel.tracking_pixels.pk,
            'orders': [order.refId for order in orders],
            'payment': payment.pk if payment else None,
            'apiToken_used': self.pixel.apiToken,
            'tiktok__response_status_code': response.status_code,
            'tiktok_response': response.content.decode(),
            'payload_sended': payload_sended,
        })

    def get_log_data(self, orders: Iterable[Order], data: list[dict], res: requests.Response) -> dict:
        return {
            'event_sent': status.is_success(res.status_code),
            'orders': [order.refId for order in orders],
            'payment': self.kwargs['payment'].pk,
            'apiToken_used': self.pixel.apiToken,
            'tiktok_response': res.json(),
            'payload_sended': data,
        }

    def get_already_triggered_event(self, order: Order) -> str | bool:
        payment_trigger_mapping: PaymentTriggerMapping = {
            'pix': (self.tracking_pixels.tiktokPixPurchaseTrigger, 'pix_gerado'),
            'openfinance_nubank': (self.tracking_pixels.tiktokNubankPurchaseTrigger, 'openfinance_nubank_gerado'),
            'picpay': (self.tracking_pixels.tiktokPicpayPurchaseTrigger, 'picpay_gerado'),
            'boleto': (self.tracking_pixels.tiktokBoletoPurchaseTrigger, 'boleto_gerado'),
        }

        return super()._get_already_triggered_event(order, payment_trigger_mapping)
