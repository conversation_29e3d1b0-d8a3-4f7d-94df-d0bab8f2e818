from django.utils import timezone
import hashlib
import json
from unittest import mock

from facebook_business.exceptions import FacebookRequestError

from cakto.tests.base import BaseTestCase
from gateway.models import Payment
from product.models import FacebookPixel, TrackingPixels
from product.pixel_strategies.base import PixelProcessingError
from product.pixel_strategies.facebook import FacebookPixelEvents
from product.pixel_strategies.types import PixelInitiateCheckoutEventKwargs

FB_URL = 'https://graph.facebook.com/v19.0/{pixel_id}/events?access_token={api_token}'


class TestFacebookPixelEvents(BaseTestCase):
    @classmethod
    def setUpTestData(cls):
        cls.create_paymentMethods()

        cls.order = cls.create_order()

        cls.product = cls.order.product

        cls.offer = cls.product.offers.filter(default=True).first()

        cls.payment = mock.Mock(spec=Payment)

        cls.pixel = mock.Mock(
            spec=FacebookPixel,
            apiToken='TokenMock',
            pixelId='PixelIdMock'
        )

        cls.tracking_pixels = cls.get_tracking_pixel_mock(product=cls.product)

        cls.pixel.tracking_pixels = cls.tracking_pixels

        cls.event_class = FacebookPixelEvents(
            orders=[cls.order],
            payment=cls.payment,
            pixel=cls.pixel,
            tracking_pixels=cls.tracking_pixels,
        )

    @classmethod
    def get_tracking_pixel_mock(
        cls,
        fbPixPurchaseTrigger=False,
        fbPixConversionValue=100,
        fbBoletoPurchaseTrigger=False,
        fbBoletoConversionValue=100,
        fbPicpayPurchaseTrigger=False,
        fbPicpayConversionValue=100,
        fbNubankPurchaseTrigger=False,
        fbNubankConversionValue=100,
        product=None
    ):
        tracking_pixel = mock.MagicMock(spec=TrackingPixels)

        tracking_pixel.fbBoletoPurchaseTrigger = fbBoletoPurchaseTrigger
        tracking_pixel.fbBoletoConversionValue = fbBoletoConversionValue

        tracking_pixel.fbPixPurchaseTrigger = fbPixPurchaseTrigger
        tracking_pixel.fbPixConversionValue = fbPixConversionValue

        tracking_pixel.fbPicpayPurchaseTrigger = fbPicpayPurchaseTrigger
        tracking_pixel.fbPicpayConversionValue = fbPicpayConversionValue

        tracking_pixel.fbNubankPurchaseTrigger = fbNubankPurchaseTrigger
        tracking_pixel.fbNubankConversionValue = fbNubankConversionValue

        tracking_pixel.product = product

        return tracking_pixel

    @classmethod
    def get_initiate_checkout_kwargs(
        cls,
        pixel: FacebookPixel | None = None,
        tracking_pixels: TrackingPixels | None = None,
    ):

        _pixel = pixel  # type: ignore
        if _pixel is None:
            _pixel: FacebookPixel = cls.pixel

        _tracking_pixels = tracking_pixels  # type: ignore
        if _tracking_pixels is None:
            _tracking_pixels = cls.tracking_pixels

        kwargs: PixelInitiateCheckoutEventKwargs = {
            'offer': cls.offer,
            'client_ip': '*********',
            'client_user_agent': 'Mozilla/5.0',
            'pixelEventId': '**********',
            'checkoutUrl': 'https://www.example.com/checkout',
            'refererUrl': 'https://www.example.com',
            'fbc': 'fbc',
            'fbp': 'fbp',
            'email': '<EMAIL>',
            'phone': '*********',
            'name': 'Teste',
            'tracking_pixels': _tracking_pixels,
            'pixel': _pixel,
        }

        return kwargs

    def test_get_already_triggered_event(self):
        payment_trigger_mapping = {
            'pix': (self.tracking_pixels.fbPixPurchaseTrigger, 'pix_gerado'),
            'openfinance_nubank': (self.tracking_pixels.fbNubankPurchaseTrigger, 'openfinance_nubank_gerado'),
            'picpay': (self.tracking_pixels.fbPicpayPurchaseTrigger, 'picpay_gerado'),
            'boleto': (self.tracking_pixels.fbBoletoPurchaseTrigger, 'boleto_gerado'),
        }

        _get_already_triggered_event_mock = mock.patch(
            'product.pixel_strategies.facebook.PixelEvents._get_already_triggered_event'
        ).start()

        # Call
        self.event_class.get_already_triggered_event(self.order)

        _get_already_triggered_event_mock.assert_called_once_with(
            self.order,
            payment_trigger_mapping
        )

    def test_get_log_data_failed_response(self):
        fb_response_mock = self.get_response_mock(status=400)
        fb_response_mock._data = {'events_received': 0}

        data_mock = mock.Mock()

        expected_result = {
            'event_sent': False,
            'orders': [self.order.refId],
            'payment': self.payment.pk,
            'apiToken_used': self.pixel.apiToken,
            'facebook_response': fb_response_mock._data,
            'payload_sended': data_mock,
        }

        # Call
        result = self.event_class.get_log_data(
            [self.order],
            data_mock,
            fb_response_mock
        )

        # Assert
        self.assertEqual(result, expected_result)

    def test_get_log_data(self):
        fb_response_mock = self.get_response_mock(status=200)
        fb_response_mock._data = {'events_received': 1}

        data_mock = mock.Mock()

        expected_result = {
            'event_sent': True,
            'orders': [self.order.refId],
            'payment': self.payment.pk,
            'apiToken_used': self.pixel.apiToken,
            'facebook_response': fb_response_mock._data,
            'payload_sended': data_mock,
        }

        # Call
        result = self.event_class.get_log_data(
            [self.order],
            data_mock,
            fb_response_mock
        )

        # Assert
        self.assertEqual(result, expected_result)

    def test_raise_pixel_error(self):
        payload_sended = {'data': [json.dumps({}), json.dumps({})]}

        fb_exception_mock = mock.Mock(
            api_error_message='Error message',
            api_error_code=100,
            api_error_subcode=1,
        )

        with self.assertRaises(
            PixelProcessingError,
            msg={
                'product_name': self.tracking_pixels.product.name,
                'product': self.tracking_pixels.product.pk,
                'pixel_id': self.pixel.pk,
                'tracking_pixel_id': self.tracking_pixels.pk,
                'orders': [self.order.refId],
                'payment': self.payment.pk,
                'apiToken_used': self.pixel.apiToken,
                'facebook_response': fb_exception_mock._api_error_message,
                'facebook_error_code': fb_exception_mock._api_error_code,
                'facebook_error_subcode': fb_exception_mock._api_error_subcode,
                'payload_sended': payload_sended,
            }
        ):
            self.event_class.raise_pixel_error(payload_sended, fb_exception_mock)

    def test_process_pixel_errors_code_190(self):
        fb_exception_mock = mock.Mock(
            api_error_message='Error message',
            _api_error_code=190,
            _api_error_subcode=1,
        )

        set_pixel_error_mock = mock.patch(
            'product.pixel_strategies.facebook.FacebookPixelEvents.set_pixel_error'
        ).start()

        mock.patch(
            'product.pixel_strategies.facebook.FacebookPixelEvents.raise_pixel_error'
        ).start()

        # Call
        self.event_class.process_pixel_errors([], fb_exception_mock)

        # Assert
        set_pixel_error_mock.assert_called_once_with(
            error='Token de API inválido.',
            notification_title=f'Erro no pixel Facebook de {self.product.name[:20]}',
        )

    def test_process_pixel_errors_code_100(self):
        fb_exception_mock = mock.Mock(
            api_error_message='Error message',
            _api_error_code=100,
            _api_error_subcode=33,
        )

        set_pixel_error_mock = mock.patch(
            'product.pixel_strategies.facebook.FacebookPixelEvents.set_pixel_error'
        ).start()

        mock.patch(
            'product.pixel_strategies.facebook.FacebookPixelEvents.raise_pixel_error'
        ).start()

        # Call
        self.event_class.process_pixel_errors([], fb_exception_mock)

        # Assert
        set_pixel_error_mock.assert_called_once_with(
            error='Pixel não encontrado. Verifique os dados do pixel.',
            notification_title=f'Erro no pixel Facebook de {self.product.name[:20]}',
        )

    def test_process_pixel_errors_code_unespected_error_code(self):
        fb_exception_mock = mock.Mock(
            api_error_message='Error message',
            _api_error_code=200,
            _api_error_subcode=33,
        )

        set_pixel_error_mock = mock.patch(
            'product.pixel_strategies.facebook.FacebookPixelEvents.set_pixel_error'
        ).start()

        raise_pixel_error_mock = mock.patch(
            'product.pixel_strategies.facebook.FacebookPixelEvents.raise_pixel_error'
        ).start()

        # Call
        self.event_class.process_pixel_errors([], fb_exception_mock)

        # Assert
        set_pixel_error_mock.assert_not_called()
        raise_pixel_error_mock.assert_called_once_with([], fb_exception_mock)

    def test_send_event(self):
        fb_ads_api_mock = mock.patch(
            'product.pixel_strategies.facebook.FacebookAdsApi',
        ).start()

        ads_pixel_mock = mock.patch(
            'product.pixel_strategies.facebook.AdsPixel',
        ).start()

        data_mock = {'data': [json.dumps({}), json.dumps({})]}

        # Call
        self.event_class.send_event(data_mock)

        # Assert
        fb_ads_api_mock.init.assert_called_once_with(
            access_token=self.pixel.apiToken,
            timeout=2,
        )
        ads_pixel_mock.assert_called_once_with(self.pixel.pixelId)
        ads_pixel_mock.return_value.create_event.assert_called_once_with(params=data_mock)

    def test_send_event_failed_request(self):
        mock.patch(
            'product.pixel_strategies.facebook.FacebookAdsApi',
        ).start()

        ads_pixel_mock = mock.patch(
            'product.pixel_strategies.facebook.AdsPixel',
        ).start()
        ads_pixel_mock.side_effect = FacebookRequestError(
            message='Error message',
            request_context={},
            body={},
            http_status=400,
            http_headers={},
        )

        process_pixel_errors_mock = mock.patch(
            'product.pixel_strategies.facebook.FacebookPixelEvents.process_pixel_errors'
        ).start()

        data_mock = mock.Mock()

        # Call
        self.event_class.send_event(data_mock)

        # Assert
        process_pixel_errors_mock.assert_called_once_with(
            data_mock,
            ads_pixel_mock.side_effect
        )

    def test_get_data(self):
        order = self.order

        customer_email = hashlib.sha256(order.customer.email.strip().lower().encode()).hexdigest()
        customer_phone = hashlib.sha256(order.customer.phone.strip().lower().encode()).hexdigest()

        customer_first_name = hashlib.sha256(order.customer.name.split()[0].lower().encode()).hexdigest()
        customer_last_name = hashlib.sha256((order.customer.name.split()[-1].lower() if len(order.customer.name.split()) > 1 else '').encode()).hexdigest()

        customer_birthDate = order.customer.birthDate.strftime('%Y%m%d') if order.customer.birthDate else ''
        customer_birthDate = hashlib.sha256(customer_birthDate.encode()).hexdigest()

        # Convert createdAt to local time
        event_time = timezone.localtime(order.createdAt).timestamp()

        event_name = 'pix_gerado'
        amount = 10

        expected_result = {
            'action_source': 'website',
            'event_id': order.id,
            'event_source_url': order.checkoutUrl,
            'referrer_url': order.refererUrl,
            'event_name': event_name,
            'event_time': int(event_time),
            'user_data': {
                'em': customer_email,
                'ph': customer_phone,
                'fn': customer_first_name,
                'ln': customer_last_name,
                'db': customer_birthDate,
                'external_id': order.customer.id,
                'client_ip_address': order.client_ip,
                'client_user_agent': order.client_user_agent,
                'fbc': self.event_class.kwargs.get('fbc', ''),
                'fbp': self.event_class.kwargs.get('fbp', ''),
            },
            'custom_data': {
                'content_type': 'product',
                'content_ids': [order.offer.id],
                'contents': [{'id': order.offer.id, 'quantity': 1, 'item_price': float(order.amount or 0)}],
                'currency': 'BRL',
                'value': float(amount),
                'order_id': order.refId,
            }
        }

        # Call
        result = self.event_class.get_data(
            orders=[self.order],
            amount=amount,
            event_time=int(event_time),
            event_name=event_name
        )

        # Assert
        self.assertEqual(result, expected_result)

    def get_initiate_checkout_data(
        self,
        kwargs: PixelInitiateCheckoutEventKwargs | None = None
    ):
        if not kwargs:
            kwargs = self.get_initiate_checkout_kwargs(
                pixel=self.pixel,
                tracking_pixels=self.tracking_pixels
            )

        offer = kwargs['offer']
        pixelEventId = kwargs['pixelEventId']

        email = (kwargs.get('email', '') or '').strip().lower()
        phone = (kwargs.get('phone', '') or '').strip().lower()
        phone = '55' + phone if phone and not phone.startswith('55') else phone

        customer_email = hashlib.sha256(email.encode()).hexdigest()
        customer_phone = hashlib.sha256(phone.encode()).hexdigest()

        customer_name = (kwargs.get('name', '') or '').split() or ['']

        customer_first_name = hashlib.sha256(customer_name[0].encode()).hexdigest()
        customer_last_name = hashlib.sha256((customer_name[-1] if len(customer_name) > 1 else '').encode()).hexdigest()

        expected_data = {
            'action_source': 'website',
            'event_name': 'InitiateCheckout',
            'event_id': pixelEventId,
            'event_source_url': kwargs.get('checkoutUrl', '') or '',
            'referrer_url': kwargs.get('refererUrl', '') or '',
            'data_processing_options': [],
            'data_processing_options_country': 0,
            'event_time': int(timezone.now().timestamp()),
            'user_data': {
                'em': customer_email,
                'ph': customer_phone,
                'fn': customer_first_name,
                'ln': customer_last_name,
                'client_ip_address': kwargs.get('client_ip', '') or '',
                'client_user_agent': kwargs.get('client_user_agent', '') or '',
                'fbc': kwargs.get('fbc', '') or '',
                'fbp': kwargs.get('fbp', '') or '',
            },
            'custom_data': {
                'content_type': 'product',
                'content_ids': [offer.id],
                'contents': [{'id': offer.id, 'quantity': 1, 'item_price': float(offer.price)}],
                'currency': 'BRL',
                'value': float(offer.price),
            }
        }

        return expected_data

    def test_initiate_checkout(self):
        send_event_mock = mock.patch(
            'product.pixel_strategies.facebook.FacebookPixelEvents.send_event',
            return_value=mock.Mock(_data={'events_received': 1})
        ).start()

        kwargs = self.get_initiate_checkout_kwargs()

        data = self.get_initiate_checkout_data(kwargs=kwargs)

        self.event_class = FacebookPixelEvents(**kwargs)

        expected_result = {
            'event_sent': True,
            'offer': self.offer.id,
            'apiToken_used': self.pixel.apiToken,
            'facebook_response': send_event_mock.return_value._data,
            'payload_sended': {'data': [json.dumps(data)]},
        }

        # Call
        result = self.event_class.initiate_checkout()

        # Assert
        for key in expected_result:
            with self.subTest(key=key):
                self.assertEqual(result[key], expected_result[key])

    def test_initiate_checkout_with_failed_request(self):
        mock.patch(
            'product.pixel_strategies.facebook.FacebookPixelEvents.send_event',
            return_value=mock.Mock(_data={'events_received': 0})
        ).start()

        kwargs = self.get_initiate_checkout_kwargs()

        self.event_class = FacebookPixelEvents(**kwargs)

        # Call
        result = self.event_class.initiate_checkout()

        # Assert
        self.assertFalse(result['event_sent'])

    def test_purchase_approved(self):
        get_data_mock = mock.patch(
            'product.pixel_strategies.facebook.FacebookPixelEvents.get_data',
            return_value={'data': 'testing_facebook'}
        ).start()

        get_additional_event_data_mock = mock.patch(
            'product.pixel_strategies.facebook.FacebookPixelEvents.get_additional_event_data',
            return_value={'data': 'testing_facebook'}
        ).start()

        send_event_mock = mock.patch(
            'product.pixel_strategies.facebook.FacebookPixelEvents.send_event',
        ).start()

        get_log_data_mock = mock.patch(
            'product.pixel_strategies.facebook.FacebookPixelEvents.get_log_data'
        ).start()

        # Call
        self.event_class.purchase_approved()

        # Assert
        get_log_data_mock.assert_called_once_with(
            [self.order],
            [json.dumps(get_data_mock.return_value),
             json.dumps(get_additional_event_data_mock.return_value)],
            send_event_mock.return_value,
        )

    def test_purchase_approved_amount_with_amount_on_call(self):
        expected_amount = 199_99
        self.order.amount = 55  # another value
        self.order.save(update_fields=['amount'])

        get_data_mock = mock.patch(
            'product.pixel_strategies.facebook.FacebookPixelEvents.get_data',
            return_value={'data': 'testing_facebook'}
        ).start()

        mock.patch(
            'product.pixel_strategies.facebook.FacebookPixelEvents.get_additional_event_data',
            return_value={'data': 'testing_facebook'}
        ).start()

        mock.patch(
            'product.pixel_strategies.facebook.FacebookPixelEvents.send_event',
        ).start()

        mock.patch(
            'product.pixel_strategies.facebook.FacebookPixelEvents.get_log_data'
        ).start()

        # Call
        self.event_class.purchase_approved(amount=expected_amount)

        # Assert
        get_data_mock.assert_called_once_with(
            orders=[self.order],
            amount=expected_amount,
            event_name='Purchase',
            event_time=mock.ANY
        )

    def test_purchase_approved_amount_without_amount_on_call(self):
        expected_amount = 199_99
        self.order.amount = expected_amount
        self.order.save(update_fields=['amount'])

        get_data_mock = mock.patch(
            'product.pixel_strategies.facebook.FacebookPixelEvents.get_data',
            return_value={'data': 'testing_facebook'}
        ).start()

        mock.patch(
            'product.pixel_strategies.facebook.FacebookPixelEvents.get_additional_event_data',
            return_value={'data': 'testing_facebook'}
        ).start()

        mock.patch(
            'product.pixel_strategies.facebook.FacebookPixelEvents.send_event',
        ).start()

        mock.patch(
            'product.pixel_strategies.facebook.FacebookPixelEvents.get_log_data'
        ).start()

        # Call
        self.event_class.purchase_approved()

        # Assert
        get_data_mock.assert_called_once_with(
            orders=[self.order],
            amount=expected_amount,
            event_name='Purchase',
            event_time=mock.ANY
        )

    def test_purchase_approved_event_time_with_event_time_on_call(self):
        now = timezone.now()
        expected_time = int(now.timestamp())
        self.order.paidAt = now - timezone.timedelta(days=1)
        self.order.save(update_fields=['paidAt'])

        get_data_mock = mock.patch(
            'product.pixel_strategies.facebook.FacebookPixelEvents.get_data',
            return_value={'data': 'testing_facebook'}
        ).start()

        mock.patch(
            'product.pixel_strategies.facebook.FacebookPixelEvents.get_additional_event_data',
            return_value={'data': 'testing_facebook'}
        ).start()

        mock.patch(
            'product.pixel_strategies.facebook.FacebookPixelEvents.send_event',
        ).start()

        mock.patch(
            'product.pixel_strategies.facebook.FacebookPixelEvents.get_log_data'
        ).start()

        # Call
        self.event_class.purchase_approved(event_time=expected_time)

        # Assert
        get_data_mock.assert_called_once_with(
            orders=[self.order],
            amount=mock.ANY,
            event_name='Purchase',
            event_time=expected_time
        )

    def test_purchase_approved_event_time_without_event_time_on_call(self):
        now = timezone.now()
        expected_time = int(now.timestamp())
        self.order.paidAt = now
        self.order.save(update_fields=['paidAt'])

        get_data_mock = mock.patch(
            'product.pixel_strategies.facebook.FacebookPixelEvents.get_data',
            return_value={'data': 'testing_facebook'}
        ).start()

        mock.patch(
            'product.pixel_strategies.facebook.FacebookPixelEvents.get_additional_event_data',
            return_value={'data': 'testing_facebook'}
        ).start()

        mock.patch(
            'product.pixel_strategies.facebook.FacebookPixelEvents.send_event',
        ).start()

        mock.patch(
            'product.pixel_strategies.facebook.FacebookPixelEvents.get_log_data'
        ).start()

        # Call
        self.event_class.purchase_approved()

        # Assert
        get_data_mock.assert_called_once_with(
            orders=[self.order],
            amount=mock.ANY,
            event_name='Purchase',
            event_time=expected_time
        )

    def test_purchase_approved_with_trigger_enabled(self):
        self.order.paymentMethod = self.pix
        self.order.status = 'paid'
        self.order.save(update_fields=['paymentMethod'])

        self.tracking_pixels.fbPixPurchaseTrigger = True

        self.event_class = FacebookPixelEvents(
            orders=[self.order],
            payment=self.payment,
            pixel=self.pixel,
            tracking_pixels=self.tracking_pixels,
        )

        # Call
        result = self.event_class.purchase_approved()

        # Assert
        self.assertEqual(
            result,
            {
                'event_sent': False,
                'reason': 'Event already sent by pix_gerado'
            }
        )

    def test_openfinance_nubank_gerado(self):
        self.tracking_pixels.fbNubankPurchaseTrigger = True

        purchase_approved_mock = mock.patch(
            'product.pixel_strategies.facebook.FacebookPixelEvents.purchase_approved'
        ).start()

        # Call
        result = self.event_class.openfinance_nubank_gerado()

        # Assert
        self.assertEqual(
            result,
            purchase_approved_mock.return_value
        )

    def test_openfinance_nubank_gerado_with_trigger_disabled(self):
        self.tracking_pixels.fbNubankPurchaseTrigger = False

        get_data_mock = mock.patch(
            'product.pixel_strategies.facebook.FacebookPixelEvents.get_data',
            return_value={'data': 'testing_facebook'}
        ).start()

        send_event_mock = mock.patch(
            'product.pixel_strategies.facebook.FacebookPixelEvents.send_event',
        ).start()

        get_log_data_mock = mock.patch(
            'product.pixel_strategies.facebook.FacebookPixelEvents.get_log_data'
        ).start()

        # Call
        self.event_class.openfinance_nubank_gerado()

        # Assert
        get_log_data_mock.assert_called_once_with(
            [self.order],
            [get_data_mock.return_value],
            send_event_mock.return_value,
        )

    def test_picpay_gerado(self):
        self.tracking_pixels.fbPicpayPurchaseTrigger = True

        purchase_approved_mock = mock.patch(
            'product.pixel_strategies.facebook.FacebookPixelEvents.purchase_approved'
        ).start()

        # Call
        result = self.event_class.picpay_gerado()

        # Assert
        self.assertEqual(
            result,
            purchase_approved_mock.return_value
        )

    def test_picpay_gerado_with_trigger_disabled(self):
        self.tracking_pixels.fbPicpayPurchaseTrigger = False

        get_data_mock = mock.patch(
            'product.pixel_strategies.facebook.FacebookPixelEvents.get_data',
            return_value={'data': 'testing_facebook'}
        ).start()

        send_event_mock = mock.patch(
            'product.pixel_strategies.facebook.FacebookPixelEvents.send_event',
        ).start()

        get_log_data_mock = mock.patch(
            'product.pixel_strategies.facebook.FacebookPixelEvents.get_log_data'
        ).start()

        # Call
        self.event_class.picpay_gerado()

        # Assert
        get_log_data_mock.assert_called_once_with(
            [self.order],
            [get_data_mock.return_value],
            send_event_mock.return_value,
        )

    def test_boleto_gerado(self):
        self.tracking_pixels.fbBoletoPurchaseTrigger = True

        purchase_approved_mock = mock.patch(
            'product.pixel_strategies.facebook.FacebookPixelEvents.purchase_approved'
        ).start()

        # Call
        result = self.event_class.boleto_gerado()

        # Assert
        self.assertEqual(
            result,
            purchase_approved_mock.return_value
        )

    def test_boleto_gerado_with_trigger_disabled(self):
        self.tracking_pixels.fbBoletoPurchaseTrigger = False

        get_data_mock = mock.patch(
            'product.pixel_strategies.facebook.FacebookPixelEvents.get_data',
            return_value={'data': 'testing_facebook'}
        ).start()

        send_event_mock = mock.patch(
            'product.pixel_strategies.facebook.FacebookPixelEvents.send_event',
        ).start()

        get_log_data_mock = mock.patch(
            'product.pixel_strategies.facebook.FacebookPixelEvents.get_log_data'
        ).start()

        # Call
        self.event_class.boleto_gerado()

        # Assert
        get_log_data_mock.assert_called_once_with(
            [self.order],
            [get_data_mock.return_value],
            send_event_mock.return_value,
        )

    def test_pix_gerado(self):
        self.tracking_pixels.fbPixPurchaseTrigger = True

        purchase_approved_mock = mock.patch(
            'product.pixel_strategies.facebook.FacebookPixelEvents.purchase_approved'
        ).start()

        # Call
        result = self.event_class.pix_gerado()

        # Assert
        self.assertEqual(
            result,
            purchase_approved_mock.return_value
        )

    def test_pix_gerado_with_trigger_disabled(self):
        self.tracking_pixels.fbPixPurchaseTrigger = False

        get_data_mock = mock.patch(
            'product.pixel_strategies.facebook.FacebookPixelEvents.get_data',
            return_value={'data': 'testing_facebook'}
        ).start()

        send_event_mock = mock.patch(
            'product.pixel_strategies.facebook.FacebookPixelEvents.send_event',
        ).start()

        get_log_data_mock = mock.patch(
            'product.pixel_strategies.facebook.FacebookPixelEvents.get_log_data'
        ).start()

        # Call
        self.event_class.pix_gerado()

        # Assert
        get_log_data_mock.assert_called_once_with(
            [self.order],
            [get_data_mock.return_value],
            send_event_mock.return_value,
        )

    def test_pixel_with_many_orders(self):
        order_2 = self.create_order(product=self.product)

        self.event_class.kwargs['orders'].append(order_2)

        event_names = [
            'pix_gerado',
            'picpay_gerado',
            'boleto_gerado',
            'openfinance_nubank_gerado',
        ]

        mock.patch(
            'product.pixel_strategies.facebook.FacebookPixelEvents.send_event',
        ).start()

        mock.patch(
            'product.pixel_strategies.facebook.FacebookPixelEvents.get_log_data',
        ).start()

        mock.patch(
            'product.pixel_strategies.facebook.json',
        ).start()

        for event_name in event_names:
            with self.subTest(event_name=event_name):
                get_data_mock = mock.patch(
                    'product.pixel_strategies.facebook.FacebookPixelEvents.get_data',
                ).start()

                # Call
                getattr(self.event_class, event_name)()

                # Assert
                get_data_mock.assert_called_once_with(
                    orders=[self.order, order_2],
                    amount=float(self.order.amount + order_2.amount),  # type:ignore
                    event_name=event_name,
                    event_time=int(self.order.createdAt.timestamp()),
                )
