from unittest import mock

from django.utils import timezone

from cakto.tests.base import BaseTestCase
from gateway.models import Order, Payment
from product.models import FacebookPixel, TrackingPixels
from product.pixel_strategies.base import PaymentTriggerMapping, PixelEventsBase


class TestPixelEvents(BaseTestCase):
    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()

        cls.product = cls.create_product()

        cls.tracking_pixels = mock.Mock(
            spec=TrackingPixels,
            product=cls.product,
        )

        cls.pixel = mock.Mock(spec=FacebookPixel)

        cls.order = mock.Mock(spec=Order)

        cls.payment = mock.Mock(spec=Payment)

        cls.pixel_events = PixelEventsBase(
            orders=[cls.order],
            payment=cls.payment,
            pixel=cls.pixel,
            tracking_pixels=cls.tracking_pixels
        )

    def test_check_prequisites_with_pixel_error(self):
        self.pixel.apiToken = 'someApiToken'
        self.pixel.error = True
        self.assertFalse(self.pixel_events.check_prerequisites())

    def test_check_prequisites_without_pixel_error(self):
        self.pixel.apiToken = 'someApiToken'
        self.pixel.error = False
        self.assertTrue(self.pixel_events.check_prerequisites())

    def test_check_prequisites_without_api_token(self):
        self.pixel.apiToken = ''
        self.pixel.error = False
        self.assertFalse(self.pixel_events.check_prerequisites())

    def test_check_prequisites_without_api_token_and_with_pixel_error(self):
        self.pixel.apiToken = ''
        self.pixel.error = True
        self.assertFalse(self.pixel_events.check_prerequisites())

    def test_check_prequisites_true(self):
        self.pixel.apiToken = ''
        self.pixel.error = True
        self.assertFalse(self.pixel_events.check_prerequisites())

    def test_get_additional_event_data(self):
        amount = 10_000
        order = mock.Mock()

        payment_methods = [
            'credit_card', 'boleto', 'pix', 'picpay', 'openfinance_nubank',
            'googlepay', 'applepay'
        ]

        for paymentMethod in payment_methods:
            with self.subTest(paymentMethod=paymentMethod):
                get_data_mock = mock.Mock()
                self.pixel_events.get_data = get_data_mock

                event_time = int(timezone.now().timestamp())

                # Call
                self.pixel_events.get_additional_event_data(
                    orders=[order],
                    amount=amount,
                    paymentMethod=paymentMethod,
                    event_time=event_time
                )

                # Assert
                get_data_mock.assert_called_once_with(
                    orders=[order],
                    amount=amount,
                    event_name=paymentMethod,
                    event_time=event_time,
                )

    def test_get_additional_event_data_with_invalid_payment_method(self):
        amount = 10_000
        order = mock.Mock()
        paymentMethod = 'invalid_payment_method'

        get_data_mock = mock.Mock()
        self.pixel_events.get_data = get_data_mock

        # Call
        result = self.pixel_events.get_additional_event_data(
            amount,
            orders=[order],
            paymentMethod=paymentMethod,
            event_time=int(timezone.now().timestamp()),
        )

        # Assert
        get_data_mock.assert_not_called()

        self.assertEqual(result, {})

    def test_set_pixel_error(self):
        error = 'mocked-error'
        notification_title = 'mocked-notification-title'
        notification_message = 'mocked-notification-message'

        self.pixel.error = False
        self.pixel.save = save_mock = mock.Mock()

        send_notification_mock = mock.patch(
            'product.pixel_strategies.base.send_notification'
        ).start()

        # Call
        self.pixel_events.set_pixel_error(
            error,
            notification_title,
            notification_message
        )

        # Assert
        save_mock.assert_called_once()
        self.assertEqual(self.pixel.error, error)

        send_notification_mock.assert_called_once_with(
            self.pixel_events.tracking_pixels.product.user,
            notification_title,
            notification_message,
        )

    def test_get_already_triggered_event_with_order_not_paid(self):
        order = mock.Mock()
        order.status = 'otherStatusButPaid'

        # Call
        result = self.pixel_events._get_already_triggered_event(
            order,
            payment_trigger_mapping={'pix': (True, 'pix_gerado')}
        )

        # Assert
        self.assertFalse(result)

    def test_get_already_triggered_event_with_trigger_true(self):
        order = mock.Mock()
        order.status = 'paid'
        order.paymentMethodType = 'pix'

        payment_trigger_mapping: PaymentTriggerMapping = {
            'pix': (True, 'pix_gerado')
        }

        # Call
        result = self.pixel_events._get_already_triggered_event(
            order,
            payment_trigger_mapping
        )

        # Assert
        self.assertEqual(result, 'pix_gerado')

    def test_get_already_triggered_event_with_trigger_false(self):
        order = mock.Mock()
        order.status = 'paid'
        order.paymentMethodType = 'pix'

        payment_trigger_mapping: PaymentTriggerMapping = {
            'pix': (False, 'pix_gerado')
        }

        # Call
        result = self.pixel_events._get_already_triggered_event(
            order,
            payment_trigger_mapping
        )

        # Assert
        self.assertFalse(result)

    def test_get_already_triggered_event_with_paymentMethod_present_not_present_in_payment_trigger_mapping(self):
        order = mock.Mock()
        order.status = 'paid'
        order.paymentMethodType = 'notPresent'

        payment_trigger_mapping: PaymentTriggerMapping = {
            'pix': (True, 'pix_gerado')
        }

        # Call
        result = self.pixel_events._get_already_triggered_event(
            order,
            payment_trigger_mapping
        )

        # Assert
        self.assertFalse(result)
