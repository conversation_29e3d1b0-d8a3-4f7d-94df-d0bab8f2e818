import hashlib
import os
from decimal import ROUND_FLOOR, Decimal
from typing import Any
from urllib.parse import urlencode

import waffle
from django.conf import settings
from django.core.cache import cache
from django.db.models import (<PERSON>, Count, ExpressionWrapper, F, Float<PERSON>ield, Func, IntegerField, Prefetch, Q, Sum,
                              Value, When)
from django.db.models.functions import <PERSON><PERSON>ce, Greatest, Least
from django.shortcuts import get_object_or_404, redirect
from django.utils import timezone
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import filters, generics, status, views, viewsets
from rest_framework.decorators import action, api_view
from rest_framework.exceptions import ValidationError
from rest_framework.parsers import FormParser, JSONParser, MultiPartParser
from rest_framework.permissions import AllowAny, IsAdminUser
from rest_framework.response import Response
from rest_framework.views import APIView
from shortuuid.django_fields import ShortUUIDField

from cakto.permissions import CanBlockProduct
from gateway.models import Subscription
from gateway.utils import get_order_by_offer_and_fingerprint
from main import unpack_offer_id_and_checkout_id
from members.sdk.discord import Discord
from members.sdk.instagram import Instagram
from product.enums import AffiliateStatus, PaymentMethodStatus, ProductType
from product.filters import (AffiliateAdminFilter, CouponFilter, OfferFilter, ProductFilter, ProductShowcaseFilter,
                             ShowcaseOrderingFilter)
from product.models import (Affiliate, Category, Checkout, ContentDelivery, Coproduction, Coupon, DeliveryAccess,
                            FacebookPixel, GoogleAdsPixel, KwaiPixel, Link, Offer, OrderBump, OutbrainPixel,
                            PaymentMethod, PixelDomain, Product, ProductDelivery, ShowcaseEvent, TaboolaPixel,
                            TikTokPixel, TrackingPixels)
from product.pagination import LimitPagination, ProductShowcasePagination
from product.pixel_strategies.facebook import send_create_product_to_fb
from product.serializers import (AffiliateProductSerializer, AffiliateSerializer, CoproductionCreateSerializer,
                                 CoproductionRetrieveSerializer, CoproductionUpdateSerializer,
                                 CouponCreateUpdateSerializer, CouponPublicSerializer, CouponSerializer,
                                 DeliveryAccessPublicSerializer, DeliveryAccessSerializer,
                                 DigitalProductCreateSerializer, LinkSerializer, OfferSerializer,
                                 OrderBumpCreateSerializer, OrderBumpSerializer, OrderBumpUpdateSerializer,
                                 PhisicalProductCreateSerializer, PixelDomainSerializer, ProductAdminSerializer,
                                 ProductAdminSerializerFull, ProductAdminUpdateSerializer, ProductDeliveryOwnerSerializer,
                                 ProductPublicSerializer, ProductSerializer, ProductSerializerFull,
                                 ProductShowcaseSerializer, ShowcaseEventRegisterSerializer, ShowcaseEventSerializer,
                                 TrackingPixelSerializer)
from product.utils import (get_affiliate_cookie_info, get_coupon, get_is_user_affiliate, is_domain_with_cname_record,
                           send_offer_data_to_compliance_analysis, showcase_get_top_averages,
                           validate_coupon_with_dynamic_fees)
from system_log.utils import log_action
from user.models import User
from user.notification import send_notification
# Permissions
from oauth2_provider.contrib.rest_framework import OAuth2Authentication
from sso.permissions import TokenHasScopeIfOauth
from rest_framework.permissions import IsAuthenticated
from user.permissions import UserIsValidated, ScopePermission
from cakto.authentication import CustomJWTAuthentication
from rest_framework.authentication import TokenAuthentication
from members.sdk.members_v2_producer import sync_producer_to_members_v2

AFFILIATE_MAX_COMMISSION = 95

@api_view(['GET'])
def categories(request):
    categories = [category.serialize() for category in Category.objects.all()]

    return Response({
        'success': True,
        'categories': categories
    })

class OfferUpsellInstallmentsAPIView(views.APIView):
    permission_classes = [AllowAny]

    def get(self, request, *args, **kwargs):
        offer = Offer.objects.filter(id__iexact=self.kwargs['short_id'], status='active').select_related('product').first()
        order = get_order_by_offer_and_fingerprint(offer, request.query_params.get('fingerprint'))

        if not order or not offer:
            return Response({'detail': 'Erro ao validar identidade.'}, status=status.HTTP_401_UNAUTHORIZED)

        data = {
            'id': offer.id,
            'name': offer.name,
            'price': offer.price,
            'calculatedInstallments': offer.calculatedInstallments(),
            'paymentMethod': order.paymentMethodType,
        }

        return Response(data)

class PaymentMethodAPIView(views.APIView):
    permission_classes = [AllowAny]

    def get(self, request, *args, **kwargs):
        user = request.user if request.user.is_authenticated else None

        if user:
            return Response(PaymentMethod.get_serialized_payment_methods(user=user))

        payment_methods = PaymentMethod._get_cached_payment_methods() or []
        payment_methods = [
            method._serialize() for method in payment_methods
            if method.status in [PaymentMethodStatus.ACTIVE.id, PaymentMethodStatus.TESTER_USER_ACCESS.id]
            and method.type != 'threeDs'
        ]

        return Response(payment_methods)

class ContentDeliveryAPIView(views.APIView):
    permission_classes = [AllowAny]

    def get(self, request, *args, **kwargs):
        return Response(ContentDelivery.get_contentDeliveries())

class ProductAPI(viewsets.ModelViewSet):
    authentication_classes = [OAuth2Authentication, CustomJWTAuthentication, TokenAuthentication]
    permission_classes = [
        IsAuthenticated,
        UserIsValidated,
        ScopePermission,
        TokenHasScopeIfOauth
    ]
    required_scopes = ['offers']
    scope = 'products'
    pagination_class = LimitPagination
    filter_backends = [filters.SearchFilter, DjangoFilterBackend, filters.OrderingFilter]
    filterset_class = ProductFilter
    search_fields = ['name', 'short_id', 'id']
    ordering_fields = ['name', 'price', 'type', 'status', 'createdAt']

    def get_serializer(self, *args, **kwargs) -> Any:
        if self.action == 'create':
            if waffle.switch_is_active('phisical_product_create_serializer'):
                return PhisicalProductCreateSerializer(*args, **kwargs)
            return DigitalProductCreateSerializer(*args, **kwargs)

        return ProductSerializer(*args, **kwargs)

    def get_queryset(self):
        productionType = self.request.query_params.get('productionType', 'producer')  # type:ignore

        query_conditions = Q()

        if 'producer' in productionType.split(','):
            query_conditions |= Q(user=self.request.user)
        if 'coproducer' in productionType.split(','):
            query_conditions |= Q(coproductions__user=self.request.user)
        if 'affiliate' in productionType.split(','):
            query_conditions |= Q(affiliates__user=self.request.user)

        return Product.objects.filter(query_conditions).prefetch_related('paymentMethods')

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer.save(user=request.user)

        product: Product = serializer.instance

        # Payment Methods
        self.set_paymentMethods(product)

        # Content Delivery
        contentDelivery_types = serializer.validated_data.get('contentDeliveries', [])
        product.contentDeliveries.set(ContentDelivery.filter_contentDelivery_instances(contentDelivery_types))

        # Offers
        offer = Offer.objects.create(
            product=product,
            name=product.name,
            price=product.price,
            type=product.type,
            default=True,
        )

        # Checkout
        checkout = Checkout.objects.create(
            name="Checkout Principal",
            product=product,
            default=True
        )
        checkout.offers.add(offer)

        # Link
        Link.objects.create(
            name="Oferta Principal",
            product=product,
            offer=offer,
            checkout=checkout,
            type='checkout',
            url=Link.generate_link_url(offer=offer, checkout_id=str(checkout.id)),
        )

        # Tracking Pixel
        TrackingPixels.objects.create(product=product)

        send_create_product_to_fb.delay(product=product)

        if product.contentDelivery == 'cakto':
            sync_producer_to_members_v2.delay(user=request.user, product=product)

        product_serializer = ProductSerializerFull(
            product,
            context={'request': request},
        )
        return Response(product_serializer.data, status=status.HTTP_201_CREATED)

    def set_paymentMethods(self, product):
        paymentMethods = PaymentMethod.get_payment_methods(user=product.user)

        if product.type == ProductType.SUBSCRIPTION.id and paymentMethods is not None:
            paymentMethods = [method for method in paymentMethods if method.type in Subscription.get_supported_paymentMethodTypes()]

        product.paymentMethods.set(paymentMethods) if paymentMethods else None

class ProductRetrieveAPI(viewsets.ModelViewSet):
    scope = 'products'
    serializer_class = ProductSerializerFull

    def get_object(self):
        return get_object_or_404(
            Product.objects.filter(id=self.kwargs['pk'], user=self.request.user)
            .prefetch_related(
                'paymentMethods',
                'contentDeliveries',
                'checkouts',
                'links',
                Prefetch('pixels', TrackingPixels.objects.filter(affiliate__isnull=True)),
                Prefetch('offers', Offer.objects.filter(status__in=['active', 'disabled'])),
            )
        )

    def update(self, request, *args, **kwargs):
        product = self.get_object()
        data = request.data.copy()
        if product.additionalInfo.get('cardBlocked', False) and 'paymentMethods' in data:
            del data['paymentMethods']

        serializer = self.get_serializer(product, data=data)
        serializer.is_valid(raise_exception=True)
        serializer.save(user=request.user)

        send_offer_data_to_compliance_analysis(offers=product.offers.all())

        serializer = self.get_serializer(product, context={'request': request})
        return Response(serializer.data, status=status.HTTP_200_OK)

    def destroy(self, request, *args, **kwargs):
        product = self.get_object()
        product.status = 'deleted'
        product.save()
        return Response({"detail": "Produto removido com sucesso."}, status=status.HTTP_200_OK)

    @action(detail=True, methods=['post'])
    def duplicate(self, request, *args, **kwargs):
        product = self.get_object()
        contentDeliveries = list(product.contentDeliveries.all())
        paymentMethods = list(product.paymentMethods.all())
        checkouts_to_copy = list(product.checkouts.all())
        offers_to_copy = list(product.offers.all())
        links_to_copy = list(product.links.all())
        bumps_to_copy = list(product.bumps.all())
        tracking_pixel_to_copy = list(product.pixels.filter(affiliate__isnull=True))

        product = self.duplicate_product(product)

        checkouts = self.duplicate_checkouts(product, checkouts_to_copy)

        offers = self.duplicate_offers(product, offers_to_copy, checkouts)

        self.duplicate_links(product, links_to_copy, offers)

        self.duplicate_bumps(product, bumps_to_copy)

        self.duplicate_tracking_pixels(product, tracking_pixel_to_copy)

        product.contentDeliveries.set(contentDeliveries)
        product.paymentMethods.set(paymentMethods)

        return Response(self.get_serializer(product).data, status=status.HTTP_200_OK)

    def duplicate_tracking_pixels(self, product, tracking_pixel):
        if tracking_pixel:
            tracking_pixel = tracking_pixel[0]
            queries = []
            for model in [FacebookPixel, GoogleAdsPixel, TaboolaPixel, OutbrainPixel, TikTokPixel, KwaiPixel]:
                queries.append(model.objects.filter(tracking_pixels=tracking_pixel))  # type:ignore

            tracking_pixel.pk = None
            tracking_pixel.product = product
            tracking_pixel.save()

            for queryset in queries:
                for pixel in queryset:
                    pixel.pk = None
                    pixel.tracking_pixels = tracking_pixel
                    pixel.save()

    def duplicate_bumps(self, product, bumps_to_copy):
        for bump in bumps_to_copy:
            bump.pk = None
            bump.product = product
            bump.save()

    def duplicate_links(self, product, links_to_copy, offers) -> None:
        def gen_unique_short_id() -> str:
            short_id = ShortUUIDField(length=7, max_length=40)._generate_uuid()  # type:ignore
            if Link.objects.filter(shortId=short_id).exists():
                return self.gen_unique_short_id()
            return str(short_id)

        for link in links_to_copy:
            offer = next((offer for offer in offers if offer.old_offer_id == link.offer_id), None)
            if offer:
                link.url = Link.generate_link_url(offer)
            link.shortId = gen_unique_short_id()
            link.pk = None
            link.product = product
            link.offer = offer
            link.save()

    def duplicate_checkouts(self, product, checkouts_to_copy) -> list[Checkout]:
        checkouts: list[Checkout] = []
        for checkout in checkouts_to_copy:
            checkout.old_offers_id = checkout.offers.all().values_list('id', flat=True)
            checkout.pk = None
            checkout.product = product
            checkout.save()
            checkout.refresh_from_db()
            checkout.offers.set([])
            checkouts.append(checkout)
        return checkouts

    def duplicate_offers(self, product, offers_to_copy, checkouts) -> list[Offer]:
        new_offers: list[Offer] = []
        for offer in offers_to_copy:
            offer.old_offer_id = offer.id
            offer.id = None
            offer.product = product
            offer.save()
            offer.refresh_from_db()
            [checkout.offers.add(offer) for checkout in checkouts if offer.old_offer_id in checkout.old_offers_id]
            new_offers.append(offer)
        return new_offers

    def duplicate_product(self, product) -> Product:
        def gen_unique_short_id() -> str:
            short_id = ShortUUIDField(length=7, max_length=40)._generate_uuid()  # type:ignore
            if Product.objects.filter(short_id=short_id).exists():
                return self.gen_unique_short_id()
            return str(short_id)

        product.pk = None
        product.short_id = gen_unique_short_id()
        product.membersId = None
        product.name = f'{product.name} - Cópia'
        product.refundRequest = False
        product.scriptSuspicious = False
        product.blockReason = None
        product.statusAdmin = 'stand_by'
        product.additionalInfo = {}

        product.total_sales = 0
        product.total_sales_count = 0
        product.total_paid = 0
        product.total_paid_count = 0
        product.total_refunded = 0
        product.total_refunded_count = 0
        product.total_chargeback = 0
        product.total_chargeback_count = 0
        product.sales_30_days = 0
        product.sales_30_days_count = 0
        product.refunded_30_days = 0
        product.refunded_30_days_count = 0
        product.chargeback_30_days = 0
        product.chargeback_30_days_count = 0
        product.sales_7_days = 0
        product.sales_7_days_count = 0
        product.refunded_7_days = 0
        product.refunded_7_days_count = 0
        product.chargeback_7_days = 0
        product.chargeback_7_days_count = 0
        product.sales_24h = 0
        product.sales_24h_count = 0
        product.refunded_24h = 0
        product.refunded_24h_count = 0
        product.chargeback_24h = 0
        product.chargeback_24h_count = 0

        product.unblockedAt = None
        product.analisedAt = None
        product.blockedAt = None

        product.save()
        product.refresh_from_db()
        return product

class ProductPublicApi(generics.RetrieveAPIView):
    permission_classes = [AllowAny]
    serializer_class = ProductPublicSerializer
    queryset = Product.objects.filter(status='active')

class ProductAdminList(generics.ListAPIView):
    permission_classes = [IsAdminUser]
    serializer_class = ProductAdminSerializer
    queryset = Product.objects.all().select_related('user___company')
    filter_backends = [filters.SearchFilter, DjangoFilterBackend, filters.OrderingFilter]
    search_fields = [
        'id', 'short_id', 'offers__id', 'name', 'supportEmail', 'producerName', 'affiliateSupportEmail',
        'user__email', 'user__cpf', 'user__cnpj'
    ]
    filterset_class = ProductFilter
    ordering_fields = ['name', 'price', 'type', 'status', 'statusAdmin']

class ProductAdminAPI(viewsets.ModelViewSet):
    permission_classes = [IsAdminUser]
    serializer_class = ProductAdminSerializerFull

    def get_object(self, *args, **kwargs):
        return get_object_or_404(
            Product.objects.filter(pk=self.kwargs['product_pk'])
            .select_related('user', 'category', 'user___company')
            .prefetch_related('links', 'offers', 'links__offer', 'contentDeliveries', 'paymentMethods')
        )

    def update(self, request, *args, **kwargs):
        product = self.get_object()

        serializer = ProductAdminUpdateSerializer(
            product,
            data=request.data,
            partial=True
        )

        serializer.is_valid(raise_exception=True)
        serializer.save()

        return Response(self.get_serializer(product).data)

    @action(detail=True, methods=['post'])
    def approve(self, request, *args, **kwargs):
        product = self.get_object()
        product.statusAdmin = 'approved'
        product.analisedAt = timezone.now()
        product.scriptSuspicious = False
        product.save()
        payment_method_types = product.additionalInfo.get('paymentMethodsBeforeBlock', ['pix', 'credit_card'])
        product.paymentMethods.set(PaymentMethod.get_payment_methods(types=payment_method_types, user=product.user))
        return Response({'detail': 'Produto aprovado com sucesso'}, status=status.HTTP_200_OK)

    @action(detail=True, methods=['post'])
    def block(self, request, *args, **kwargs):
        product = self.get_object()
        product.status = 'blocked'
        product.blockReason = request.data.get('blockReason', '')
        product.statusAdmin = 'stand_by'
        product.blockedAt = timezone.now()
        product.save()

        # Log da ação -> bloqueio de produtos
        log_action(
            user=request.user,
            action='product_block',
            details=f"Produto {product.id} bloqueado. Motivo: {product.blockReason}",
            product=product,
            affected_user=product.user
        )

        return Response({'detail': 'Produto bloqueado com sucesso'}, status=status.HTTP_200_OK)

    @action(detail=True, methods=['post'])
    def unblock(self, request, *args, **kwargs):
        product = self.get_object()
        product.status = 'active'
        product.statusAdmin = 'approved'
        product.scriptSuspicious = False
        product.unblockedAt = timezone.now()
        product.save()
        payment_method_types = product.additionalInfo.get('paymentMethodsBeforeBlock', ['pix', 'credit_card'])
        product.paymentMethods.set(PaymentMethod.get_payment_methods(types=payment_method_types, user=product.user))
        return Response({'detail': 'Produto desbloqueado com sucesso'}, status=status.HTTP_200_OK)

    @action(detail=True, methods=['post'])
    def block_card(self, request, *args, **kwargs):
        product = self.get_object()
        product.additionalInfo['paymentMethodsBeforeBlock'] = list(product.paymentMethods.values_list('type', flat=True))
        product.additionalInfo['cardBlockedAt'] = timezone.now().isoformat()
        product.additionalInfo['cardBlocked'] = True
        product.paymentMethods.set(PaymentMethod.get_payment_methods(types=['pix'], user=product.user))
        product.save()
        return Response({'detail': 'Cartão bloqueado com sucesso'}, status=status.HTTP_200_OK)

    @action(detail=True, methods=['post'])
    def unblock_card(self, request, *args, **kwargs):
        product = self.get_object()
        product.additionalInfo['cardBlocked'] = False
        product.save()
        payment_methods_types = product.additionalInfo.get('paymentMethodsBeforeBlock', ['pix', 'credit_card'])
        product.paymentMethods.set(PaymentMethod.get_payment_methods(types=payment_methods_types, user=product.user))
        return Response({'detail': 'Cartão desbloqueado com sucesso'}, status=status.HTTP_200_OK)

    @action(detail=True, methods=['post'])
    def refund_request(self, request, *args, **kwargs):
        product = self.get_object()
        product.refundRequest = not product.refundRequest
        product.save()
        return Response(self.get_serializer(product).data, status=status.HTTP_200_OK)

    @action(detail=True, methods=['post'])
    def hideshowcase(self, request, *args, **kwargs):
        product = self.get_object()
        product.hideShowcase = not product.hideShowcase
        product.save(update_fields=['hideShowcase'])

        message = (
            "Produto escondido da vitrine com sucesso"
            if product.hideShowcase
            else "Produto exibido na vitrine com sucesso"
        )
        return Response({'detail': message}, status=status.HTTP_200_OK)

    @action(detail=True, methods=['post'])
    def showcase_sales_filter(self, request, *args, **kwargs):
        product = self.get_object()
        product.bypassShowcaseSalesFilter = not product.bypassShowcaseSalesFilter
        if product.bypassShowcaseSalesFilter:
            product.affiliateMarketplace = True
        product.save(update_fields=['bypassShowcaseSalesFilter', 'affiliateMarketplace'])

        message = (
            "Bypass no filtro de total de vendas habilitado."
            if product.bypassShowcaseSalesFilter
            else "Bypass no filtro de total de vendas desabilitado."
        )
        return Response({
            'detail': message,
            'bypassShowcaseSalesFilter': product.bypassShowcaseSalesFilter,
        }, status=status.HTTP_200_OK)

class ProductDeliveryAPI(viewsets.GenericViewSet, generics.ListAPIView):
    model = ProductDelivery
    serializer_class = ProductDeliveryOwnerSerializer

    def get_queryset(self):
        return ProductDelivery.objects.filter(
            product__id=self.kwargs['pk'],
            product__user=self.request.user,
            status__in=['active', 'waiting_config']
        )

    def create(self, request, *args, **kwargs):
        product = get_object_or_404(Product, id=self.kwargs['pk'], user=request.user)

        contentDelivery = ContentDelivery.get_contentDelivery_instance(request.data.get('type'))
        if not contentDelivery:
            return Response({'detail': 'Tipo de entrega inválido.'}, status=status.HTTP_400_BAD_REQUEST)

        if contentDelivery.type == 'instagram_pp' or contentDelivery.type == 'instagram_cf':
            return self.login_instagram(request, product, contentDelivery)

        if contentDelivery.type == 'telegram':
            return self.start_telegram_config(request, product, contentDelivery)

        if contentDelivery.type == 'discord':
            return self.handle_discord_flow(request, product, contentDelivery)

        return Response({'detail': 'Tipo de entrega inválido.'}, status=status.HTTP_400_BAD_REQUEST)

    def validate_product_delivery_exists(self, product, contentDeliveryType) -> None:
        exists = ProductDelivery.objects.filter(
            product=product,
            contentDelivery__type=contentDeliveryType,
            status__in=['active', 'waiting_config']
        ).exists()

        if exists:
            raise ValidationError({'detail': 'Entrega existente, exclua a entrega atual para configurar uma nova.'})

    def login_instagram(self, request, product, contentDelivery):
        instagram = Instagram()
        username = request.data.get('username')
        password = request.data.get('password')

        login_data = instagram.login(username=username, password=password, user=request.user)

        login_succeeded = instagram.login_succeded(login_data, username=username)

        if login_succeeded or instagram.is_logged_in is True:
            self.validate_product_delivery_exists(product=product, contentDeliveryType=contentDelivery.type)

            ProductDelivery.objects.create(
                product=product,
                name='Instagram - Perfil Privado' if contentDelivery.type == 'instagram_pp' else 'Instagram - Close Friends',
                contentDelivery=contentDelivery,
                fields={'username': username}
            )
            product.contentDeliveries.add(contentDelivery)
            return Response({'detail': 'Instagram configurado com sucesso.'})

        raise Exception(f'Error handling Instagram Login: {login_data}')

    def start_telegram_config(self, request, product, contentDelivery):
        self.validate_product_delivery_exists(product=product, contentDeliveryType=contentDelivery.type)

        productDelivery = ProductDelivery.objects.create(
            product=product,
            name=f'Telegram - {product.name}',
            contentDelivery=contentDelivery,
            status='waiting_config',
            fields={'chat_id': None, 'chat_type': None, 'chat_title': None}
        )
        product.contentDeliveries.add(contentDelivery)
        return Response(self.get_serializer(productDelivery).data)

    def handle_discord_flow(self, request, product, contentDelivery):
        authCode = request.data.get('authCode')
        guildId = request.data.get('guildId')
        state = request.data.get('state')
        user_session_state = request.session.get('discord_oauth_state')

        if not state or not authCode or not guildId:
            return self.start_discord_config(request)

        if user_session_state != state:
            return Response({'detail': 'Invalid state.'}, status=status.HTTP_400_BAD_REQUEST)

        return self.config_discord(product, contentDelivery, guildId, authCode)

    def start_discord_config(self, request):
        discord = Discord()
        oAuth_url = discord.get_discord_oauth_producer(request)
        return Response({'oAuthUrl': oAuth_url})

    def config_discord(self, product, contentDelivery, guildId, authCode):
        discord = Discord()

        code_res = discord.exchange_code(authCode)

        # Checks if the authCode belongs to the informed guildId
        if code_res.get('guild', {}).get('id') != guildId:
            return Response({'detail': 'Invalid authCode.'}, status=status.HTTP_400_BAD_REQUEST)

        role_res = discord.create_role(guildId, f'CaktoBot: {product.name}')

        self.validate_product_delivery_exists(product=product, contentDeliveryType=contentDelivery.type)

        productDelivery = ProductDelivery.objects.create(
            product=product,
            name=f'Discord - {product.name}',
            contentDelivery=contentDelivery,
            status='active',
            fields={
                'guildId': guildId,
                'guildName': code_res.get('guild', {}).get('name'),
                'roleId': role_res.get('id')
            }
        )
        product.contentDeliveries.add(contentDelivery)
        return Response(self.get_serializer(productDelivery).data)

class DeliveryAccessAPI(generics.ListAPIView, views.APIView):
    model = DeliveryAccess

    def get_serializer_class(self):
        if self.request.method == 'POST':
            return DeliveryAccessSerializer
        return DeliveryAccessPublicSerializer

    def get_queryset(self):
        return DeliveryAccess.objects.filter(user=self.request.user)

    def post(self, request, *args, **kwargs):
        delivery_access_id = self.kwargs.get('delivery_access_id')
        if not delivery_access_id:
            return Response(status=status.HTTP_404_NOT_FOUND)

        delivery_access = DeliveryAccess.objects.filter(
            Q(expiresAt__gte=timezone.now()) | Q(expiresAt=None),
            id=delivery_access_id,
            user=request.user,
        ).first()
        if not delivery_access:
            return Response({'detail': 'Acesso não encontrado'}, status=status.HTTP_400_BAD_REQUEST)

        if delivery_access.productDelivery.contentDelivery.type in ['instagram_pp', 'instagram_cf']:
            return self.handle_instagram_access(request, delivery_access)

        if delivery_access.productDelivery.contentDelivery.type == 'discord':
            return self.handle_discord_access(request, delivery_access)

        return Response({'detail': 'Tipo de entrega não configurável.'}, status=status.HTTP_400_BAD_REQUEST)

    def handle_instagram_access(self, request, deliveryAccess: DeliveryAccess):
        username = request.data.get('username')
        instagram = Instagram()

        if deliveryAccess.status == 'active' and deliveryAccess.fields.get('username') != username:
            if deliveryAccess.productDelivery.contentDelivery.type == 'instagram_pp':
                reset_res = instagram.remove_from_private_account(deliveryAccess=deliveryAccess, requester=username)
            else:
                reset_res = instagram.remove_from_closed_friends(deliveryAccess=deliveryAccess, requester=username)
            if not status.is_success(reset_res.status_code):
                raise Exception(f'Erro ao configurar Instagram: {reset_res.content.decode("utf-8")}')

        if deliveryAccess.productDelivery.contentDelivery.type == 'instagram_pp':
            res = instagram.add_to_private_account(deliveryAccess=deliveryAccess, requester=username)
        else:
            res = instagram.add_to_closed_friends(deliveryAccess=deliveryAccess, requester=username)

        if status.is_success(res.status_code):
            access_time = deliveryAccess.calculate_access_time()
            deliveryAccess.expiresAt = access_time if isinstance(access_time, timezone.datetime) else None
            deliveryAccess.fields['username'] = username
            deliveryAccess.status = 'active'
            deliveryAccess.configuredAt = timezone.now()
            deliveryAccess.save()
            return Response({'detail': 'Acesso solicitado com sucesso.'}, status=res.status_code)

        raise Exception(f'Erro ao configurar Instagram: {res.content.decode("utf-8")}')

    def handle_discord_access(self, request, deliveryAccess: DeliveryAccess):
        if deliveryAccess.status != 'waiting_config':
            return Response({'detail': 'Acesso já configurado.'}, status=status.HTTP_400_BAD_REQUEST)

        authCode = request.data.get('authCode')
        state = request.data.get('state')
        if not authCode or not state:
            return Response({'detail': '"authCode" and "state" are required.'}, status=status.HTTP_400_BAD_REQUEST)

        if state != request.session.get('discord_oauth_state'):
            return Response({'detail': 'Invalid state.'}, status=status.HTTP_400_BAD_REQUEST)

        discord = Discord()
        discord.handle_token_exchange(authCode, deliveryAccess)

        accessToken = discord.get_access_token(deliveryAccess)

        user_info_res = discord.get_user_info(access_token=accessToken)
        deliveryAccess.fields['userId'] = user_info_res.get('id')

        discord.handle_discord_access(deliveryAccess)

        deliveryAccess.fields['username'] = user_info_res.get('username')
        deliveryAccess.fields['global_name'] = user_info_res.get('global_name')
        deliveryAccess.status = 'active'

        access_time = deliveryAccess.calculate_access_time()
        deliveryAccess.expiresAt = access_time if isinstance(access_time, timezone.datetime) else None

        deliveryAccess.save()

        return Response({'detail': 'Acesso configurado com sucesso.'})

class AffiliateAdminList(generics.ListAPIView):
    permission_classes = [IsAdminUser]
    serializer_class = AffiliateSerializer
    queryset = Affiliate.objects.all().select_related()
    filter_backends = [filters.SearchFilter, DjangoFilterBackend, filters.OrderingFilter]
    search_fields = [
        'short_id', 'user__email', 'user___company__cpf', 'user___company__companyCnpj',
        'product__name', 'product__short_id', 'product__id',
    ]
    filterset_class = AffiliateAdminFilter
    ordering = ['-startTime']
    ordering_fields = ['user', 'user__email', 'product', 'product__name', 'status', 'commission', 'startTime', 'endTime',]

class AffiliateProductAPIView(viewsets.ModelViewSet):
    scope = 'products'
    serializer_class = AffiliateProductSerializer
    filter_backends = [filters.SearchFilter]
    lookup_field = 'short_id'
    search_fields = [
        'product__name', 'product__affiliateDescription', 'product__affiliateSupportEmail',
        'commission', 'product__id', 'product__short_id', 'startTime', 'endTime',
        'product__user___company__companyName',
    ]

    def get_queryset(self):
        status_list = self.request.query_params.get('status', '')  # type:ignore

        queryset = Affiliate.objects.order_by('-startTime').filter(user=self.request.user).select_related('product__user___company')

        if status_list:
            queryset = queryset.filter(status__in=status_list.split(','))
        return queryset

class ProductImageAPIView(views.APIView):
    scope = 'products'

    def put(self, request, *args, **kwargs):
        product = get_object_or_404(Product, id=kwargs.get('pk'), user=request.user)

        product.image.delete(save=False)
        product.image = request.FILES.get('image', product.image)
        product.save()

        return Response({'detail': 'Imagem salva com sucesso'}, status=status.HTTP_200_OK)

    def delete(self, request, *args, **kwargs):
        product = get_object_or_404(Product, id=kwargs.get('pk'), user=request.user)
        product.image.delete(save=True)

        return Response({'detail': 'Imagem removida'}, status=status.HTTP_200_OK)

class AffiliateListCreateAPIView(viewsets.ViewSet, generics.ListAPIView):
    serializer_class = AffiliateSerializer
    scope = 'affiliates'
    filter_backends = (filters.SearchFilter,)
    search_fields = [
        'user__email', 'product__name', 'product__short_id', 'product__id', 'short_id',
        'user__first_name', 'user__last_name', 'user__cpf', 'user__cnpj', 'user__cellphone',
    ]

    def get_queryset(self):
        queryset = (
            Affiliate.objects
            .filter(Q(product__user=self.request.user) | Q(user=self.request.user))
            .select_related('user', 'product')
            .order_by('-startTime')
        )

        product_pk = self.request.query_params.get('productPk')  # type:ignore
        user_email = self.request.query_params.get('userEmail')  # type:ignore
        status = self.request.query_params.get('status', None)  # type:ignore
        type = self.request.query_params.get('type', '')  # type:ignore

        if type == 'producer':
            queryset = queryset.filter(product__user=self.request.user)
        elif type == 'affiliate':
            queryset = queryset.filter(user=self.request.user)

        if status:
            statuses = status.split(',')
            queryset = queryset.filter(status__in=statuses)
        if product_pk:
            product_pks = product_pk.split(',')
            queryset = queryset.filter(product__pk__in=product_pks)
        if user_email:
            queryset = queryset.filter(user__email=user_email)
        return queryset

    def create(self, request, *args, **kwargs):
        data = request.data.copy()

        product = get_object_or_404(Product, pk=data.get('productPk'))

        if not product.affiliate:
            return Response({'detail': 'Produto fechado para afiliação.'}, status=status.HTTP_400_BAD_REQUEST)

        if product.user == request.user:
            return Response({'detail': 'O produtor não pode ser afiliado do seu próprio produto.'}, status=status.HTTP_400_BAD_REQUEST)

        already_exists = Affiliate.objects.filter(
            user=self.request.user,
            product=product,
            status__in=['active', 'waiting', 'refused', 'blocked']
        ).exists()
        if already_exists:
            return Response({'detail': 'Já existe uma afiliação para esse produto.'}, status=status.HTTP_400_BAD_REQUEST)

        affiliate_status = AffiliateStatus.WAITING.id if product.affiliateRequest else AffiliateStatus.ACTIVE.id

        affiliate = Affiliate.objects.create(
            user=request.user,
            product=product,
            status=affiliate_status,
            commission=product.affiliateCommission,
        )

        # Invalidade cache because it's possible to set affiliate cache
        # regardless of the existence of the Affiliate
        affiliate.invalidate_cache()

        response = AffiliateSerializer(affiliate).data

        if data.get('is_from_showcase'):
            showcase_event = ShowcaseEvent.objects.create(
                user=self.request.user,
                product=product,
                type='affiliate',
            )
            response['showcase_event'] = ShowcaseEventSerializer(showcase_event).data  # type:ignore

        return Response(response, status=status.HTTP_201_CREATED)

    @action(detail=False, methods=['post'])
    def accept_many(self, request, *args, **kwargs):
        self._update_many(request, AffiliateStatus.ACTIVE)
        return Response({'detail': 'Afiliados aprovados.'})

    @action(detail=False, methods=['post'])
    def reject_many(self, request, *args, **kwargs):
        self._update_many(request, AffiliateStatus.REFUSED)
        return Response({'detail': 'Afiliados reprovados.'})

    @action(detail=False, methods=['post'])
    def block_many(self, request, *args, **kwargs):
        self._update_many(request, AffiliateStatus.BLOCKED)
        return Response({'detail': 'Afiliados bloqueados.'})

    @action(detail=False, methods=['post'])
    def unblock_many(self, request, *args, **kwargs):
        self._update_many(request, AffiliateStatus.ACTIVE)
        return Response({'detail': 'Afiliados desbloqueados.'})

    def _update_many(self, request, status: AffiliateStatus):
        affiliates = self._filter_affiliates(request)
        affiliates.update(status=status.id)
        Affiliate.bulk_invalidate_cache(affiliates)

    def _filter_affiliates(self, request):
        affiliate_ids = request.data.get('ids', '')
        affiliate_short_ids = request.data.get('short_ids', '')
        return Affiliate.objects.filter(
            Q(product__user=self.request.user)
            & Q(Q(pk__in=affiliate_ids) | Q(short_id__in=affiliate_short_ids))
        ).only('id', 'short_id')

class AffiliateRetrieveUpdateDestroyAPIView(viewsets.ModelViewSet):
    serializer_class = AffiliateSerializer
    scope = 'affiliates'

    def get_object(self):
        pk = str(self.kwargs['pk'])
        if pk.isdigit():
            return get_object_or_404(Affiliate.objects.filter(
                Q(product__user=self.request.user) | Q(user=self.request.user)),
                pk=pk
            )
        else:
            return get_object_or_404(Affiliate.objects.filter(
                Q(product__user=self.request.user) | Q(user=self.request.user)),
                short_id=pk
            )

    def get_queryset(self):
        return Affiliate.objects.filter(Q(product__user=self.request.user) | Q(user=self.request.user))

    def update(self, request, *args, **kwargs):
        instance = self.get_object()
        commission = Decimal(request.data['commission'])

        if self.request.user != instance.product.user:
            return Response({'detail': 'Acesso negado.'}, status=status.HTTP_401_UNAUTHORIZED)

        if (commission > AFFILIATE_MAX_COMMISSION) or (commission < 1):
            return Response(
                {'detail': f'Comissão deve ser no mínimo 1% e no máximo {AFFILIATE_MAX_COMMISSION}%.'},
                status=status.HTTP_400_BAD_REQUEST
            )

        instance.commission = commission
        instance.save()
        return Response({'detail': 'Comissão alterada com sucesso.'}, status=status.HTTP_200_OK)

    def destroy(self, request, *args, **kwargs):
        instance = self.get_object()

        instance.status = 'canceled'
        instance.save()

        return Response({"detail": "Affiliação cancelada."}, status=status.HTTP_200_OK)

class AffiliateTotalSalesAPIView(APIView):
    """
    API view to retrieve total sales data with and without affiliates,
    the number of affiliates per product, and the top 3 products with the most affiliates.
    Methods:
    -------
    get(request, period, *args, **kwargs):
        Handles GET requests to retrieve sales data for a specified period.
    Parameters:
    ----------
    request : HttpRequest
        The HTTP request object.
    period : str ('%Y-%m-%d')
        The period for which the sales data is to be retrieved.
    *args : tuple
        Additional positional arguments.
    **kwargs : dict
        Additional keyword arguments.
    Returns:
    -------
    Response
        A Response object containing the sales data:
        - total_sales_with_affiliates: Total sales amount with affiliates.
        - total_sales_without_affiliates: Total sales amount without affiliates.
        - affiliates_per_product: List of products with their respective affiliate counts.
        - top_3_products: List of top 3 products with the highest number of affiliates.
    """

    def get(self, request):
        start_date = request.query_params.get('startDate')
        end_date = request.query_params.get('endDate')

        start_date, end_date = self._convert_start_date_and_end_date(start_date, end_date)

        data = {
            'total_sales_with_affiliates': self._get_total_sales_with_affiliates(start_date, end_date),
            'total_sales_without_affiliates': self._get_total_sales_without_affiliates(start_date, end_date),
            'affiliates_per_product': self._get_affiliates_per_product(start_date, end_date),
            'top_3_affiliates': self._get_top_3_affiliates(start_date, end_date),
        }

        return Response(data)

    def _convert_start_date_and_end_date(self, start_date, end_date):
        """
        Converts start and end date strings to timezone-aware datetime objects.
        This method takes start and end date strings in the format '%d-%m-%Y', converts them to date objects,
        and then combines them with the minimum and maximum times of the day respectively. The resulting
        datetime objects are made timezone-aware and converted to the local time zone.
        Args:
            start_date (str): The start date string in the format '%d-%m-%Y'.
            end_date (str): The end date string in the format '%d-%m-%Y'.
        Returns:
            tuple: A tuple containing two timezone-aware datetime objects representing the start and end dates.
        """
        if not self._is_valid_dates(start_date, end_date):
            return None, None

        start_date = timezone.datetime.strptime(start_date, '%d-%m-%Y').date()
        end_date = timezone.datetime.strptime(end_date, '%d-%m-%Y').date()

        start_date = timezone.make_aware(timezone.datetime.combine(start_date, timezone.datetime.min.time()))
        end_date = timezone.make_aware(timezone.datetime.combine(end_date, timezone.datetime.max.time()))

        start_date = timezone.localtime(start_date)
        end_date = timezone.localtime(end_date)

        return start_date, end_date

    def _get_total_sales_with_affiliates(self, start_date, end_date):
        """
        Calculate the total sales for products with affiliates within a specified date range.
        Args:
            start_date (datetime): The start date of the range to filter products.
            end_date (datetime): The end date of the range to filter products.
        Returns:
            float: The total sales amount for products with affiliates within the specified date range.
        """
        if start_date is None or end_date is None:
            products_with_affiliate = Product.objects.filter(affiliate=True, status="active", user=self.request.user)
        else:
            products_with_affiliate = Product.objects.filter(affiliate=True, status="active", user=self.request.user, createdAt__gte=start_date, createdAt__lte=end_date)

        total_sales_with_affiliates = products_with_affiliate.aggregate(total=Sum('total_sales'))['total'] or 0.0

        return total_sales_with_affiliates

    def _get_total_sales_without_affiliates(self, start_date, end_date):
        """
        Calculate the total sales for products that are not affiliated within a given date range.
        Args:
            start_date (datetime): The start date for the sales calculation.
            end_date (datetime): The end date for the sales calculation.
        Returns:
            float: The total sales amount for non-affiliated products within the specified date range.
        """
        if start_date is None or end_date is None:
            products_without_affiliate = Product.objects.filter(affiliate=False, status="active", user=self.request.user)
        else:
            products_without_affiliate = Product.objects.filter(affiliate=False, status="active", user=self.request.user, createdAt__gte=start_date, createdAt__lte=end_date)

        total_sales_without_affiliates = products_without_affiliate.aggregate(total=Sum('total_sales'))['total'] or 0.0

        return total_sales_without_affiliates

    def _get_affiliates_per_product(self, start_date, end_date):
        """
        Retrieve the top 3 affiliates per product within a specified date range.
        Args:
            start_date (datetime): The start date for filtering products.
            end_date (datetime): The end date for filtering products.
        Returns:
            QuerySet: A QuerySet containing dictionaries with the following keys:
                - 'id': The ID of the product.
                - 'name': The name of the product.
                - 'affiliate_count': The number of affiliates associated with the product.
                - 'total_sales': The total sales of the product.
        """
        affiliates_per_product = []

        if start_date is None or end_date is None:
            filter_affiliates = Product.objects.filter(status="active", user=self.request.user)
        else:
            filter_affiliates = Product.objects.filter(status="active", user=self.request.user, createdAt__gte=start_date, createdAt__lte=end_date)

        filter_affiliates_ordered = filter_affiliates.annotate(affiliate_count=Count('affiliates')).order_by('-total_sales')

        if len(filter_affiliates_ordered) > 3:
            filter_3_affiliates = filter_affiliates_ordered[:3]
            for affiliate in filter_3_affiliates:
                affiliates_per_product.append({
                    'id': affiliate.id,
                    'name': affiliate.name,
                    'affiliate_count': affiliate.affiliate_count,
                    'total_sales': affiliate.total_sales
                })

        return affiliates_per_product

    def _get_top_3_affiliates(self, start_date, end_date):
        """
        Retrieves the top 3 affiliates based on total sales within a specified date range.
        Args:
            start_date (datetime): The start date for filtering affiliates.
            end_date (datetime): The end date for filtering affiliates.
        Returns:
            QuerySet: A QuerySet containing the top 3 affiliates with the following fields:
                - id (int): The ID of the affiliate.
                - name (str): The name of the affiliate.
                - affiliate_count (int): The count of affiliates.
                - total_sales (float): The total sales of the affiliate.
                - percentage (float): The percentage of total sales contributed by the affiliate.
        """
        top_3_affiliates = []

        if start_date is None or end_date is None:
            filter_affiliates = Product.objects.filter(status="active", user=self.request.user)
        else:
            filter_affiliates = Product.objects.filter(status="active", user=self.request.user, createdAt__gte=start_date, createdAt__lte=end_date)

        filter_affiliates_ordered = filter_affiliates.annotate(affiliate_count=Count('affiliates')).order_by('-total_sales')

        if len(filter_affiliates_ordered) > 3:
            filter_top_3_affiliates = filter_affiliates_ordered[:3]
            for affiliate in filter_top_3_affiliates:
                top_3_affiliates.append({
                    'id': affiliate.id,
                    'name': affiliate.name,
                    'affiliate_count': affiliate.affiliate_count,
                    'total_sales': affiliate.total_sales
                })
        else:
            return []

        # get total sales for top_3_affiliates
        total_sales = sum([affiliate['total_sales'] for affiliate in top_3_affiliates])

        # get the percentage for top 3 affiliates based on total sales
        for affiliate in top_3_affiliates:
            if total_sales > 0 and affiliate['total_sales'] > 0:
                affiliate['percentage'] = round((affiliate['total_sales'] / total_sales) * 100, 2)
            else:
                affiliate['percentage'] = 0.0

        return top_3_affiliates

    def _is_valid_dates(self, start_date, end_date):
        """
        Validates the format of the start and end dates.

        This method attempts to parse the provided start and end dates using the
        format '%d-%m-%Y'. If the dates are not in the correct format, it returns
        a response indicating an invalid date error.

        Args:
            start_date (str): The start date as a string in the format 'dd-mm-yyyy'.
            end_date (str): The end date as a string in the format 'dd-mm-yyyy'.

        Returns:
            bool: True if the dates are valid, False otherwise.
        """
        if not start_date or not end_date:
            return False

        try:
            start_date = timezone.datetime.strptime(start_date, '%d-%m-%Y').date()
            end_date = timezone.datetime.strptime(end_date, '%d-%m-%Y').date()
        except ValueError:
            return False

        return True

class LinkAPIView(viewsets.ModelViewSet):
    model = Link
    serializer_class = LinkSerializer
    scope = 'products'
    filter_backends = (filters.SearchFilter,)
    search_fields = ['name', 'url']

    def get_queryset(self):
        query_conditions = Q()
        if self.kwargs.get('product'):
            product = get_object_or_404(Product, id=self.kwargs.get('product'), user=self.request.user)
            query_conditions = Q(product=product)
        elif self.kwargs.get('pk'):
            query_conditions = Q(pk=self.kwargs.get('pk'), product__user=self.request.user)

        queryset = Link.objects.filter(query_conditions).select_related('offer')

        status_param = self.request.query_params.get('status', '')  # type:ignore
        if status_param and status_param != 'all':
            queryset = queryset.filter(status=status_param)

        return queryset

    def create(self, request, *args, **kwargs):
        product = get_object_or_404(Product, id=self.kwargs.get('product'), user=self.request.user)
        request.data['product'] = product.id  # Add product ID to request data
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer.save(product=product)  # Set the product instance before saving
        headers = self.get_success_headers(serializer.data)
        return Response(serializer.data, status=status.HTTP_201_CREATED, headers=headers)

    @action(detail=True, methods=['post'])
    def disable(self, request, pk=None):
        link = self.get_object()
        link.status = 'disabled'
        link.save()
        link.offer.status = 'disabled'
        link.offer.save()
        return Response({'status': 'Desabilitado com sucesso'}, status=status.HTTP_200_OK)

    @action(detail=True, methods=['post'])
    def enable(self, request, pk=None):
        link = self.get_object()
        link.status = 'active'
        link.save()
        link.offer.status = 'active'
        link.offer.save()
        return Response({'status': 'Habilitado com sucesso'}, status=status.HTTP_200_OK)

    @action(detail=True, methods=['post'])
    def show_affiliates(self, request, pk=None):
        link = self.get_object()
        link.showAffiliates = True
        link.save()

        return Response({'status': 'Mostrando o link para os afiliados'}, status=status.HTTP_200_OK)

    @action(detail=True, methods=['post'])
    def hide_affiliates(self, request, pk=None):
        link = self.get_object()
        link.showAffiliates = False
        link.save()

        return Response({'status': 'Escondendo o link dos afiliados'}, status=status.HTTP_200_OK)

    @action(detail=True, methods=['post'])
    def enable_many(self, request, pk=None):
        links = Link.objects.filter(id__in=request.data['ids'])
        links.update(status='active')
        for link in links.filter(offer__isnull=False):
            link.offer.status = 'active'  # type:ignore
            link.offer.save()  # type:ignore
        return Response({'status': 'Links habilitados com sucesso'}, status=status.HTTP_200_OK)

    @action(detail=True, methods=['post'])
    def disable_many(self, request, pk=None):
        links = Link.objects.filter(id__in=request.data['ids'])
        links.update(status='disabled')
        for link in links.filter(offer__isnull=False):
            link.offer.status = 'disabled'  # type:ignore
            link.offer.save()  # type:ignore

        return Response({'status': 'Links desabilitados com sucesso'}, status=status.HTTP_200_OK)

    @action(detail=False, methods=['get'])
    def affiliate_links(self, *args, **kwargs):
        product_pk = self.kwargs['product']
        affiliates = Affiliate.objects.filter(product=product_pk, user=self.request.user, status='active')
        queryset = Link.objects.filter(
            Q(offer__status='active') | Q(offer=None),
            product=product_pk,
            showAffiliates=True,
            product__affiliates__in=affiliates,
        ).select_related('offer')

        queryset = self.filter_queryset(queryset)

        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

class LinkRedirectAPIView(views.APIView):
    permission_classes = [AllowAny]

    def get_afid_data(self, afid_shortId, product_id):
        affiliate = Affiliate.objects.filter(short_id=afid_shortId, status='active').select_related('product').first()
        return get_affiliate_cookie_info(afid_shortId, product_id) if affiliate else None

    def get_cached_afid_data(self, afid_shortId: str | None, product_id: str | None) -> dict | None:
        afid_shortId = Affiliate.validate_short_id(afid_shortId)

        if not afid_shortId or not product_id:
            return None

        return cache.get_or_set(
            f'{Affiliate.cache_prefix}{afid_shortId}',
            lambda: self.get_afid_data(afid_shortId, product_id),
            None
        )

    def get_link_data(self, shortId):
        link = Link.objects.filter(shortId=shortId, status='active').first()
        return (link.url, link.product.pk) if link else (None, None)

    def get_cached_url(self, shortId):
        front_end_404_page = settings.FRONT_END_BASE_URL + '/404'

        shortId = Link.validate_short_id(shortId)

        if not shortId:
            return front_end_404_page, None

        link_url, product_id = cache.get_or_set(  # type:ignore
            Link.cache_prefix + shortId,
            lambda: self.get_link_data(shortId),
            None
        )

        if not link_url or not product_id:
            return front_end_404_page, None

        return link_url, product_id

    def set_affiliate_cookie(self, response, afid_data):
        cookieTime = int(afid_data['cookieTime']) if afid_data['cookieTime'] > 0 else 365
        response.set_cookie(
            'affiliate_short_id',
            afid_data['affiliateShortId'],
            domain=settings.SESSION_COOKIE_DOMAIN,
            max_age=60 * 60 * 24 * cookieTime
        )

    def get(self, request, shortId):
        # redirect to be possible to set cookies over the cakto.com.br domain
        if request.get_host().endswith(settings.REDIRECT_DOMAIN):
            return redirect(f'{settings.BACKEND_BASE_URL}{shortId}/?{urlencode(request.query_params)}')

        url, product_id = self.get_cached_url(shortId)

        response = redirect(url)

        afid_data = self.get_cached_afid_data(request.query_params.get('affiliate', None), product_id)  # type:ignore

        if afid_data:
            self.set_affiliate_cookie(response, afid_data)

        return response

class OrderBumpAPI(viewsets.ModelViewSet):
    model = OrderBump
    serializer_class = OrderBumpCreateSerializer
    scope = 'products'

    def get_queryset(self):
        return OrderBump.objects.filter(product__user=self.request.user)

    def create(self, request, *args, **kwargs):
        product = get_object_or_404(Product.objects.prefetch_related('bumps'), id=self.kwargs.get('product'), user=self.request.user)

        if product.bumps.filter(offer=request.data.get('offer')).exists():  # type:ignore
            return Response({'detail': 'Oferta já cadastrada como order bump deste produto.'}, status=status.HTTP_400_BAD_REQUEST)

        request.data['product'] = product.id
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer.save(product=product)

        return Response(OrderBumpSerializer(serializer.instance).data, status=status.HTTP_201_CREATED)

    @action(detail=True, methods=['post'])
    def position_update(self, request, *args, **kwargs):
        product = get_object_or_404(Product.objects.prefetch_related('bumps'), id=self.kwargs.get('product'), user=self.request.user)
        bumps_to_update = []

        for bump_data in request.data:
            bump_id, position = bump_data.get('id'), bump_data.get('position')
            # Find the bump instance in the prefetched bumps to avoid additional queries
            bump = next((bump for bump in product.bumps.all() if bump.id == bump_id), None)  # type:ignore
            if bump:
                bump.position = position
                bumps_to_update.append(bump)

        if bumps_to_update:
            OrderBump.objects.bulk_update(bumps_to_update, ['position'])
            product.invalidate_cache()

        return Response({'detail': 'Posições atualizadas com sucesso.'})

class OrderBumpRetrieveAPI(generics.RetrieveUpdateDestroyAPIView):
    scope = 'products'
    serializer_class = OrderBumpSerializer

    def get_queryset(self):
        return OrderBump.objects.filter(product__user=self.request.user)

    def update(self, request, *args, **kwargs):
        bump = self.get_object()
        serializer = OrderBumpUpdateSerializer(instance=bump, data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)
        serializer.save()

        return Response(serializer.data, status=status.HTTP_200_OK)

    def destroy(self, request, *args, **kwargs):
        bump = self.get_object()
        bump.delete()
        return Response({"detail": "Order bump deletado com sucesso!"}, status=status.HTTP_200_OK)

class CoproductionAPIView(viewsets.ModelViewSet):
    model = Coproduction
    scope = 'products'
    serializer_class = CoproductionRetrieveSerializer

    def get_object(self):
        user = self.request.user
        return get_object_or_404(
            Coproduction.objects
            .filter((Q(user=user) | Q(product__user=user)) & Q(pk=self.kwargs['pk']))
            .select_related('product', 'product__user', 'user')
        )

    def update(self, request, *args, **kwargs):
        coproduction = self.get_object()

        if self.request.user != coproduction.product.user:
            return Response({'detail': 'Apenas o dono do produto pode alterar a coprodução.'}, status=status.HTTP_400_BAD_REQUEST)
        if coproduction.status != 'pending':
            return Response({'detail': 'Apenas coprodução com status pendente pode ser alterada.'}, status=status.HTTP_400_BAD_REQUEST)

        # Limit commission values
        coproductions = Coproduction.objects.filter(product=coproduction.product)
        coproductions_commissions = sum(
            [coproduction.amount for coproduction in coproductions if coproduction.status in ['active', 'pending']],
            start=Decimal('0')
        )
        coproductions_commissions -= coproduction.amount

        commission = Decimal(request.data.get('percentage', 0)).quantize(Decimal('1.00'), ROUND_FLOOR)

        if (commission > 80) or ((coproductions_commissions + commission).quantize(Decimal('1.00'), ROUND_FLOOR) > Decimal('80')):
            return Response({'detail': 'A soma de todas as coproduções deve ser menor que 80%.'}, status=status.HTTP_400_BAD_REQUEST)
        elif commission < 1:
            return Response({'detail': 'A comissão deve ser maior que 1%.'}, status=status.HTTP_400_BAD_REQUEST)

        serializer = CoproductionUpdateSerializer(coproduction, request.data, partial=True)

        if serializer.is_valid():
            serializer.save()
            send_notification(
                coproduction.user,
                'Detalhes de Coprodução alterada',
                f'{self.request.user} alterou os detalhes da coprodução em {coproduction.product.name}.',
            )
            return Response(serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def destroy(self, request, *args, **kwargs):
        coproduction = self.get_object()

        if request.user == coproduction.user and not coproduction.deletionApprovedByUser:
            coproduction.deletionApprovedByUser = True
            coproduction.save()
        elif request.user == coproduction.product.user and not coproduction.deletionApprovedByOwner:
            coproduction.deletionApprovedByOwner = True
            coproduction.save()

        both_approval = coproduction.deletionApprovedByOwner and coproduction.deletionApprovedByUser
        rejected = (coproduction.status == 'rejected')
        pending_and_owner = (coproduction.status == 'pending' and request.user == coproduction.product.user)

        if both_approval or rejected or pending_and_owner:
            coproduction.delete()
            return Response(status=status.HTTP_200_OK)
        return Response({'detail': 'Coprodução marcada para ser cancelada. As duas partes devem solicitar para que o cancelamento seja efetivado.'}, status=status.HTTP_200_OK)

    @action(detail=True, methods=['post'])
    def cancel_delete(self, *args, **kwargs):
        coproduction = self.get_object()

        user_condition = (self.request.user == coproduction.user and coproduction.deletionApprovedByUser)
        owner_condition = (self.request.user == coproduction.product.user and coproduction.deletionApprovedByOwner)

        if user_condition:
            coproduction.deletionApprovedByUser = False
        elif owner_condition:
            coproduction.deletionApprovedByOwner = False

        if user_condition or owner_condition:
            coproduction.save()
            return Response({'detail': 'Pedido para cancelamento de coprodução removido.'}, status=status.HTTP_200_OK)
        return Response({'detail': 'Nenhum cancelamento em aberto encontrado.'}, status=status.HTTP_404_NOT_FOUND)

    @action(detail=True, methods=['post'])
    def accept(self, *args, **kwargs):
        coproduction = self.get_object()

        if self.request.user != coproduction.user:
            return Response({'detail': 'Apenas o coprodutor pode aceitar ou recusar o convite.'}, status=status.HTTP_401_UNAUTHORIZED)

        coproduction.status = 'accepted'
        coproduction.startTime = timezone.now()
        coproduction.save()
        send_notification(
            coproduction.product.user,
            'Coprodução aceita',
            f'{self.request.user} aceitou seu pedido de coprodução.'
        )
        return Response({'detail': 'Coprodução aceita.'}, status=status.HTTP_200_OK)

    @action(detail=True, methods=['post'])
    def reject(self, *args, **kwargs):
        coproduction = self.get_object()

        if self.request.user != coproduction.user:
            return Response({'detail': 'Apenas o coprodutor pode aceitar ou recusar o convite.'}, status=status.HTTP_401_UNAUTHORIZED)
        if coproduction.status == 'active':
            return Response({'detail': 'Coprodução ativa não pode ser rejeitada.'}, status=status.HTTP_400_BAD_REQUEST)

        coproduction.status = 'rejected'
        coproduction.save()
        send_notification(
            coproduction.product.user,
            'Coprodução recusada',
            f'{self.request.user} recusou seu pedido de coprodução.'
        )
        return Response({'detail': 'Coprodução recusada.'}, status=status.HTTP_200_OK)

class CoproductionCreateView(generics.CreateAPIView):
    model = Coproduction
    scope = 'products'

    def create(self, request):
        data = request.data.copy()
        commission = data.get('percentage')

        user = User.objects.filter(email__iexact=data.get('userEmail')).first()
        if user is None:
            return Response({'detail': 'Usuário não cadastrado na Cakto.'}, status=status.HTTP_400_BAD_REQUEST)

        product = Product.objects.filter(pk=data.get('productPk'), user=request.user).first()
        if product is None:
            return Response({'detail': 'Produto não encontrado.'}, status=status.HTTP_404_NOT_FOUND)

        if user == product.user:
            return Response({'detail': 'O dono do produto não pode convidar a si mesmo para ser coprodutor.'}, status=status.HTTP_400_BAD_REQUEST)

        coproductions = Coproduction.objects.filter(user=user, product=product)
        already_exists = any(coprodcution.status in ['active', 'pending'] for coprodcution in coproductions)
        if already_exists:
            return Response({'detail': 'Já existe uma coprodução para esse usuário neste produto.'}, status=status.HTTP_400_BAD_REQUEST)

        # Limit commission values
        coproductions = Coproduction.objects.filter(product=product)
        coproductions_commissions = sum(
            [coproduction.amount for coproduction in coproductions if coproduction.status in ['active', 'pending']],
            start=Decimal('0')
        )

        if (commission > 80) or ((coproductions_commissions + Decimal(commission)).quantize(Decimal('1.000')) > Decimal('80')):
            return Response({'detail': 'A soma de todas as coproduções deve ser menor que 80%.'}, status=status.HTTP_400_BAD_REQUEST)
        elif commission < 1:
            return Response({'detail': 'A comissão deve ser maior que 1%.'}, status=status.HTTP_400_BAD_REQUEST)

        data['user'] = user.pk
        data['product'] = product.pk
        data['amount'] = commission

        serializer = CoproductionCreateSerializer(data=data)
        serializer.is_valid(raise_exception=True)
        serializer.save()
        send_notification(
            user,
            'Pedido para coprodução',
            f'{self.request.user} convidou você para ser coprodutor(a) em {product.name}'
        )
        serializer = CoproductionRetrieveSerializer(serializer.instance)
        return Response(serializer.data, status=status.HTTP_201_CREATED)

class CoproductionListView(generics.ListAPIView):
    model = Coproduction
    scope = 'products'
    serializer_class = CoproductionRetrieveSerializer
    filter_backends = [filters.SearchFilter]
    search_fields = [
        'user__email', 'user__first_name', 'user__last_name', 'user__cpf', 'user__cnpj',
        'product__name', 'product__short_id', 'product__id', 'product__user__email',
        'product__user__first_name', 'product__user__last_name', 'product__user__cpf',
        'product__user__cnpj',
    ]

    def get_queryset(self):
        user = self.request.user
        query_set = Coproduction.objects.filter(Q(user=user) | Q(product__user=user))
        query_set.select_related('product', 'product__user', 'user')
        query_set.order_by('-startTime')

        product_pk = self.request.query_params.get('productPk')  # type: ignore
        user_email = self.request.query_params.get('userEmail')  # type: ignore
        coproduction_status = self.request.query_params.get('status', '')  # type: ignore
        coproduction_type = self.request.query_params.get('type')  # type: ignore

        if coproduction_type == 'producer':
            query_set = query_set.filter(product__user=user)
        elif coproduction_type == 'coproducer':
            query_set = query_set.filter(user=user)
        if product_pk:
            query_set = query_set.filter(product__pk=product_pk)
        if user_email:
            query_set = query_set.filter(user__email=user_email)
        if coproduction_status:
            ids_to_exclude = [coproduction.pk for coproduction in query_set if coproduction.status not in coproduction_status.split(',')]
            query_set = query_set.exclude(pk__in=ids_to_exclude)
        return query_set

class PixelDomainAPIView(viewsets.ModelViewSet):
    scope = 'products'
    model = PixelDomain
    serializer_class = PixelDomainSerializer

    def get_queryset(self):
        return PixelDomain.objects.filter(user=self.request.user)

    def create(self, request, *args, **kwargs):
        request.data.update(
            domain='pixels.' + request.data.get('domain', '').lower().strip()
            .replace('https://', '').replace('http://', '').replace('www.', '').replace('pixels.', '')
        )
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        serializer.save(user=request.user)
        headers = self.get_success_headers(serializer.data)
        return Response(serializer.data, status=status.HTTP_201_CREATED, headers=headers)

    def destroy(self, request, *args, **kwargs):
        pixel_domain = self.get_object()
        self.perform_destroy(pixel_domain)
        return Response(status=status.HTTP_200_OK)

    @action(detail=True, methods=['post'])
    def verify_pixel_domain(self, request, *args, **kwargs):
        pixel_domain = get_object_or_404(PixelDomain.objects.filter(pk=kwargs['pk'], user=request.user))

        if not is_domain_with_cname_record(pixel_domain.domain):
            return Response({'detail': 'Não foi possível verificar o domínio.'}, status=status.HTTP_400_BAD_REQUEST)

        pixel_domain.lastVerified = timezone.now()
        pixel_domain.isVerified = True
        pixel_domain.save()

        return Response({'detail': 'Domínio verificado com sucesso.'}, status=status.HTTP_200_OK)

class TrackingPixelAPIView(generics.RetrieveUpdateAPIView):
    scope = 'products'
    serializer_class = TrackingPixelSerializer

    def get_queryset(self):
        return TrackingPixels.objects.filter(
            Q(product__user=self.request.user) | Q(affiliate__user=self.request.user)
        ).prefetch_related(
            'facebook_pixels', 'google_ads_pixels', 'taboola_pixels',
            'outbrain_pixels', 'tiktok_pixels', 'kwai_pixels',
        )

class CouponListCreateAPIView(generics.ListCreateAPIView):
    scope = 'coupons'
    serializer_class = CouponSerializer
    filter_backends = [filters.SearchFilter, DjangoFilterBackend, filters.OrderingFilter]
    filterset_class = CouponFilter
    search_fields = ['code', 'products__name', 'products__short_id', 'products__id', 'id']
    ordering_fields = ['code', 'discount', 'applyOnBumps', 'status', 'startTime', 'endTime', 'createdAt', 'updatedAt']

    def get_queryset(self):
        return Coupon.objects.filter(products__user=self.request.user).distinct()

    def filter_queryset(self, queryset):
        queryset = super().filter_queryset(queryset)
        if not self.request.query_params.get('status'):
            queryset = queryset.filter(status='active')
        return queryset

    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)

    def create(self, request, *args, **kwargs):
        serializer = CouponCreateUpdateSerializer(data=request.data, context={'request': request})
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(CouponSerializer(serializer.instance).data, status=status.HTTP_201_CREATED)

class CouponAPI(viewsets.ModelViewSet):
    scope = 'coupons'
    serializer_class = CouponSerializer

    def get_queryset(self):
        return Coupon.objects.filter(products__user=self.request.user).distinct()

    def update(self, request, *args, **kwargs):
        coupon = self.get_object()
        serializer = CouponCreateUpdateSerializer(instance=coupon, data=request.data, partial=True, context={'request': request})
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(CouponSerializer(serializer.instance).data, status=status.HTTP_200_OK)

    def perform_destroy(self, coupon):
        coupon.status = 'deleted'
        coupon.save(update_fields=['status'])

class CouponAllowAnyAPI(viewsets.ModelViewSet):
    permission_classes = [AllowAny]
    serializer_class = CouponSerializer

    @action(detail=False, methods=['post'])
    def validate_coupon(self, request, *args, **kwargs):
        code = (request.data.get('code') or '').lower()
        offerId, _ = unpack_offer_id_and_checkout_id(short_id=request.data.get('offerId'))
        coupon = get_coupon(coupon_code=code, offer_id=offerId)

        if not coupon:
            return Response({'detail': 'Cupom inválido ou vencido.'}, status=status.HTTP_400_BAD_REQUEST)

        from product.models import Offer
        offer = Offer.objects.filter(id=offerId, status='active').first()

        if not offer:
            return Response({'detail': 'Oferta não encontrada.'}, status=status.HTTP_400_BAD_REQUEST)

        if not validate_coupon_with_dynamic_fees(offer, coupon):
            return Response({
                'detail': 'Este cupom não pode ser aplicado pois o valor final seria menor que as taxas cobradas.'
            }, status=status.HTTP_400_BAD_REQUEST)

        return Response(CouponPublicSerializer(coupon).data, status=status.HTTP_200_OK)

class OfferAPIView(viewsets.ModelViewSet):
    authentication_classes = [OAuth2Authentication, CustomJWTAuthentication, TokenAuthentication]
    scope = 'products'
    required_scopes = ['offers']
    parser_classes = [JSONParser, MultiPartParser, FormParser]
    serializer_class = OfferSerializer
    filter_backends = [filters.SearchFilter, DjangoFilterBackend, filters.OrderingFilter]
    permission_classes = [
        IsAuthenticated,
        UserIsValidated,
        ScopePermission,
        TokenHasScopeIfOauth,
    ]
    search_fields = [
        'name', 'product__name', 'product__short_id', 'product__id', 'id',
    ]
    filterset_class = OfferFilter
    ordering_fields = ['id', 'name', 'price', 'product', 'status', 'default', 'createdAt', 'updatedAt']

    def get_queryset(self):
        qs = Offer.objects.filter(
            product__user=self.request.user,
            status__in=['active', 'disabled']
        ).select_related('product')

        if self.action == 'update':
            qs.prefetch_related('product__paymentMethods')

        return qs

    def perform_update(self, serializer):
        response = super().perform_update(serializer)
        send_offer_data_to_compliance_analysis(offers=[serializer.instance])  # type:ignore
        return response

    def get_serializer_context(self) -> dict[str, Any]:
        product = None
        if self.action in ['update', 'partial_update']:
            product = self.get_object().product
        elif self.action == 'create':
            product = self.get_product_instance()

        response = super().get_serializer_context()

        response.update({'product': product})

        return response

    def get_product_instance(self):
        return (
            Product.objects
            .filter(id=self.request.data.get('product'), user=self.request.user)
            .prefetch_related('paymentMethods')
            .first()
        )

    def create(self, request, *args, **kwargs):
        product = self.get_product_instance()
        if not product:
            return Response({'detail': 'Produto não encontrado.'}, status=status.HTTP_400_BAD_REQUEST)

        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        offer: Offer = serializer.save(product=product)

        Link.objects.get_or_create(
            name=offer.name,
            product=product,
            offer=offer,
            defaults={
                'type': 'checkout',
                'url': f'{os.getenv("CHECKOUT_BASE_URL", "https://pay.cakto.com.br")}/{offer.id}'
            }
        )

        # Adds offer to product default checkout
        default_checkout: Checkout = product.checkouts.filter(default=True).first()
        if default_checkout:
            default_checkout.offers.add(offer)

        send_offer_data_to_compliance_analysis(offers=[offer])

        return Response(serializer.data, status=status.HTTP_201_CREATED)

    def destroy(self, request, *args, **kwargs):
        offer: Offer = self.get_object()

        if offer.default:
            return Response({'detail': 'Oferta padrão não pode ser deletada.'}, status=status.HTTP_400_BAD_REQUEST)

        Link.objects.filter(offer=offer).delete()

        OrderBump.objects.filter(offer=offer).delete()

        checkouts = Checkout.objects.filter(offers=offer).only('id', 'offers')
        for checkout in checkouts:
            checkout.offers.remove(offer)
            checkout.save()

        offer.status = 'deleted'
        offer.save(update_fields=['status'])

        return Response(status=status.HTTP_204_NO_CONTENT)

class BlockProductByOfferAPIView(viewsets.GenericViewSet):
    scope = 'products'
    queryset = Offer.objects.all()
    permission_classes = [CanBlockProduct]

    def patch(self, request, *args, **kwargs):
        """Block a product by its offer pk."""
        offer = self.get_object()
        offer.product.status = 'blocked'
        offer.product.save(update_fields=['status'])
        return Response({'detail': 'Produto bloqueado com sucesso.'})

class ShowcaseAPIView(generics.ListAPIView):
    scope = 'products'
    serializer_class = ProductShowcaseSerializer
    pagination_class = ProductShowcasePagination
    filter_backends = [DjangoFilterBackend, ShowcaseOrderingFilter, filters.SearchFilter]
    search_fields = ['name', 'description']
    ordering_fields = ['name', 'price', 'createdAt', 'temperature', 'id']
    ordering = ["-temperature", "-id"]
    filterset_class = ProductShowcaseFilter
    cache_prefix = "showcase_products"

    def get_queryset(self):
        cached = cache.get("showcase_products:max_values") or {
            'max_volume': 1,
            'max_growth_percent': 1,
            'max_conversion': 1,
        }

        max_volume = cached["max_volume"] or 1
        max_growth = cached["max_growth_percent"] or 1
        max_conversion = cached["max_conversion"] or 1

        queryset = Product.objects.filter(
            Product.get_showcase_filters()
        ).select_related("user", "category").annotate(
            previous=Coalesce(F('sales_30_days_count') - F('sales_7_days_count'), 0),
            growth=Case(
                When(previous=0, then=Value(0.0)),
                default=ExpressionWrapper(
                    (F('sales_7_days_count') - F('previous')) * 1.0 / F('previous'),
                    output_field=FloatField()
                ),
                output_field=FloatField()
            ),
            volume_score=Least(F('sales_7_days_count') * 60.0 / max_volume, 60.0),
            growth_score=Least(Greatest(F('growth') * 37.5 / max_growth, 0.0), 37.5),
            total_visits=Coalesce(Sum('checkouts__visits'), 0),
            conversion=Case(
                When(total_visits=0, then=Value(0.0)),
                default=ExpressionWrapper(
                    F('total_sales_count') * 1.0 / F('total_visits'),
                    output_field=FloatField()
                ),
                output_field=FloatField()
            ),
            conversion_score=Least(F('conversion') * 30.0 / max_conversion, 30.0),
            last_24h_score=Case(
                When(sales_24h_count__gt=0, then=Value(22.5)),
                default=Value(0),
                output_field=IntegerField()
            ),
            temperature=Func(
                F('volume_score') + F('growth_score') + F('conversion_score') + F('last_24h_score'),
                function='ROUND',
                template='%(function)s(%(expressions)s, 0)',
                output_field=IntegerField()
            )
        ).order_by('-temperature')

        return queryset

    def list(self, request, *args, **kwargs):
        query_params = self.request.GET.urlencode()
        cache_key = f'{self.cache_prefix}:{hashlib.md5(query_params.encode()).hexdigest()}'
        cached_data = cache.get(cache_key)

        if cached_data:
            product_list = cached_data.get("results", cached_data)
            product_ids = [p["id"] for p in product_list]

            affiliations = Affiliate.objects.filter(
                user=request.user,
                product_id__in=product_ids
            ).values("product_id", "status")

            affiliation_map = {a["product_id"]: a["status"] for a in affiliations}

            hide_map = {
                product.id: (product.hideShowcase, product.status)
                for product in Product.objects.filter(id__in=product_ids)
            }

            filtered_products = []
            for prod in product_list:
                pid = prod["id"]
                product_meta = hide_map.get(pid)

                if not product_meta:
                    continue

                hide_showcase, status = product_meta
                if hide_showcase or status != "active":
                    continue

                status = affiliation_map.get(pid)
                prod["affiliation"] = {"status": status} if status else None
                prod["hideShowcase"] = False
                filtered_products.append(prod)

            if isinstance(cached_data, dict) and "results" in cached_data:
                cached_data["results"] = filtered_products
                cached_data["count"] = len(filtered_products)
                return Response(cached_data)
            else:
                return Response(filtered_products)

        queryset = self.filter_queryset(self.get_queryset())
        page = self.paginate_queryset(queryset)

        if page is not None:
            serializer = self.get_serializer(page, many=True)
            response = self.get_paginated_response(serializer.data)
            cache.set(cache_key, response.data, timeout=60 * 60)
            return response

        serializer = self.get_serializer(queryset, many=True)
        cache.set(cache_key, serializer.data, timeout=60 * 60)
        return Response(serializer.data)

class ShowcaseV2APIView(ShowcaseAPIView):
    cache_prefix = "showcase_products_v2"

    def get_queryset(self):
        avgs = showcase_get_top_averages()

        queryset = (
            Product.objects.filter(Product.get_showcase_filters())
            .select_related("user", "category")
            .annotate(
                # bases
                previous=Coalesce(F("sales_30_days_count") - F("sales_7_days_count"), 0),
                growth=Case(
                    When(previous=0, then=Value(0.0)),
                    default=ExpressionWrapper(
                        (F("sales_7_days_count") - F("previous")) * 1.0 / F("previous"),
                        output_field=FloatField(),
                    ),
                    output_field=FloatField(),
                ),
                total_visits=Coalesce(Sum("checkouts__visits"), 0),
                conversion=Case(
                    When(total_visits=0, then=Value(0.0)),
                    default=ExpressionWrapper(
                        F("total_sales_count") * 1.0 / F("total_visits"),
                        output_field=FloatField(),
                    ),
                    output_field=FloatField(),
                ),
                # scores v2
                volume_score=Least(
                    ExpressionWrapper(
                        F("sales_7_days_count") * 60.0 / Value(avgs["avg_volume"]),
                        output_field=FloatField(),
                    ),
                    60.0,
                ),
                growth_score=Least(
                    Greatest(
                        ExpressionWrapper(
                            F("growth") * 37.5 / Value(avgs["avg_growth"]),
                            output_field=FloatField(),
                        ),
                        0.0,
                    ),
                    37.5,
                ),
                conversion_score=Least(
                    ExpressionWrapper(
                        F("conversion") * 30.0 / Value(avgs["avg_conversion"]),
                        output_field=FloatField(),
                    ),
                    30.0,
                ),
                last_24h_score=Case(
                    When(sales_24h_count__gt=0, then=Value(22.5)),
                    default=Value(0),
                    output_field=FloatField(),
                ),
                temperature=Func(
                    F("volume_score")
                    + F("growth_score")
                    + F("conversion_score")
                    + F("last_24h_score"),
                    function="ROUND",
                    template="%(function)s(%(expressions)s, 0)",
                    output_field=IntegerField(),
                ),
            )
            .order_by("-temperature", "-id")
        )
        return queryset

class ShowcaseEventView(generics.GenericAPIView):
    scope = 'products'
    serializer_class = ShowcaseEventRegisterSerializer

    def post(self, request):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        data = serializer.validated_data

        events = [
            ShowcaseEvent(
                product=product,
                type=data.get('type'),
                user=self.request.user,
            )
            for product in data.get('products')
        ]

        logged_events = ShowcaseEvent.objects.bulk_create(events)

        return Response(
            {
                'detail': 'Evento(s) registrado(s) com sucesso.',
                'logged_events': ShowcaseEventSerializer(logged_events, many=True).data
            },
            status=status.HTTP_201_CREATED
        )

class AffiliateDetailAPIView(generics.RetrieveAPIView):
    lookup_field = "id"
    queryset = Product.objects.filter(affiliate=True)

    def get(self, request, *args, **kwargs):
        product = self.get_object()
        affiliate = get_is_user_affiliate(request.user, product)

        if not affiliate:
            return Response({"affiliation": False})

        affiliation_status = affiliate.status
        affiliation_data = AffiliateProductSerializer(affiliate).data

        return Response({
            'affiliate': affiliation_status,
            'affiliate_details': affiliation_data
        })

class ProductSuppliers(views.APIView):
    scope = 'products'

    def get(self, request, *args, **kwargs):
        from faker import Faker
        factory = Faker()
        mocked_data = [
            {
                'id': factory.random_int(min=1, max=1000),
                'name': factory.name(),
                'email': factory.email(),
            } for _ in range(5)
        ]
        return Response(mocked_data)
