from unittest import mock

from cakto.tests.base import BaseTestCase
from apps.services.event_manager import dispatch_pixel_events
from product.models import FacebookPixel, OrderBump, TrackingPixels


class OrderBumpPixelEventsTests(BaseTestCase):
    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()
        cls.create_paymentMethods()

        cls.product = cls.create_product(user=cls.user)

        cls.tracking_pixels_main = TrackingPixels.objects.create(product=cls.product)
        FacebookPixel.objects.create(tracking_pixels=cls.tracking_pixels_main, pixelId='123456')

        cls.bump_offer_product = cls.create_product(user=cls.user)
        cls.bump_offer = cls.bump_offer_product.offers.first()

        cls.tracking_pixels_bump_offer = TrackingPixels.objects.create(product=cls.bump_offer_product)
        FacebookPixel.objects.create(tracking_pixels=cls.tracking_pixels_bump_offer, pixelId='654321')

        cls.bump = OrderBump.objects.create(product=cls.product, offer=cls.bump_offer)

        cls.customer = cls.create_customer()
        cls.order = cls.create_order(product=cls.product, customer=cls.customer)

    def test_field_default_is_false(self):
        new_product = self.create_product(user=self.user)
        self.assertFalse(new_product.disable_orderbump_pixel_events)

    @mock.patch('apps.services.event_manager.process_pixel_event.delay')
    def test_pixel_event_sent_when_flag_false(self, delay_mock):
        dispatch_pixel_events(
            'initiate_checkout',
            order=self.order,
            offer=self.bump_offer,
        )
        self.assertTrue(delay_mock.called, 'Pixel deveria ter sido enfileirado')

    @mock.patch('apps.services.event_manager.process_pixel_event.delay')
    def test_pixel_event_blocked_when_flag_true(self, delay_mock):
        self.product.disable_orderbump_pixel_events = True
        self.product.save(update_fields=['disable_orderbump_pixel_events'])

        dispatch_pixel_events(
            'initiate_checkout',
            order=self.order,
            offer=self.bump_offer,
        )
        delay_mock.assert_not_called()

    @mock.patch('apps.services.event_manager.process_pixel_event.delay')
    def test_non_bump_event_still_sent_with_flag_true(self, delay_mock):
        self.product.disable_orderbump_pixel_events = True
        self.product.save(update_fields=['disable_orderbump_pixel_events'])

        normal_offer = self.product.offers.first()

        dispatch_pixel_events(
            'initiate_checkout',
            order=self.order,
            offer=normal_offer
        )
        self.assertTrue(delay_mock.called, 'Eventos normais não devem ser bloqueados')

    @mock.patch('apps.services.event_manager.process_pixel_event.delay')
    def test_bump_without_pixel_and_flag_false_should_send(self, delay_mock):
        self.tracking_pixels_bump_offer.delete()

        dispatch_pixel_events(
            'initiate_checkout',
            order=self.order,
            offer=self.bump_offer,
        )
        self.assertTrue(delay_mock.called, 'Pixel deveria ter sido enfileirado mesmo sem pixel no bump')

    @mock.patch('apps.services.event_manager.process_pixel_event.delay')
    def test_bump_without_pixel_and_flag_true_should_block(self, delay_mock):
        self.tracking_pixels_bump_offer.delete()

        self.product.disable_orderbump_pixel_events = True
        self.product.save(update_fields=['disable_orderbump_pixel_events'])

        dispatch_pixel_events(
            'initiate_checkout',
            order=self.order,
            offer=self.bump_offer,
        )
        delay_mock.assert_not_called()

    @mock.patch('apps.services.event_manager.process_pixel_event.delay')
    def test_explicit_tracking_pixels_should_not_be_overwritten(self, delay_mock):
        dispatch_pixel_events(
            'initiate_checkout',
            order=self.order,
            offer=self.bump_offer,
            tracking_pixels=self.tracking_pixels_bump_offer
        )
        self.assertTrue(delay_mock.called, 'Pixel explícito deveria ser respeitado')
