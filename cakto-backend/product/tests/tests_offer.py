import json
import os
from unittest import mock

from django.contrib.auth.models import Permission
from django.contrib.contenttypes.models import ContentType
from django.core.cache import cache
from django.test import override_settings
from django.urls import reverse
from django.utils import timezone
from rest_framework import status

from cakto.tests.base import BaseTestCase
from product.enums import ProductType
from product.models import Checkout, Link, Offer, OrderBump, PaymentMethod, Product
from product.serializers import OfferSerializer


@override_settings(STORAGES={
    'default': {
        'BACKEND': 'django.core.files.storage.InMemoryStorage',
    },
})
class OfferTest(BaseTestCase):
    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()
        cls.create_paymentMethods()

        cls.credit_card, _ = PaymentMethod.objects.get_or_create(type='credit_card', defaults={'name': 'Cartão de Crédito'})
        cls.boleto, _ = PaymentMethod.objects.get_or_create(type='boleto', defaults={'name': '<PERSON><PERSON><PERSON>'})
        cls.pix, _ = PaymentMethod.objects.get_or_create(type='pix', defaults={'name': 'Pix'})

        cls.product = cls.create_product(user=cls.user, paymentMethod='credit_card')
        cls.product.paymentMethods.set([cls.credit_card, cls.pix])

        cls.headers = cls.build_user_auth_headers(cls.user)

    def test_offer_cache_key(self):
        offer = self.product.offers.first()

        self.assertEqual(offer.cache_key, Offer.cache_prefix + offer.id, 'Cache key should be class Offer cache prefix + offer id')

    def test_offer_invalidate_cache_on_save(self):
        offer = self.product.offers.first()

        cache_value = 'Test Invalidate Cache'
        cache.set(offer.cache_key, cache_value, None)

        offer.name = 'Updated'
        offer.save()

        self.assertEqual(cache.get(offer.cache_key), None, 'Cache key should be invalidated')

    def test_offer_invalidate_cache_with_custom_checkout(self):
        offer = self.product.offers.first()
        checkout = Checkout.objects.create(product=self.product, name='Checkout 1')
        checkout.offers.set([offer])

        cache_key = '{}{}_{}'.format(Offer.cache_prefix, offer.id, checkout.id)
        cache.set(cache_key, 'Test Invalidate Cache', None)

        offer.save()

        self.assertEqual(cache.get(cache_key), None, 'Cache key should be invalidated')

    def test_offer_bulk_invalidade_cache(self):
        offer = self.product.offers.first()
        offer_2 = Offer.objects.create(product=self.product, price=20.0, name='Offer 2')

        cache_value = 'Test Invalidate Cache'

        cache.set(offer.cache_key, cache_value, None)
        cache.set(offer_2.cache_key, cache_value, None)

        Offer.bulk_invalidate_cache([offer, offer_2])

        self.assertEqual(cache.get(offer.cache_key), None, 'Cache key should be invalidated')
        self.assertEqual(cache.get(offer_2.cache_key), None, 'Cache key should be invalidated')

    def test_offer_list(self):
        url = reverse('offers')

        offer = self.product.offers.first()
        offer_2 = Offer.objects.create(product=self.product, price=20.0, name='Offer 2')

        expected_response = {
            'count': 2,
            'next': None,
            'previous': None,
            'results': [
                {
                    'id': offer.pk,
                    'name': offer.name,
                    'image': offer.image.url if offer.image else None,
                    'price': offer.price,
                    'interval': offer.interval,
                    'intervalType': offer.intervalType,
                    'default': True,
                    'status': 'active',
                    'cost_per_item': None,
                    'profit_per_item': None,
                    'type': offer.type,
                    'product': offer.product.pk,
                    'recurrence_period': offer.recurrence_period,
                    'quantity_recurrences': offer.quantity_recurrences,
                    'trial_days': offer.trial_days,
                    'max_retries': offer.max_retries,
                    'retry_interval': offer.retry_interval,
                },
                {
                    'id': offer_2.pk,
                    'name': offer_2.name,
                    'image': offer_2.image.url if offer_2.image else None,
                    'price': offer_2.price,
                    'interval': offer_2.interval,
                    'intervalType': offer_2.intervalType,
                    'default': False,
                    'status': 'active',
                    'cost_per_item': None,
                    'profit_per_item': None,
                    'type': offer_2.type,
                    'product': offer_2.product.pk,
                    'recurrence_period': offer.recurrence_period,
                    'quantity_recurrences': offer_2.quantity_recurrences,
                    'trial_days': offer_2.trial_days,
                    'max_retries': offer_2.max_retries,
                    'retry_interval': offer_2.retry_interval,
                }
            ]
        }

        response = self.client.get(url, headers=self.headers)

        self.assertEqual(response.status_code, status.HTTP_200_OK, response.content.decode())
        self.assertEqual(response.json(), expected_response, response.content.decode())

    def test_offer_list_does_not_return_deleted_offers(self):
        url = reverse('offers')

        offer = self.product.offers.first()
        Offer.objects.create(product=self.product, price=20.0, name='Offer 2', status='deleted')

        expected_response = {
            'count': 1,
            'next': None,
            'previous': None,
            'results': [
                {
                    'id': offer.pk,
                    'name': offer.name,
                    'image': offer.image.url if offer.image else None,
                    'price': offer.price,
                    'interval': offer.interval,
                    'intervalType': offer.intervalType,
                    'default': True,
                    'status': 'active',
                    'cost_per_item': None,
                    'profit_per_item': None,
                    'type': offer.type,
                    'product': offer.product.pk,
                    'recurrence_period': offer.recurrence_period,
                    'quantity_recurrences': offer.quantity_recurrences,
                    'trial_days': offer.trial_days,
                    'max_retries': offer.max_retries,
                    'retry_interval': offer.retry_interval,
                }
            ]
        }

        response = self.client.get(url, headers=self.headers)

        self.assertEqual(response.status_code, status.HTTP_200_OK, response.content.decode())
        self.assertEqual(response.json(), expected_response, response.content.decode())

    def test_offer_list_does_not_return_other_user_offers(self):
        Product.objects.all().delete()
        Offer.objects.all().delete()

        url = reverse('offers')

        self.create_product(user=self.create_user())

        expected_response = {
            'count': 0,
            'next': None,
            'previous': None,
            'results': []
        }

        response = self.client.get(url, headers=self.headers)

        self.assertEqual(Offer.objects.count(), 1)
        self.assertEqual(response.status_code, status.HTTP_200_OK, response.content.decode())
        self.assertEqual(response.json(), expected_response, response.content.decode())

    def test_offer_retrieve(self):
        offer = self.product.offers.first()

        url = reverse('offer', args=[offer.pk])

        expected_response = {
            'id': offer.pk,
            'name': offer.name,
            'image': offer.image.url if offer.image else None,
            'price': float(offer.price),
            'interval': offer.interval,
            'intervalType': offer.intervalType,
            'default': True,
            'status': 'active',
            'cost_per_item': None,
            'profit_per_item': None,
            'type': offer.type,
            'product': offer.product.pk,
            'recurrence_period': offer.recurrence_period,
            'quantity_recurrences': offer.quantity_recurrences,
            'trial_days': offer.trial_days,
            'max_retries': offer.max_retries,
            'retry_interval': offer.retry_interval,
        }

        response = self.client.get(url, headers=self.headers)

        self.assertEqual(response.status_code, status.HTTP_200_OK, response.content.decode())
        self.assertEqual(response.json(), expected_response, response.content.decode())

    def test_offer_retrieve_does_not_return_other_user_offer(self):
        offer = Offer.objects.create(
            product=self.create_product(user=self.create_user()),
            price=20.0,
            name='Offer 2'
        )

        url = reverse('offer', args=[offer.pk])

        response = self.client.get(url, headers=self.headers)

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND, response.content.decode())

    def test_offer_update(self):
        offer: Offer = self.product.offers.first()

        url = reverse('offer', args=[offer.pk])

        new_data = {
            'name': 'Updated Offer',
            'price': 30.0,
            'interval': 2,
            'intervalType': 'month',
            'status': 'disabled',
            'type': 'subscription',
            'recurrence_period': 120,
            'quantity_recurrences': 23,
            'trial_days': 22,
            'max_retries': 21,
            'retry_interval': 20,
        }

        response = self.client.put(url, data=new_data, headers=self.headers)

        offer.refresh_from_db()

        self.assertEqual(response.status_code, status.HTTP_200_OK, response.content.decode())
        self.assertEqual(offer.name, new_data['name'])
        self.assertEqual(offer.price, new_data['price'])
        self.assertEqual(offer.interval, new_data['interval'])
        self.assertEqual(offer.intervalType, new_data['intervalType'])
        self.assertEqual(offer.status, new_data['status'])
        self.assertEqual(offer.type, new_data['type'])
        self.assertEqual(offer.recurrence_period, new_data['recurrence_period'])
        self.assertEqual(offer.quantity_recurrences, new_data['quantity_recurrences'])
        self.assertEqual(offer.trial_days, new_data['trial_days'])
        self.assertEqual(offer.max_retries, new_data['max_retries'])
        self.assertEqual(offer.retry_interval, new_data['retry_interval'])

    def test_offer_update_does_not_update_product(self):
        offer: Offer = self.product.offers.first()

        new_product = self.create_product(user=self.user)

        url = reverse('offer', args=[offer.pk])

        new_data = {
            'name': 'Updated Offer',
            'price': 30.0,
            'interval': 2,
            'intervalType': 'month',
            'status': 'disabled',
            'type': 'subscription',
            'product': new_product.pk,
        }

        response = self.client.put(url, data=new_data, headers=self.headers)

        self.assertEqual(response.status_code, status.HTTP_200_OK, response.content.decode())
        self.assertEqual(offer.product, self.product)

    def test_offer_update_does_not_update_other_user_offer(self):
        product = self.create_product(user=self.create_user())
        offer = Offer.objects.create(
            product=product,
            price=20.0,
            name='Offer 2'
        )

        url = reverse('offer', args=[offer.pk])

        new_data = {
            'name': 'Updated Offer',
            'price': 30.0,
            'interval': 2,
            'intervalType': 'month',
            'status': 'disabled',
            'type': 'subscription',
        }

        response = self.client.put(url, data=new_data, headers=self.headers)

        offer.refresh_from_db()

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND, response.content.decode())
        self.assertEqual(offer.name, 'Offer 2')
        self.assertEqual(offer.price, 20.0)
        self.assertEqual(offer.product, product)

    def test_offer_update_not_found(self):
        url = reverse('offer', kwargs={'pk': 'non-existent-id'})

        image_file = self.create_test_image()

        response = self.client.put(
            url,
            data={'image': image_file},
            format='multipart',
            headers=self.headers
        )

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND, response.content.decode())
        self.assertEqual(response.json()['detail'], 'Não encontrado.')

    def test_offer_update_without_authentication_returns_unauthorized(self):
        offer = self.product.offers.first()
        url = reverse('offer', kwargs={'pk': offer.pk})

        image_file = self.create_test_image()

        response = self.client.put(
            url,
            data={'image': image_file},
            format='multipart'
        )

        self.assertEqual(
            response.status_code,
            status.HTTP_401_UNAUTHORIZED,
            response.content.decode()
        )
        self.assertEqual(
            response.json()['detail'],
            'As credenciais de autenticação não foram fornecidas.',
            response.content.decode()
        )

    def test_offer_delete(self):
        offer = Offer.objects.create(product=self.product, price=20.0, name='Offer 2')

        url = reverse('offer', args=[offer.pk])

        response = self.client.delete(url, headers=self.headers)

        offer.refresh_from_db()

        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        self.assertEqual(offer.status, 'deleted')

    def test_offer_delete_not_found(self):
        url = reverse('offer', kwargs={'pk': 'non-existent-id'})

        response = self.client.delete(url, headers=self.headers)

        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND, response.content.decode())
        self.assertEqual(response.json()['detail'], 'Não encontrado.')

    def test_offer_delete_without_authentication_returns_unauthorized(self):
        offer = self.product.offers.first()
        url = reverse('offer', kwargs={'pk': offer.pk})

        response = self.client.delete(url)

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED, response.content.decode())
        self.assertEqual(
            response.json()['detail'],
            'As credenciais de autenticação não foram fornecidas.',
            response.content.decode()
        )

    def test_default_offer_can_not_be_deleted(self):
        offer = self.product.offers.first()

        url = reverse('offer', args=[offer.pk])

        response = self.client.delete(url, headers=self.headers)

        offer.refresh_from_db()

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(offer.status, 'active')

    def test_offers_are_not_deleted_on_product_update(self):
        url = reverse('product', args=[self.product.pk])
        offer = self.product.offers.first()  # type:ignore
        offer_2 = Offer.objects.create(product=self.product, price=20.0, name='Offer 2')

        payload = {
            'name': 'Updated Product',
            'description': 'A new description',
            'price': 30.0,
            'offers': [
                {
                    'id': offer.pk,
                    'price': 10.0,
                    'name': 'Offer 1',
                },
                {
                    'id': offer_2.pk,
                    'price': 20.0,
                    'name': 'Offer 2',
                    'delete': True,
                }
            ]
        }

        self.client.put(url, data=json.dumps(payload), headers=self.headers, content_type='application/json')
        self.product.refresh_from_db()

        self.assertEqual(Product.objects.first().name, payload['name'])  # type:ignore
        self.assertEqual(self.product.offers.filter(status='active').count(), 2)  # type:ignore

    def test_link_is_deleted_when_offer_is_deleted(self):
        offer = Offer.objects.create(product=self.product, price=20.0, name='Offer 2')

        Link.objects.create(
            product=self.product,
            offer=offer,
            name='Test Link',
            type='checkout',
            url=f'https://pay.cakto.com.br/{offer.id}'
        )

        url = reverse('offer', args=[offer.pk])

        response = self.client.delete(url, headers=self.headers)

        self.assertEqual(response.status_code, 204)  # type:ignore
        self.assertEqual(Link.objects.count(), 0)

    def test_order_bump_is_deleted_when_its_offer_is_deleted(self):
        offer = Offer.objects.create(product=self.product, price=20.0, name='Offer 2')

        url = reverse('offer', args=[offer.pk])

        OrderBump.objects.create(product=self.product, offer=offer)

        response = self.client.delete(url, headers=self.headers)

        self.assertEqual(response.status_code, 204)  # type:ignore
        self.assertEqual(OrderBump.objects.count(), 0)

    def test_offer_is_removed_from_checkout_when_is_deleted(self):
        offer = Offer.objects.create(product=self.product, price=20.0, name='Offer 2')

        url = reverse('offer', args=[offer.pk])

        checkout = Checkout.objects.create(product=self.product, name='Test Checkout')
        checkout.offers.add(offer)

        response = self.client.delete(url, headers=self.headers)

        self.assertEqual(response.status_code, 204)  # type:ignore
        self.assertEqual(checkout.offers.count(), 0)

    def test_offer_invalidate_cache_when_deleted(self):
        offer = Offer.objects.create(product=self.product, price=20.0, name='Offer 2')

        url = reverse('offer', args=[offer.pk])

        cache.set((offer.cache_key), 'Test Invalidate Cache', None)

        response = self.client.delete(url, headers=self.headers)

        self.assertEqual(response.status_code, 204)  # type:ignore

        self.assertEqual(cache.get(offer.cache_key), None)

    def test_offer_invalidate_cache_when_product_save(self):
        offer = self.product.offers.first()  # type:ignore

        cache.set(offer.cache_key, 'Test Invalidate Cache', None)

        self.product.save()

        self.assertEqual(cache.get(offer.cache_key), None)

    def test_offer_serializer_validate_intervalType(self):
        serializer = OfferSerializer(data={
            'name': 'Test Offer',
            'price': 10.0,
            'interval': 1,
            'intervalType': 'invalid_interval',
            'type': 'unique',
            'status': 'active',
        })
        self.assertFalse(serializer.is_valid())
        self.assertIn('intervalType', serializer.errors)
        self.assertEqual(serializer.errors['intervalType'][0], '"invalid_interval" não é um escolha válido.', serializer.errors)  # type: ignore

    def test_offer_serializer_validate_price(self):
        serializer = OfferSerializer(data={
            'name': 'Test Offer',
            'price': 4.0,
            'interval': 1,
            'intervalType': 'month',
            'type': 'unique',
            'status': 'active',
        })
        self.assertFalse(serializer.is_valid())
        self.assertIn('price', serializer.errors)
        self.assertEqual(serializer.errors['price'][0], 'O preço mínimo do produto é de R$ 5,00', serializer.errors)  # type: ignore

    def test_offer_serializer_validate_interval(self):
        serializer = OfferSerializer(data={
            'name': 'Test Offer',
            'price': 10.0,
            'interval': 0,
            'intervalType': 'month',
            'type': 'unique',
            'status': 'active',
        })
        self.assertFalse(serializer.is_valid())
        self.assertIn('interval', serializer.errors)
        self.assertEqual(serializer.errors['interval'][0], 'O intervalo de tempo deve ser maior que 0.', serializer.errors)  # type: ignore

    def test_offer_serializer_validate_type(self):
        serializer = OfferSerializer(data={
            'name': 'Test Offer',
            'price': 10.0,
            'interval': 1,
            'intervalType': 'month',
            'type': 'invalid_type',
            'status': 'active',
        })
        self.assertFalse(serializer.is_valid())
        self.assertIn('type', serializer.errors)
        self.assertEqual(serializer.errors['type'][0], '"invalid_type" não é um escolha válido.', serializer.errors)  # type: ignore

    def test_offer_serializer_validate_status(self):
        serializer = OfferSerializer(data={
            'name': 'Test Offer',
            'price': 10.0,
            'interval': 1,
            'intervalType': 'month',
            'type': 'unique',
            'status': 'deleted',
        })
        self.assertFalse(serializer.is_valid())
        self.assertIn('status', serializer.errors)
        self.assertEqual(serializer.errors['status'][0], 'O status da oferta deve ser "active" ou "disabled".', serializer.errors)  # type: ignore

    def test_offer_serializer_validate_quantity_recurrences_with_value_lower_than_1_and_different_than_minus_1(self):
        serializer = OfferSerializer(data={
            'name': 'Test Offer',
            'price': 10.0,
            'interval': 1,
            'intervalType': 'month',
            'type': 'unique',
            'status': 'active',
            'quantity_recurrences': 0,
        })
        self.assertFalse(serializer.is_valid())
        self.assertIn('quantity_recurrences', serializer.errors)
        self.assertEqual(
            serializer.errors['quantity_recurrences'][0],  # type:ignore
            'O número de recorrências deve estar entre 1 e 100 ou ser igual a -1 para recorrência infinita.',
            serializer.errors
        )

    def test_offer_serializer_validate_quantity_recurrences_with_value_higher(self):
        serializer = OfferSerializer(data={
            'name': 'Test Offer',
            'price': 10.0,
            'interval': 1,
            'intervalType': 'month',
            'type': 'unique',
            'status': 'active',
            'quantity_recurrences': 101,
        })
        self.assertFalse(serializer.is_valid())
        self.assertIn('quantity_recurrences', serializer.errors)
        self.assertEqual(
            serializer.errors['quantity_recurrences'][0],  # type:ignore
            'O número de recorrências deve estar entre 1 e 100 ou ser igual a -1 para recorrência infinita.',
            serializer.errors
        )

    def test_offer_serializer_validate_trial_days_with_negative_value(self):
        serializer = OfferSerializer(data={
            'name': 'Test Offer',
            'price': 10.0,
            'interval': 1,
            'intervalType': 'month',
            'type': 'unique',
            'status': 'active',
            'trial_days': -1,
        })
        self.assertFalse(serializer.is_valid())
        self.assertIn('trial_days', serializer.errors)
        self.assertEqual(
            serializer.errors['trial_days'][0],  # type:ignore
            'O número de dias de teste deve estar entre 0 e 365.',
            serializer.errors
        )

    def test_offer_serializer_validate_trial_days_with_value_higher_than_365(self):
        serializer = OfferSerializer(data={
            'name': 'Test Offer',
            'price': 10.0,
            'interval': 1,
            'intervalType': 'month',
            'type': 'unique',
            'status': 'active',
            'trial_days': 366,
        })
        self.assertFalse(serializer.is_valid())
        self.assertIn('trial_days', serializer.errors)
        self.assertEqual(
            serializer.errors['trial_days'][0],  # type:ignore
            'O número de dias de teste deve estar entre 0 e 365.',
            serializer.errors
        )

    def test_offer_create_success(self):
        url = reverse('offers')
        data = {
            'name': 'New Offer',
            'price': 50.0,
            'interval': 1,
            'intervalType': 'month',
            'type': 'subscription',
            'status': 'disabled',
            'product': self.product.id,
            'recurrence_period': 120,
            'quantity_recurrences': 23,
            'trial_days': 22,
            'max_retries': 21,
            'retry_interval': 20,
        }

        response = self.client.post(url, data, headers=self.headers, format='json')

        self.assertEqual(response.status_code, status.HTTP_201_CREATED, response.content.decode())
        self.assertEqual(Offer.objects.count(), 2)

        offer: Offer = Offer.objects.last()  # type:ignore
        self.assertEqual(offer.name, data['name'])
        self.assertEqual(offer.price, data['price'])
        self.assertEqual(offer.interval, data['interval'])
        self.assertEqual(offer.intervalType, data['intervalType'])
        self.assertEqual(offer.type, data['type'])
        self.assertEqual(offer.status, data['status'])
        self.assertEqual(offer.product, self.product)
        self.assertEqual(offer.recurrence_period, data['recurrence_period'])
        self.assertEqual(offer.quantity_recurrences, data['quantity_recurrences'])
        self.assertEqual(offer.trial_days, data['trial_days'])
        self.assertEqual(offer.max_retries, data['max_retries'])
        self.assertEqual(offer.retry_interval, data['retry_interval'])

    def test_offer_create_with_image_success(self):
        Offer.objects.all().delete()  # Clear existing offers

        url = reverse('offers')
        image_file = self.create_test_image()

        data = {
            'name': 'Offer with Image',
            'price': 50.0,
            'type': 'unique',
            'product': self.product.id,
            'image': image_file,
        }

        response = self.client.post(url, data, headers=self.headers, format='multipart')

        self.assertEqual(response.status_code, status.HTTP_201_CREATED, response.content.decode())
        self.assertIn('image', response.json())
        self.assertTrue(response.json()['image'].endswith('.png'))

        offer: Offer = Offer.objects.first()  # type:ignore
        self.assertIsNotNone(offer.image)
        self.assertTrue(offer.image.name.endswith('.png'))

    def test_offer_create_creates_link(self):
        url = reverse('offers')
        data = {
            'name': 'New Offer',
            'price': 50.0,
            'interval': 1,
            'intervalType': 'month',
            'type': 'subscription',
            'status': 'disabled',
            'product': self.product.id,
        }

        response = self.client.post(url, data, headers=self.headers, format='json')

        self.assertEqual(response.status_code, status.HTTP_201_CREATED, response.content.decode())
        self.assertEqual(Link.objects.count(), 1)

        link: Link = Link.objects.last()  # type:ignore
        offer = Offer.objects.get(id=response.json()['id'])

        self.assertEqual(link.offer, offer)
        self.assertEqual(link.name, offer.name)
        self.assertEqual(link.product, offer.product)
        self.assertEqual(link.type, 'checkout')
        self.assertEqual(
            link.url,
            f'{os.getenv("CHECKOUT_BASE_URL","https://pay.cakto.com.br")}/{offer.id}'
        )

    def test_offer_create_adds_offer_to_default_checkout(self):
        url = reverse('offers')

        data = {
            'name': 'New Offer',
            'price': 50.0,
            'interval': 1,
            'intervalType': 'month',
            'type': 'subscription',
            'status': 'disabled',
            'product': self.product.id,
        }

        checkout = Checkout.objects.create(
            product=self.product,
            name='Default Checkout',
            default=True
        )

        response = self.client.post(url, data, headers=self.headers, format='json')

        self.assertEqual(response.status_code, status.HTTP_201_CREATED, response.content.decode())

        offer = Offer.objects.get(id=response.json()['id'])

        self.assertEqual(checkout, offer.checkouts.first())
        self.assertEqual(checkout.offers.first(), offer)
        self.assertEqual(checkout.product, offer.product)

    def test_offer_can_not_create_with_other_users_product(self):
        product = self.create_product(user=self.create_user())
        url = reverse('offers')
        data = {
            'name': 'New Offer',
            'price': 50.0,
            'interval': 1,
            'intervalType': 'month',
            'type': 'subscription',
            'status': 'disabled',
            'product': product.id,
        }

        existent_offers_count = Offer.objects.count()

        response = self.client.post(url, data, headers=self.headers, format='json')

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST, response.content.decode())
        self.assertEqual(Offer.objects.count(), existent_offers_count)

    def test_offer_can_not_create_with_default_offer(self):
        Offer.objects.all().delete()

        url = reverse('offers')
        data = {
            'name': 'New Offer',
            'price': 50.0,
            'interval': 1,
            'intervalType': 'month',
            'type': 'subscription',
            'status': 'active',
            'product': self.product.id,
            'default': True,
        }

        response = self.client.post(url, data, headers=self.headers, format='json')

        self.assertEqual(response.status_code, status.HTTP_201_CREATED, response.content.decode())
        self.assertEqual(Offer.objects.count(), 1)
        self.assertEqual(Offer.objects.first().default, False)  # type:ignore

    def test_offer_create_invalid_price(self):
        url = reverse('offers')
        data = {
            'name': 'New Offer',
            'price': 4.0,
            'interval': 1,
            'intervalType': 'month',
            'type': 'subscription',
            'status': 'active',
            'product': self.product.id,
        }

        response = self.client.post(url, data, headers=self.headers, format='json')

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST, response.content.decode())
        self.assertIn('price', response.json())
        self.assertEqual(response.json()['price'][0], 'O preço mínimo do produto é de R$ 5,00')

    def test_offer_create_invalid_intervalType(self):
        url = reverse('offers')
        data = {
            'name': 'New Offer',
            'price': 50.0,
            'interval': 1,
            'intervalType': 'invalid_interval',
            'type': 'subscription',
            'status': 'active',
            'product': self.product.id,
        }

        response = self.client.post(url, data, headers=self.headers, format='json')

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST, response.content.decode())
        self.assertIn('intervalType', response.json())
        self.assertEqual(response.json()['intervalType'][0], '"invalid_interval" não é um escolha válido.')

    def test_offer_create_invalid_type(self):
        url = reverse('offers')
        data = {
            'name': 'New Offer',
            'price': 50.0,
            'interval': 1,
            'intervalType': 'month',
            'type': 'invalid_type',
            'status': 'active',
            'product': self.product.id,
        }

        response = self.client.post(url, data, headers=self.headers, format='json')

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST, response.content.decode())
        self.assertIn('type', response.json())
        self.assertEqual(response.json()['type'][0], '"invalid_type" não é um escolha válido.')

    def test_offer_create_invalid_status(self):
        url = reverse('offers')
        data = {
            'name': 'New Offer',
            'price': 50.0,
            'interval': 1,
            'intervalType': 'month',
            'type': 'subscription',
            'status': 'deleted',
            'product': self.product.id,
        }

        response = self.client.post(url, data, headers=self.headers, format='json')

        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST, response.content.decode())
        self.assertIn('status', response.json())
        self.assertEqual(response.json()['status'][0], 'O status da oferta deve ser "active" ou "disabled".')

    @mock.patch('product.views.send_offer_data_to_compliance_analysis')
    def test_offer_create_send_data_to_compliance(self, mock_send_offer_data):
        url = reverse('offers')
        data = {
            'name': 'New Offer',
            'price': 50.0,
            'interval': 1,
            'intervalType': 'month',
            'type': 'subscription',
            'status': 'disabled',
            'product': self.product.id,
        }

        response = self.client.post(url, data, headers=self.headers, format='json')

        self.assertEqual(response.status_code, status.HTTP_201_CREATED, response.content.decode())
        self.assertEqual(Offer.objects.count(), 2)
        mock_send_offer_data.assert_called_once()

    @mock.patch('product.views.send_offer_data_to_compliance_analysis')
    def test_offer_update_send_data_to_compliance(self, mock_send_offer_data):
        offer = self.product.offers.first()
        url = reverse('offer', args=[offer.pk])

        new_data = {
            'name': 'Updated Offer',
            'price': 30.0,
            'interval': 2,
            'intervalType': 'month',
            'status': 'disabled',
            'type': 'subscription',
        }

        response = self.client.put(url, data=new_data, headers=self.headers)

        self.assertEqual(response.status_code, status.HTTP_200_OK, response.content.decode())
        mock_send_offer_data.assert_called_once()

    def test_offer_filter_by_id(self):
        url = reverse('offers')

        offer = self.product.offers.first()

        Offer.objects.create(product=self.product, price=20, name='Offer 2')

        response = self.client.get(f'{url}?id={offer.id}', headers=self.headers)

        self.assertTrue(Offer.objects.count() > 1)
        self.assertEqual(response.status_code, status.HTTP_200_OK, response.content.decode())
        self.assertEqual(response.json()['count'], 1)
        self.assertEqual(response.json()['results'][0]['id'], offer.id)

    def test_offer_filter_by_product(self):
        url = reverse('offers')

        product_2 = self.create_product(user=self.user)

        offer_2 = product_2.offers.first()

        response = self.client.get(f'{url}?product={product_2.id}', headers=self.headers)

        self.assertTrue(Offer.objects.count() > 1)
        self.assertEqual(response.status_code, status.HTTP_200_OK, response.content.decode())
        self.assertEqual(response.json()['count'], 1)
        self.assertEqual(response.json()['results'][0]['id'], offer_2.id)

    def test_offer_filter_by_name(self):
        url = reverse('offers')

        offer = self.product.offers.first()

        Offer.objects.create(product=self.product, name='Another offer', price=20)

        response = self.client.get(f'{url}?name=Another', headers=self.headers)

        self.assertEqual(response.status_code, status.HTTP_200_OK, response.content.decode())
        self.assertEqual(response.json()['count'], 1)
        self.assertNotEqual(response.json()['results'][0]['id'], offer.id)

    def test_offer_filter_by_intervalType(self):
        url = reverse('offers')

        offer = self.product.offers.first()
        offer.intervalType = 'week'
        offer.save(update_fields=['intervalType'])

        offer_2 = Offer.objects.create(product=self.product, name='Monthly offer', price=20, intervalType='month')

        response = self.client.get(f'{url}?intervalType=month', headers=self.headers)

        self.assertEqual(response.status_code, status.HTTP_200_OK, response.content.decode())
        self.assertEqual(response.json()['count'], 1)
        self.assertEqual(response.json()['results'][0]['id'], offer_2.id)

    def test_offer_filter_by_type(self):
        url = reverse('offers')

        offer = self.product.offers.first()
        offer.type = 'unique'
        offer.save(update_fields=['type'])

        offer_2 = Offer.objects.create(
            product=self.product,
            name='Subscription offer',
            price=20,
            type='subscription'
        )

        response = self.client.get(f'{url}?type=subscription', headers=self.headers)

        self.assertEqual(response.status_code, status.HTTP_200_OK, response.content.decode())
        self.assertEqual(response.json()['count'], 1)
        self.assertEqual(response.json()['results'][0]['id'], offer_2.id)

    def test_offer_filter_by_status(self):
        url = reverse('offers')

        offer_2 = Offer.objects.create(product=self.product, name='Disabled offer', price=20, status='disabled')

        response = self.client.get(f'{url}?status=disabled', headers=self.headers)

        self.assertTrue(Offer.objects.count() > 1)
        self.assertEqual(response.status_code, status.HTTP_200_OK, response.content.decode())
        self.assertEqual(response.json()['count'], 1)
        self.assertEqual(response.json()['results'][0]['id'], offer_2.id)

    def test_offer_filter_by_default(self):
        url = reverse('offers')

        offer = self.product.offers.first()
        offer.default = True
        offer.save(update_fields=['default'])

        Offer.objects.create(product=self.product, name='Non default offer', price=20, default=False)

        response = self.client.get(f'{url}?default=true', headers=self.headers)

        self.assertEqual(response.status_code, status.HTTP_200_OK, response.content.decode())
        self.assertEqual(response.json()['count'], 1)
        self.assertEqual(response.json()['results'][0]['id'], offer.id)

    def test_offer_filter_by_price_range(self):
        url = reverse('offers')

        offer = self.product.offers.first()
        offer.price = 25
        offer.save(update_fields=['price'])

        offer_2 = Offer.objects.create(product=self.product, name='Expensive offer', price=100)

        Offer.objects.create(product=self.product, name='Cheap offer', price=160)

        response = self.client.get(f'{url}?price__gte=50&price__lte=150', headers=self.headers)

        self.assertEqual(response.status_code, status.HTTP_200_OK, response.content.decode())
        self.assertEqual(response.json()['count'], 1)
        self.assertEqual(response.json()['results'][0]['id'], offer_2.id)

    def test_offer_filter_by_interval_range(self):
        url = reverse('offers')

        offer = self.product.offers.first()
        offer.interval = 1
        offer.save(update_fields=['interval'])

        offer_2 = Offer.objects.create(product=self.product, name='Monthly offer', price=20, interval=2)

        Offer.objects.create(product=self.product, name='Quarterly offer', price=30, interval=4)

        response = self.client.get(f'{url}?interval__gte=2&interval__lte=3', headers=self.headers)

        self.assertEqual(response.status_code, status.HTTP_200_OK, response.content.decode())
        self.assertEqual(response.json()['count'], 1)
        self.assertEqual(response.json()['results'][0]['id'], offer_2.id)

    def test_offer_filter_by_createdAt_range(self):
        url = reverse('offers')

        now = timezone.datetime.strptime('2021-01-01T12:00:00Z-0000', '%Y-%m-%dT%H:%M:%SZ%z')

        offer = self.product.offers.first()
        offer.createdAt = now - timezone.timedelta(days=2)

        offer_2 = Offer.objects.create(product=self.product, name='New offer', price=20)
        offer_2.createdAt = now

        offer_3 = Offer.objects.create(product=self.product, name='Old offer', price=30)
        offer_3.createdAt = now + timezone.timedelta(days=2)

        Offer.objects.bulk_update([offer, offer_2, offer_3], ['createdAt'])

        start = (now - timezone.timedelta(days=1)).strftime('%Y-%m-%dT%H:%M:%SZ')
        end = (now + timezone.timedelta(days=1)).strftime('%Y-%m-%dT%H:%M:%SZ')

        response = self.client.get(
            f'{url}?createdAt__gte={start}&createdAt__lte={end}',
            headers=self.headers
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK, response.content.decode())
        self.assertEqual(response.json()['count'], 1)
        self.assertEqual(response.json()['results'][0]['id'], offer_2.id)

    def test_offer_filter_by_updatedAt_range(self):
        url = reverse('offers')

        now = timezone.datetime.strptime('2021-01-01T12:00:00Z-0000', '%Y-%m-%dT%H:%M:%SZ%z')

        offer = self.product.offers.first()
        offer.updatedAt = now - timezone.timedelta(days=2)

        offer_2 = Offer.objects.create(product=self.product, name='New offer', price=20)
        offer_2.updatedAt = now

        offer_3 = Offer.objects.create(product=self.product, name='Old offer', price=30)
        offer_3.updatedAt = now + timezone.timedelta(days=2)

        Offer.objects.bulk_update([offer, offer_2, offer_3], ['updatedAt'])

        start = (now - timezone.timedelta(days=1)).strftime('%Y-%m-%dT%H:%M:%SZ')
        end = (now + timezone.timedelta(days=1)).strftime('%Y-%m-%dT%H:%M:%SZ')

        response = self.client.get(
            f'{url}?updatedAt__gte={start}&updatedAt__lte={end}',
            headers=self.headers
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK, response.content.decode())
        self.assertEqual(response.json()['count'], 1)
        self.assertEqual(response.json()['results'][0]['id'], offer_2.id)

    def test_offer_filter_combined_filters(self):
        url = reverse('offers')
        offer_2 = Offer.objects.create(
            product=self.product,
            name='Test offer',
            price=30,
            type='subscription',
            status='active'
        )

        filters = {
            'name': 'Test',
            'type': 'subscription',
            'status': 'active',
            'price_gte': '25',
            'price_lte': '35'
        }
        query_string = '&'.join([f'{k}={v}' for k, v in filters.items()])

        response = self.client.get(f'{url}?{query_string}', headers=self.headers)

        self.assertTrue(Offer.objects.count() > 1)
        self.assertEqual(response.status_code, status.HTTP_200_OK, response.content.decode())
        self.assertEqual(response.json()['count'], 1)
        self.assertEqual(response.json()['results'][0]['id'], offer_2.id)

    def test_offer_filter_no_results(self):
        url = reverse('offers')

        response = self.client.get(f'{url}?name=NonExistent', headers=self.headers)

        self.assertTrue(Offer.objects.count() > 0)
        self.assertEqual(response.status_code, status.HTTP_200_OK, response.content.decode())
        self.assertEqual(response.json()['count'], 0)
        self.assertEqual(len(response.json()['results']), 0)

    def test_create_offer_subscription_with_product_that_has_unsuported_paymentMethod_raises_400(self):
        url = reverse('offers')

        self.product.paymentMethods.set([self.googlepay])

        data = {
            'name': 'New Offer',
            'price': 50.0,
            'type': ProductType.SUBSCRIPTION.id,
            'product': self.product.id,
        }

        # Call
        response = self.client.post(url, data, headers=self.headers, format='json')

        self.assertEqual(
            response.status_code,
            status.HTTP_400_BAD_REQUEST,
            response.content.decode()
        )

        self.assertEqual(
            response.json(),
            {
                'product':
                ['Remova o método de pagamento Google Pay'
                 f' do {self.product.name[:30]} para adicionar uma oferta '
                 'do tipo assinatura.']
            }
        )

    def test_update_offer_to_subscription_with_product_that_has_unsuported_paymentMethod_raises_400(self):
        offer = self.product.offers.first()

        url = reverse('offer', args=[offer.id])

        offer.type = ProductType.UNIQUE.id
        offer.save(update_fields=['type'])

        self.product.paymentMethods.set([self.googlepay])

        data = {
            'name': 'Offer Updated',
            'price': 50.0,
            'type': ProductType.SUBSCRIPTION.id,
            'product': self.product.id,
        }

        # Call
        response = self.client.put(url, data, headers=self.headers, format='json')

        self.assertEqual(
            response.status_code,
            status.HTTP_400_BAD_REQUEST,
            response.content.decode()
        )

        self.assertEqual(
            response.json(),
            {
                'product':
                ['Remova o método de pagamento Google Pay'
                 f' do {self.product.name[:30]} para alterar oferta '
                 'para o tipo assinatura.']
            }
        )

    def __create_block_product_permission(self):
        content_type = ContentType.objects.get_for_model(Offer)
        permission, _ = Permission.objects.get_or_create(
            codename='block_product',
            content_type=content_type,
            defaults={'name': 'Can block product'}
        )

        return permission

    def test_common_user_cant_block_product_by_offer(self):
        offer = self.product.offers.first()

        url = reverse('block-product-by-offer', args=[offer.pk])

        permission = self.__create_block_product_permission()

        # Simulate a common user
        self.user.user_permissions.remove(permission)

        result = self.client.patch(
            url,
            headers=self.build_user_auth_headers(self.user)
        )

        self.assertEqual(result.status_code, status.HTTP_403_FORBIDDEN, result.content.decode())

    def test_block_product_by_offer(self):
        offer = self.product.offers.first()

        url = reverse('block-product-by-offer', args=[offer.pk])

        permission = self.__create_block_product_permission()

        # Simulate a common user
        self.user.user_permissions.add(permission)

        result = self.client.patch(
            url,
            headers=self.build_user_auth_headers(self.user)
        )

        self.assertEqual(result.status_code, status.HTTP_200_OK, result.content.decode())

        self.product.refresh_from_db()
        self.assertEqual(self.product.status, 'blocked')

    def __file_exists_in_storage(self, file_path):
        from django.core.files.storage import default_storage
        return default_storage.exists(file_path)

    def test_offer_image_update_success(self):
        offer = self.product.offers.first()
        url = reverse('offer', kwargs={'pk': offer.pk})

        image_file = self.create_test_image()

        response = self.client.put(
            url,
            data={'image': image_file},
            format='multipart',
            headers=self.headers,
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK, response.content.decode())
        self.assertIn('image', response.json())
        self.assertTrue(response.json()['image'].endswith('.png'))

        offer.refresh_from_db()
        self.assertIsNotNone(offer.image)
        self.assertTrue(offer.image.name.endswith('.png'))

    def test_offer_image_update_replaces_existing_image(self):
        offer = self.product.offers.first()
        url = reverse('offer', kwargs={'pk': offer.pk})

        # First upload
        first_image = self.create_test_image('first.png')
        self.client.put(
            url,
            data={'image': first_image},
            format='multipart',
            headers=self.headers
        )

        self.assertTrue(self.__file_exists_in_storage('offers/' + first_image.name))

        offer.refresh_from_db()
        first_image_name = offer.image.name

        # Second upload should replace the first
        second_image = self.create_test_image('second.png')
        response = self.client.put(
            url,
            data={'image': second_image},
            format='multipart',
            headers=self.headers
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK, response.content.decode())

        self.assertFalse(self.__file_exists_in_storage('offers/' + first_image.name))
        offer.refresh_from_db()
        self.assertIsNotNone(offer.image)
        self.assertNotEqual(offer.image.name, first_image_name)

    def test_offer_image_update_image_blank_deletes_image(self):
        offer = self.product.offers.first()
        url = reverse('offer', kwargs={'pk': offer.pk})

        # First upload an image
        image_file = self.create_test_image()
        self.client.put(
            url,
            data={'image': image_file},
            format='multipart',
            headers=self.headers
        )

        offer.refresh_from_db()
        original_image_name = offer.image.name  # Get the file name for storage verification

        # Verify the file exists in storage
        self.assertTrue(
            self.__file_exists_in_storage(original_image_name),
            f"Image file should exist in storage: {original_image_name}"
        )

        # PUT without image should delete the existing one
        response = self.client.put(
            url,
            data={'image': ''},
            format='multipart',
            headers=self.headers
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK, response.content.decode())
        self.assertIn('image', response.json())
        self.assertIsNone(response.json()['image'])

        offer.refresh_from_db()
        self.assertFalse(offer.image)

        # Assert that the actual file is deleted from storage
        self.assertFalse(
            self.__file_exists_in_storage(original_image_name),
            f"Image file should be deleted from storage: {original_image_name}"
        )
