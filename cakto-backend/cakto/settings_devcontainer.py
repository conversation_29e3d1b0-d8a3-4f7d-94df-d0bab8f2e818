import os
from datetime import <PERSON><PERSON><PERSON>
from pathlib import Path

from firebase_admin import initialize_app

DJANGO_SETTINGS_MODULE = os.getenv('DJANGO_SETTINGS_MODULE')

DAYS_TO_NEXT_NPS_POLL = 7

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent


# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/4.1/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = os.getenv('SECRET_KEY')

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = os.getenv('DEBUG', False)

ALLOWED_HOSTS = os.getenv('ALLOWED_HOSTS', '').split(',')

CORS_ORIGIN_ALLOW_ALL = False
CORS_ALLOW_CREDENTIALS = True

CORS_ORIGIN_WHITELIST = os.getenv('CORS_ORIGIN_WHITELIST', '').split(',')

SESSION_COOKIE_SAMESITE = None
SESSION_COOKIE_SECURE = True
SESSION_COOKIE_DOMAIN = os.getenv('SESSION_COOKIE_DOMAIN', 'cakto.com.br')

CSRF_TRUSTED_ORIGINS = os.getenv('CSRF_TRUSTED_ORIGINS', '').split(',')

# List of regex endpoint, will disable CORS policy
CORS_ALLOWED_ENDPOINTS = [
    r'^\/api\/login\/otp\/\?fingerprint=.*$',
    r'^\/api\/payment\/upsell\/.*\/.*\/$',
    r'^\/api\/payment\/status\/.*\/$',
    r'^\/api\/product\/upsell\/.*\/?fingerprint=.*$',
]

# Application definition
INSTALLED_APPS = [
    'cakto.apps.CaktoAppConfig',
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'allauth',
    'allauth.account',
    'allauth.socialaccount',
    'allauth.socialaccount.providers.facebook',
    'allauth.socialaccount.providers.google',
    'rest_framework_simplejwt',
    'rest_framework_simplejwt.token_blacklist',
    'rest_framework.authtoken',
    'rest_framework',
    'django_filters',
    'django_rq',
    'django_apscheduler',
    'dj_rest_auth',
    'notifications',
    'fcm_django',
    'corsheaders',
    'financial',
    'gateway',
    'checkout',
    'product',
    'customer',
    'user',
    'members',
    'apps',
    'dashboard',
    'email_service',
    'django_prometheus',
    'debug_toolbar',  # Useful for development, not in production
    'django_extensions',  # Useful for development, not in production
    'system_log',
    'waffle',
    'baas',
    'phonenumber_field',  # Phone number lib -> "django-phonenumber-field[phonenumbers]"

    # Oauth
    'oauth2_provider',

    # Multi Factor Authentication
    'django_otp',
    'django_otp.plugins.otp_totp',
    'django_otp.plugins.otp_email',  # for email capability.
]

OAUTH2_PROVIDER = {
    "OIDC_ENABLED": True,
    "OAUTH2_VALIDATOR_CLASS": "sso.validators.UserOAuth2Validator",
    "OIDC_RSA_PRIVATE_KEY": os.getenv('OIDC_RSA_PRIVATE_KEY'),
    "SCOPES" :{
        'openid': 'Identificação básica (login com conta)',
        'user': 'Ver seu e-mail e informações básicas',
        'offers': 'Ver ofertas disponíveis para você',
        'products': 'Ver produtos e assinaturas associadas à sua conta'
    }
}
LOGIN_REDIRECT_URL = '/oauth/authorize/'

# DJ REST AUTH
SITE_ID = 1
REST_USE_JWT = True

REST_FRAMEWORK = {
    'DEFAULT_AUTHENTICATION_CLASSES': (
        'rest_framework.authentication.SessionAuthentication',
        'rest_framework_simplejwt.authentication.JWTAuthentication',
        'oauth2_provider.contrib.rest_framework.OAuth2Authentication',
        'rest_framework.authentication.TokenAuthentication',
    ),
    'DEFAULT_FILTER_BACKENDS': ['django_filters.rest_framework.DjangoFilterBackend'],
    'DEFAULT_PERMISSION_CLASSES': (
        'rest_framework.permissions.IsAuthenticated',
        'user.permissions.UserIsValidated',  # Checks if user is email and whatsapp validated
        'user.permissions.ScopePermission',  # Checks if user has scope permission
    ),
    'DEFAULT_PAGINATION_CLASS': 'cakto.pagination.CustomPagination',
    'DEFAULT_THROTTLE_CLASSES': [
        'cakto.throttling.CaktoAnonRateThrottle',
        'cakto.throttling.CaktoUserRateThrottle',
        'cakto.throttling.CaktoScopedRateThrottle',
    ],
    'DEFAULT_THROTTLE_RATES': {
        'anon': '200/hour',
        'user': '5000/hour',
        'whatsappToken': '1/minute',
        'emailToken': '1/minute',
        'MFASendToken': '1/minute',
        'requestLoginLinkEmail': '1/minute',
        'threeDsToken': '10/minute',
    },
}

OAUTH2_PROVIDER = {
    "OIDC_ENABLED": True,
    "SCOPES": {
        'email': 'Leitura ao email',
    },
}

MIDDLEWARE = [
    'django_prometheus.middleware.PrometheusBeforeMiddleware',  # should be the first middleware
    'django.middleware.security.SecurityMiddleware',
    'whitenoise.middleware.WhiteNoiseMiddleware',
    'cakto.middleware.DisableSessionMiddleware',
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django_otp.middleware.OTPMiddleware',  # should be after AuthenticationMiddleware
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    'allauth.account.middleware.AccountMiddleware',
    'waffle.middleware.WaffleMiddleware',
    'django_prometheus.middleware.PrometheusAfterMiddleware',  # should be the last middleware
    'debug_toolbar.middleware.DebugToolbarMiddleware',
    'cakto.health.HealthCheckMiddleware'
]

INTERNAL_IPS = [
    "127.0.0.1",
]

ROOT_URLCONF = 'cakto.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': ['views'],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'cakto.wsgi.application'
ASGI_APPLICATION = 'cakto.asgi.application'

# Database
# https://docs.djangoproject.com/en/4.1/ref/settings/#databases

DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': os.getenv('DB_NAME'),
        'USER': os.getenv('DB_USER'),
        'PASSWORD': os.getenv('DB_PASSWORD'),
        'HOST': os.getenv('DB_HOST'),
        'PORT': os.getenv('DB_PORT'),
        'CONN_MAX_AGE': 0,
        'DISABLE_SERVER_SIDE_CURSORS': True,
        'OPTIONS': {
            'sslmode': os.getenv('DB_SSL_MODE'),
        },
    }
}

# Caches
CACHE_PREFIX = 'cakto'
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.redis.RedisCache',
        'LOCATION': os.getenv('REDIS_URL'),
        'KEY_PREFIX': CACHE_PREFIX,
    }
}

# Password validation
# https://docs.djangoproject.com/en/4.1/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
        'OPTIONS': {
            'min_length': 8,
        },
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'cakto.validators.CaptialLetterValidator',
    },
    {
        'NAME': 'cakto.validators.LowerCaseValidator',
    },
    {
        'NAME': 'cakto.validators.MinimunNumberValidator',
    },
    {
        'NAME': 'cakto.validators.SymbolValidator',
    },
]

# Auth
AUTH_USER_MODEL = 'user.User'

# Internationalization
# https://docs.djangoproject.com/en/4.1/topics/i18n/

LANGUAGE_CODE = 'pt-br'

TIME_ZONE = 'America/Sao_Paulo'

USE_I18N = True

USE_TZ = True


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/4.1/howto/static-files/

STATIC_URL = 'assets/'
STATIC_ROOT = BASE_DIR / 'assets'

MEDIA_ROOT = BASE_DIR / 'images/'
MEDIA_URL = 'images/'

# Default primary key field type
# https://docs.djangoproject.com/en/4.1/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# JWT
SIMPLE_JWT = {
    'ACCESS_TOKEN_LIFETIME': timedelta(hours=1),
    'REFRESH_TOKEN_LIFETIME': timedelta(days=7),
    'ROTATE_REFRESH_TOKENS': True,
    'BLACKLIST_AFTER_ROTATION': True,
}

# S3
AWS_ACCESS_KEY_ID = os.getenv('AWS_ACCESS_KEY_ID')
AWS_SECRET_ACCESS_KEY = os.getenv('AWS_SECRET_ACCESS_KEY')
AWS_STORAGE_BUCKET_NAME = os.getenv('AWS_STORAGE_BUCKET_NAME')
AWS_S3_SIGNATURE_VERSION = 's3v4'
AWS_S3_REGION_NAME = os.getenv('AWS_S3_REGION_NAME')
AWS_S3_VERIFY = True
AWS_DEFAULT_ACL = 'public-read'
AWS_S3_FILE_OVERWRITE = False
AWS_S3_ENDPOINT_URL = os.getenv('AWS_S3_ENDPOINT_URL')
AWS_S3_CUSTOM_DOMAIN = os.getenv('AWS_S3_CUSTOM_DOMAIN')

STORAGES = {
    'default': {  # Use this for localstack or s3
        'BACKEND': 'cakto.storages.PublicStorage',
    },
    # 'default': {  # Use this for local development without localstack, it will create files in your machine
    #     'BACKEND': 'django.core.files.storage.FileSystemStorage',
    # },
    'public': {
        'BACKEND': 'cakto.storages.PublicStorage',
    },
    'private': {
        'BACKEND': 'cakto.storages.PrivateStorage',
    },
    'staticfiles': {  # Whitenoise
        'BACKEND': 'whitenoise.storage.CompressedManifestStaticFilesStorage',
    }
}

RQ_QUEUES = {
    'default': {
        'URL': os.getenv('REDIS_URL_QUEUE', ''),
        'DEFAULT_TIMEOUT': 10,
    },
    'pixel_events': {
        'URL': os.getenv('REDIS_URL_QUEUE', ''),
        'DEFAULT_TIMEOUT': 5,
        'PREFIX': 'pixel_events_',
    },
    'cakto_pixel': {
        'URL': os.getenv('REDIS_URL_QUEUE', ''),
        'DEFAULT_TIMEOUT': 5,
        'PREFIX': 'pixel_events_',
    },
    'metrics': {
        'URL': os.getenv('REDIS_URL_QUEUE', ''),
        'DEFAULT_TIMEOUT': 10,
        'PREFIX': 'metrics_',
    },
}

# FCM
initialize_app()

FCM_DJANGO_SETTINGS = {
    'DEFAULT_FIREBASE_APP': None,
    'APP_VERBOSE_NAME': 'Cakto',
    'ONE_DEVICE_PER_USER': False,
    'DELETE_INACTIVE_DEVICES': False,
}

# Mail
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = os.environ.get('EMAIL_HOST', '')
EMAIL_PORT = os.environ.get('EMAIL_PORT', '')
EMAIL_USE_TLS = os.environ.get('EMAIL_USE_TLS', '') == 'true'
EMAIL_HOST_USER = os.environ.get('EMAIL_HOST_USER', '')
EMAIL_HOST_PASSWORD = os.environ.get('EMAIL_HOST_PASSWORD', '')
DEFAULT_FROM_MAIL = os.environ.get('DEFAULT_FROM_MAIL', 'Cakto <<EMAIL>>')

# Just for django admin send email tokens for MFA
OTP_EMAIL_SENDER = os.getenv('OTP_EMAIL_SENDER', '<EMAIL>')
OTP_TOTP_ISSUER = os.getenv('OTP_TOTP_ISSUER', 'Cakto').title()
OTP_EMAIL_BODY_HTML_TEMPLATE_PATH = 'mail/otp_token.html'

# MFA THROTTLE
OTP_EMAIL_THROTTLE_FACTOR = 0.6
OTP_TOTP_THROTTLE_FACTOR = 0.6

LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
        },
    },
    'loggers': {
        'django': {
            'handlers': ['console'],
            'level': 'ERROR',
            'propagate': False,
        },
    },
}

REDIRECT_DOMAIN = os.getenv('REDIRECT_DOMAIN', 'cakto.app')
BACKEND_BASE_URL = os.getenv('BACKEND_BASE_URL', 'https://api.cakto.com.br/')
FRONT_END_BASE_URL = os.getenv('FRONT_END_BASE_URL', 'https://app.cakto.com.br')
CHECKOUT_BASE_URL = os.getenv('CHECKOUT_BASE_URL', 'https://pay.cakto.com.br')

WHATSAPP_VALIDATION_ENABLED = os.getenv('WHATSAPP_VALIDATION_ENABLED', 'false').lower() == 'true'
ENABLE_DJANGO_ADMIN_MFA = os.getenv('ENABLE_DJANGO_ADMIN_MFA', 'true').lower() == 'true'
ENABLE_FAKE_REDIS_CONN = os.getenv('ENABLE_FAKE_REDIS_CONN', 'false').lower() == 'true'

INFOBIP_BASE_URL = os.getenv('INFOBIP_BASE_URL')
INFOBIP_API_TOKEN = os.getenv('INFOBIP_API_TOKEN')
INFOBIP_WHATSAPP_NUMBER = os.getenv('INFOBIP_WHATSAPP_NUMBER')
INFOBIP_AUTH_TEMPLATE_NAME = os.getenv('INFOBIP_AUTH_TEMPLATE_NAME')

CRM_WEBHOOK_URL = os.getenv('CRM_WEBHOOK_URL')
CRM_WEBHOOK_TOKEN = os.getenv('CRM_WEBHOOK_TOKEN')

# Card anti-fraud analysis
ANALISYS_REFUSED_PERCENTAGE = 0.65
ANALISYS_ORDERS_COUNT = 0
ANALISYS_MINIMAL_TICKET = 200
ANALISYS_MINIMAL_RATIO_CARD_PIX = 15
BLOCK_CARD_REFUSED_PERCENTAGE = 0.8
BLOCK_CARD_ORDERS_COUNT = 25

GATEWAY_URL = os.getenv('GATEWAY_URL', 'https://api.scpay.com.br/')

MEMBERS_V2_BASE_URL = os.getenv('MEMBERS_V2_BASE_URL')
MEMBERS_V2_API_TOKEN = os.getenv('MEMBERS_V2_API_TOKEN')
MEMBERS_V2_MAX_IMAGE_SIZE = os.getenv('MEMBERS_V2_MAX_IMAGE_SIZE', 10)  # in MB
MEMBERS_V2_MAX_FILE_SIZE = os.getenv('MEMBERS_V2_MAX_FILE_SIZE', 1024)  # in MB
MEMBERS_V2_TOKEN_CACHE_TIMEOUT = os.getenv('MEMBERS_V2_TOKEN_CACHE_TIMEOUT', 1380)  # in minutes

AI_COMPLIANCE_URL = os.getenv('AI_COMPLIANCE_URL')
AI_COMPLIANCE_API_KEY = os.getenv('AI_COMPLIANCE_API_KEY')

# Card anti-fraud analysis
ANALISYS_REFUSED_PERCENTAGE = 0.65
ANALISYS_ORDERS_COUNT = 0
ANALISYS_MINIMAL_TICKET = 200
ANALISYS_MINIMAL_RATIO_CARD_PIX = 15
BLOCK_CARD_REFUSED_PERCENTAGE = 0.8
BLOCK_CARD_ORDERS_COUNT = 25

GATEWAY_URL = os.getenv('GATEWAY_URL', 'https://api.scpay.com.br/')
# MailChimp Settings
MAILCHIMP_API_KEY = os.getenv('MAILCHIMP_API_KEY')
MAILCHIMP_SERVER_PREFIX = os.getenv('MAILCHIMP_SERVER_PREFIX')
MAILCHIMP_AUDIENCE_ID = os.getenv('MAILCHIMP_AUDIENCE_ID')
MAX_MFA_THROTTLE_SECONDS = os.getenv('MAX_MFA_THROTTLE_SECONDS', 15 * 60)

# TheMembers Settings
THEMEMBERS_DEV_TOKEN = os.getenv('THEMEMBERS_DEV_TOKEN')

ADDRESS_REQUIRED_FOR_PAYMENT = os.getenv('ADDRESS_REQUIRED_FOR_PAYMENT', 'false').lower() == 'true'

BRAND = os.getenv('BRAND', 'cakto').lower()

TELEGRAM_BOT_TOKEN = os.getenv('TELEGRAM_BOT_TOKEN', '')
# Cakto Members V2 Integration
CAKTO_MEMBERS_V2_BASE_URL = os.getenv('CAKTO_MEMBERS_V2_BASE_URL', 'https://aluno.cakto.com.br')
CAKTO_MEMBERS_V2_API_KEY = os.getenv('CAKTO_MEMBERS_V2_API_KEY')
