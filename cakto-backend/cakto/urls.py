from django.conf import settings
from django.conf.urls.static import static
from django.contrib import admin
from django.urls import include, path, re_path
from oauth2_provider import urls as oauth2_urls
from rest_framework.routers import DefaultRouter

from cakto.admin import MFAAdminSite
from cakto.views import prometheus_metrics
from dashboard.views import ShortLinkRedirectAPIView
from product.views import LinkRedirectAPIView

router = DefaultRouter()

if settings.ENABLE_DJANGO_ADMIN_MFA:
    admin.site.__class__ = MFAAdminSite

urlpatterns = [
    re_path(r'^(?P<shortId>.{7})\/$', LinkRedirectAPIView.as_view(), name='link-redirect'),
    re_path(r'^r\/(?P<shortId>.{1,40})\/$', ShortLinkRedirectAPIView.as_view(), name='short-link-redirect'),
    path('admin/', admin.site.urls),
    path('accounts/', include('sso.urls')),
    path('api/', include('user.urls')),
    path('api/', include('product.urls')),
    path('api/', include('notifications.urls')),
    path('api/', include('gateway.urls')),
    path('api/', include('reports.urls')),
    path('api/', include('checkout.urls')),
    path('api/', include('members.urls')),
    path('api/', include('financial.urls')),
    path('api/', include('apps.urls')),
    path('api/', include('dashboard.urls')),
    path('api/', include('system_log.urls')),
    path('api/', include('customer.urls')),
    path('api/', include(router.urls)),
    path('django-rq/', include('django_rq.urls')),
    path("metrics", prometheus_metrics, name="prometheus-django-metrics"),
    path('oauth/', include(oauth2_urls)),
    path('api/baas/', include('baas.urls')),
]

if settings.DEBUG:
    urlpatterns += path("__debug__/", include("debug_toolbar.urls")),
    # Serve media files during development
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
