from django.conf import settings
from django.core.files.storage import storages
from storages.backends.s3boto3 import S3Boto3Storage


class PublicStorage(S3Boto3Storage):
    querystring_auth = False
    default_acl = 'public-read'

class PrivateStorage(S3Boto3Storage):
    querystring_auth = True
    default_acl = 'private'


def create_storage(storage_key: str):
    """Utility function to create a storage instance based on STORAGES setting.

    Use in the model as follows if you want to use other storage than the `default` one:
    ```python

    from cakto.storages import create_storage
    class MyModel(models.Model):
        my_file = models.FileField(storage=create_storage('private'))
    ```

    This will use the `private` storage defined in `settings.STORAGES`.
    """
    return storages.create_storage(settings.STORAGES[storage_key])
