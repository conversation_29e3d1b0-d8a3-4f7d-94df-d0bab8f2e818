import decimal
import io
import json
import logging
from io import BytesIO
from typing import Iterable
from unittest import mock

import fakeredis
from django.core.cache import cache
from django.core.files.uploadedfile import SimpleUploadedFile
from django.test import TestCase
from django.utils import timezone
from PIL import Image
from requests import Response
from rest_framework.test import APIClient
from rest_framework_simplejwt.tokens import RefreshToken

from apps.models import App
from customer.models import Customer
from gateway.models import Order, Payment
from gateway.sdk.splitpay import SplitPay
from gateway.strategies.base import PaymentStrategy
from product.models import Category, Offer, PaymentMethod, Product
from user.models import User


class FakeRedisConn:
    """Singleton FakeRedis connection."""

    def __init__(self):
        self.conn = None

    def __call__(self, _, strict):
        if not self.conn:
            self.conn = fakeredis.FakeStrictRedis() if strict else fakeredis.FakeRedis()
        return self.conn


class BaseTestCase(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.user = cls.create_user()

    def setUp(self):
        self.client = APIClient()

        # Ignore django request logs (bad request, forbidden, etc)
        self.logger = logging.getLogger('django.request')
        self.previous_logging_level = self.logger.getEffectiveLevel()
        self.logger.setLevel(logging.CRITICAL)
        return super().setUp()

    def tearDown(self):
        mock.patch.stopall()
        cache.clear()
        return super().tearDown()

    @classmethod
    def create_paymentMethods(cls):
        cls.credit_card, _ = PaymentMethod.objects.get_or_create(type='credit_card', defaults={'name': 'Cartão de Crédito'})
        cls.boleto, _ = PaymentMethod.objects.get_or_create(type='boleto', defaults={'name': 'Boleto'})
        cls.pix, _ = PaymentMethod.objects.get_or_create(type='pix', defaults={'name': 'Pix'})
        cls.picpay, _ = PaymentMethod.objects.get_or_create(type='picpay', defaults={'name': 'PicPay'})
        cls.googlepay, _ = PaymentMethod.objects.get_or_create(type='googlepay', defaults={'name': 'Google Pay'})
        cls.applepay, _ = PaymentMethod.objects.get_or_create(type='applepay', defaults={'name': 'Apple Pay'})
        cls.openfinance_nubank, _ = PaymentMethod.objects.get_or_create(type='openfinance_nubank', defaults={'name': 'Nubank'})
        cls.threeDs, _ = PaymentMethod.objects.get_or_create(type='threeDs', defaults={'name': '3DS'})

    @classmethod
    def get_response_mock(cls, *args, content: dict = {}, status: int = 200, **kwargs):
        mock_response = mock.Mock(spec=Response)
        mock_response.status_code = status
        mock_response._content = json.dumps(content).encode('utf-8')
        mock_response.json = mock.Mock(return_value=json.loads(mock_response._content))
        return mock_response

    @classmethod
    def get_user_access_token(cls, user: User) -> str:
        return RefreshToken.for_user(user).access_token

    @classmethod
    def create_headers(cls, token):
        return {'Authorization': f'Bearer {token}'}

    @classmethod
    def build_user_auth_headers(cls, user: User | None = None) -> dict:
        user = user or cls.user
        return cls.create_headers(cls.get_user_access_token(user))

    @classmethod
    def create_user(
        cls,
        email: str | None = None,
        password: str = 'teste1234',
        username: str = '',
        emailValidated: bool = True,
        whatsappValidated: bool = True,
        **kwargs
    ) -> User:
        from user.tests.tests_user import mock_createAccount

        if not email:
            email = f'teste_{User.objects.count() + 1}@teste.com'

        if not username:
            username = email

        with mock.patch.object(SplitPay, 'createAccount', new_callable=mock_createAccount):
            user = User.objects.create(
                email=email.lower(),
                username=username,
                emailValidated=emailValidated,
                whatsappValidated=whatsappValidated,
                **kwargs
            )

        user.set_password(password)
        return user

    @classmethod
    def create_customer(
        cls,
        name='Test Customer',
        email='<EMAIL>',
        phone='*********',
        docNumber='***********',
        docType='cpf',
        **kwargs
    ) -> Customer:
        birthDate = kwargs.pop('birthDate', None)
        if birthDate:
            kwargs['birthDate'] = timezone.datetime.strptime(birthDate, '%Y-%m-%d').date()
        customer = Customer.objects.create(
            name=name,
            email=email,
            phone=phone,
            docNumber=docNumber,
            docType=docType,
            **kwargs
        )
        return customer

    @classmethod
    def create_order(cls, customer=None, product=None, offer=None, create_splits=True, **kwargs) -> Order:
        if not customer:
            customer = cls.create_customer()
        if not product:
            product = cls.create_product()
        if not offer:
            offer = product.offers.first()

        order_data = {
            'customer': customer,
            'product': product,
            'offer': offer,
            'client_user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'client_ip': '127.0.0.1',
            'checkoutUrl': 'https://checkout.test.com',
            'refererUrl': 'https://referer.test.com',
            'baseAmount': decimal.Decimal('100.00'),
            'amount': decimal.Decimal('100.00'),
            'reason': 'Test Order',
            'offer_type': 'main',
            'type': 'unique',
            'paymentMethod': 'credit_card',
            'installments': 1,
            'saveCard': False,
            'acquirerType': 'test_acquirer',
            'status': 'waiting_payment',
        }
        order_data.update(kwargs)

        paymentMethod = order_data['paymentMethod']
        if not isinstance(paymentMethod, PaymentMethod):
            paymentMethod = PaymentMethod.objects.get(type=paymentMethod)
        del order_data['paymentMethod']

        if 'commissions' not in kwargs:
            order_data['commissions'] = [{
                'commissionValue': str(order_data['amount']),
                'type': 'producer',
                'commissionPercentage': 100,
                'userId': product.user.id,
            }]

        commissioned_users = []
        if 'commissionedUsers' in kwargs:
            commissioned_users = order_data.pop('commissionedUsers')
        else:
            commissioned_users = [comm.get('userId') for comm in order_data['commissions']]

        order = Order.objects.create(**order_data, paymentMethod=paymentMethod)

        if commissioned_users:
            order.commissionedUsers.set(commissioned_users)

        if create_splits:
            PaymentStrategy.create_order_splits(order)
        return order

    @classmethod
    def create_payment(
        cls,
        orders: Iterable[Order] | None = None,
        product: Product | None = None,
        **kwargs
    ) -> Payment:

        if not product:
            product = cls.create_product()

        if not orders:
            orders = [cls.create_order(product=product)]

        amount = sum(order.amount or 0 for order in orders)

        paymentMethod = orders[0].paymentMethod  # type: ignore

        payment = Payment.objects.create(amount=amount, **kwargs, paymentMethod=paymentMethod)
        payment.orders.set(orders)

        return payment

    @classmethod
    def create_app(cls, product=None, fields=None, platform=None, user=None, token: str = "1234", url: str = 'test/test.com', **kwargs) -> App:
        if not product:
            product = cls.create_product()

        app_data = {
            'user': user or product.user,
            'platform': platform,
            'token': token,
            'name': 'test',
            'url': url,
            'fields': fields,
        }
        app = App.objects.create(**app_data)
        app.products.set([product])
        return app

    @classmethod
    def create_product(cls, name: str = 'Test Product', price: float = 10, user=None, category=None, **kwargs) -> Product:
        if not user:
            user = cls.create_user()
        if not category:
            category = Category.objects.create(name="test")
        product = Product.objects.create(name=name, price=price, user=user, category=category, **kwargs)
        Offer.objects.create(product=product, price=price, default=True)
        return product

    @classmethod
    def create_response(cls, status=200, json_data=None):
        mock_response = mock.Mock(spec=Response)
        mock_response.status_code = status
        mock_response.json.return_value = json_data
        mock_response.text = str(json_data)
        return mock_response

    @classmethod
    def create_test_image(cls, name: str = 'test.png', content_type: str = 'image/png', content: bytes | None = None) -> SimpleUploadedFile:
        """
        Use override_settings on STORAGES to use InMemoryStorage so the image will not be saved.

        Example usage in a test case:
        ```python

        from django.test import override_settings
        @override_settings(STORAGES={
            'default': {
                'BACKEND': 'django.core.files.storage.InMemoryStorage',
            },
        })
        class MyTestCaseClass(BaseTestCase):
            def test_create_image(self):
                ...  # Your test logic here
        ```
        """
        image_data = BytesIO()
        image = Image.new('RGB', (100, 100), 'white')  # type:ignore
        image.save(image_data, format='png')
        image_data.seek(0)

        return SimpleUploadedFile(name, content or image_data.read(), content_type=content_type)

    @classmethod
    def create_large_test_image(cls, target_size_kb, width=5000, height=5000, color=(255, 0, 0)):
        """
        Use override_settings on STORAGES to use InMemoryStorage so the image will not be saved.

        Example usage in a test case:
        ```python

        from django.test import override_settings
        @override_settings(STORAGES={
            'default': {
                'BACKEND': 'django.core.files.storage.InMemoryStorage',
            },
        })
        class MyTestCaseClass(BaseTestCase):
            def test_create_image(self):
                ...  # Your test logic here
        ```
        """
        image = Image.new('RGB', (width, height), color)
        image_file = io.BytesIO()

        # Start with a high quality and reduce until the target size is met
        quality = 95
        while True:
            image_file.seek(0)
            image.save(image_file, 'JPEG', quality=quality)
            size_kb = image_file.tell() / 1024
            if size_kb >= target_size_kb or quality <= 10:
                break
            quality -= 5

        image_file.seek(0)
        return image_file
