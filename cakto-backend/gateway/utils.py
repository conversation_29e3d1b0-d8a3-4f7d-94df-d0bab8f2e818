import base64
import os
import tempfile
from collections import defaultdict
from decimal import Decimal
from io import BytesIO
from typing import Union, Optional

import requests
from django.conf import settings
from django.db.models import Count, QuerySet, Sum
from django.utils import timezone
from google.cloud import recaptchaenterprise_v1
from google.cloud.recaptchaenterprise_v1 import Assessment
from qrcode.constants import ERROR_CORRECT_L
from qrcode.main import QRCode
from rest_framework import status
from rest_framework.exceptions import ValidationError
from rest_framework.response import Response

from customer.models import CustomerHistory
from gateway.models import Order
from gateway.sdk.splitpay import SplitPay
from product.models import Offer, PaymentMethod, Product
from user.models import User

STATUS_MAPPING = {
    'pending': 'waiting_payment',
    'declined': 'refused',
    'blocked': 'refused',
}

def get_split_installments_fees(account_id) -> dict[str, Decimal]:
    split = SplitPay()
    response = split.getInstallmentsFees(account_id)
    if not status.is_success(response.status_code):
        raise Exception(response.text)
    return {k: Decimal(v) for k, v in response.json().items()}

def generate_qr_code(data):
    qr = QRCode(
        version=1,
        error_correction=ERROR_CORRECT_L,
        box_size=10,
        border=4,
    )
    qr.add_data(data)
    qr.make(fit=True)

    img = qr.make_image(fill_color="black", back_color="white")
    buffered = BytesIO()
    img.save(buffered, format="PNG")  # type:ignore

    img_str = base64.b64encode(buffered.getvalue()).decode()
    return f"data:image/png;base64,{img_str}"

def get_client_ip(request):
    """Get the client IP address from the request."""
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        # proxies can send list of IPs
        client_ip = x_forwarded_for.split(',')[0]
    else:
        # if no proxy, this is the user's IP address
        client_ip = request.META.get('REMOTE_ADDR')

    # try to get the digitalocean ip or client ip
    return request.META.get('HTTP_DO_CONNECTING_IP') or client_ip

def create_captcha_assessment(token: str, recaptcha_action: str) -> Assessment:
    """Crie uma avaliação para analisar o risco de uma ação da interface.
    Args:
        project_id: O ID do seu projeto do Google Cloud.
        recaptcha_key: A chave reCAPTCHA associada ao site/app
        token: O token gerado obtido do cliente.
        recaptcha_action: Nome da ação correspondente ao token.
    """
    project_id = os.getenv("RECAPTCHA_PROJECT_ID", '')
    recaptcha_key = os.getenv("RECAPTCHA_SITE_KEY", '')

    client = recaptchaenterprise_v1.RecaptchaEnterpriseServiceClient()

    # Defina as propriedades do evento que será monitorado.
    event = recaptchaenterprise_v1.Event()
    event.site_key = recaptcha_key
    event.token = token

    assessment = recaptchaenterprise_v1.Assessment()
    assessment.event = event

    project_name = f"projects/{project_id}"

    # Crie a solicitação de avaliação.
    request = recaptchaenterprise_v1.CreateAssessmentRequest()
    request.assessment = assessment
    request.parent = project_name

    response = client.create_assessment(request)

    # Verifique se o token é válido.
    if not response.token_properties.valid:
        raise Exception(
            "The CreateAssessment call failed because the token was "
            + "invalid for the following reasons: "
            + str(response.token_properties.invalid_reason)
        )

    # Verifique se a ação esperada foi executada.
    if response.token_properties.action != recaptcha_action:
        raise Exception(
            "The action attribute in your reCAPTCHA tag does"
            + "not match the action you are expecting to score"
        )
    else:
        # Consulte a pontuação de risco e os motivos.
        # Para mais informações sobre como interpretar a avaliação, acesse:
        # https://cloud.google.com/recaptcha-enterprise/docs/interpret-assessment
        for reason in response.risk_analysis.reasons:
            print(f'Anti-fraud recaptcha [{recaptcha_action}] | reason:' + str(reason))
        # Consiga o nome da avaliação (ID). Use isso para anotar a avaliação.
        # assessment_name = client.parse_assessment_path(response.name).get("assessment")
        # print(f"Anti-fraud recaptcha [{recaptcha_action}] | Assessment name: {assessment_name}")
    return response

def validate_recaptcha(token: str, action: str, userEmail: str, product: Product | None = None):
    if settings.DEBUG:
        return

    product_info = f' | product: {product.short_id}, {product.name[:30]:<30}' if product else ''
    customer_info = f' | customerEmail: {userEmail}'
    log_info = product_info + customer_info

    try:
        if not token:
            raise Exception('Token not found')

        recaptcha_analysis = create_captcha_assessment(token, action)
        score = round(recaptcha_analysis.risk_analysis.score, 2)
        log_info = f' | score: {score}' + log_info

        if score < float(os.getenv('RECAPTCHA_THRESHOLD', 0.3)):
            raise Exception('Risk score is too low')
    except Exception as e:
        print(f'Anti-fraud recaptcha [{action}] | {e}{log_info}')
        raise ValidationError({'detail': 'Recaptcha validation failed.'})

def cloudflarecheck(request, token: str, action: str, userEmail: str, product: Product | None = None):
    # https://developers.cloudflare.com/turnstile/

    if settings.DEBUG:
        return

    def get_log_info(product, userEmail):
        product_info = f' | product: {product.short_id}, {product.name[:30]:<30}' if product else ''
        customer_info = f' | customerEmail: {userEmail}'
        return product_info + customer_info

    def verify_token(token, request):
        if not token:
            return 'Token not found'

        secret_key = os.getenv('TURNSTILE_SECRET_KEY')
        if not secret_key:
            return 'TURNSTILE_SECRET_KEY not set in environment variables'

        verify_url = 'https://challenges.cloudflare.com/turnstile/v0/siteverify'
        data = {'secret': secret_key, 'response': token, 'remoteip': get_client_ip(request)}

        response = requests.post(verify_url, data=data)
        response_data = response.json()
        if not response_data.get('success'):
            return str(response_data.get('error-codes') or response_data)

    log_info = get_log_info(product, userEmail)
    error = verify_token(token, request)

    if error:
        print(f'Captcha Turnstile [{action}] | {error}{log_info} | token: {token[:20]}')
        raise ValidationError({'detail': 'Recaptcha validation failed.'})

def get_order_by_offer_and_fingerprint(offer: Offer | None, fingerprint: str) -> Order | None:
    if not offer:
        return None

    customer_history = CustomerHistory.objects.filter(fingerprint=fingerprint).order_by('-createdAt').first()
    if not customer_history:
        return None

    time_limit = timezone.now() - timezone.timedelta(minutes=60)
    return (Order.objects
            .filter(
                customer=customer_history.customer,
                createdAt__gte=time_limit,
                product__user=offer.product.user,
                status='paid',
                offer_type='main',
            )
            .order_by('-createdAt')
            .first())

def card_anti_fraud(product: Product, data: dict):
    if data.get('paymentMethod') != 'credit_card' or product.statusAdmin == 'block_analisis':
        return

    now = timezone.now()
    minus_30_days = now - timezone.timedelta(days=30)
    search_time = max((product.analisedAt or minus_30_days), (product.unblockedAt or minus_30_days), minus_30_days)
    orders = Order.objects.filter(product=product, paymentMethod__type='credit_card', createdAt__gte=search_time)
    orders_count = orders.count()
    refused_orders = orders.filter(status='refused').count()

    if not orders_count:
        return

    refused_percentage = refused_orders / orders_count

    orders_pix = Order.objects.filter(product=product, paymentMethod__type='pix', createdAt__gte=search_time)
    ratio_card_pix = round((orders_count / orders_pix.count()) * 100, 2) if orders_pix.count() else 0

    offer_ids = [item.get('id') for item in data.get('items', [])]
    total_ticket = Offer.objects.filter(id__in=offer_ids).aggregate(total=Sum('price')).get('total') or 0

    # print(
    #     f'Anti-fraud check:'
    #     f' | {product.name[:30]:<30}'
    #     f' | {product.short_id}'
    #     f' | {round(refused_percentage * 100, 2):<6}% Refused'
    #     f' | Vendas {(search_time - now).days:<3}d: {orders_count:<4}'
    #     f' | Recusadas: {refused_orders:<4}'
    #     f' | card/pix ratio: {ratio_card_pix:.2f}'
    #     f' | Total Ticket: {total_ticket:.2f}'
    # )

    if (refused_percentage > settings.ANALISYS_REFUSED_PERCENTAGE
        and orders_count >= settings.ANALISYS_ORDERS_COUNT
        and total_ticket >= settings.ANALISYS_MINIMAL_TICKET
            and ratio_card_pix >= settings.ANALISYS_MINIMAL_RATIO_CARD_PIX):
        product.statusAdmin = 'block_analisis'

        if (refused_percentage > settings.BLOCK_CARD_REFUSED_PERCENTAGE
            and orders_count >= settings.BLOCK_CARD_ORDERS_COUNT
                and not product.analisedAt):
            product.additionalInfo['paymentMethodsBeforeBlock'] = list(product.paymentMethods.values_list('type', flat=True))
            product.additionalInfo['cardBlocked'] = True
            product.additionalInfo['cardBlockedAt'] = timezone.now().isoformat()
            product.paymentMethods.set([PaymentMethod.get_payment_method_instance('pix')])

        product.save()

def get_orders_by_paymentMethod_and_status(orders: QuerySet[Order]) -> dict[str, dict[str, dict[str, Union[float, Decimal]]]]:
    orders_data = orders.values('paymentMethod', 'status').annotate(commissions=Sum('userCommissions'), count=Count('id'))
    data: dict = defaultdict(lambda: defaultdict(lambda: {'commissions': 0, 'count': 0}))
    for item in orders_data:
        paymentMethod = item['paymentMethod']
        status = item['status']

        data[paymentMethod][status]['commissions'] = item['commissions'] or 0
        data[paymentMethod][status]['count'] = item['count'] or 0

    return data  # type:ignore

def user_has_access_to_order_customer_data(user: User, order: Order) -> bool:
    affiliateContact = order.product.affiliateContact
    user_is_coproducer = order.splits.filter(user=user, type='coproducer')
    if affiliateContact or order.product.user == user or user_is_coproducer.exists():
        return True
    return False

def validate_applepay_merchant(validation_url: str):
    merchant_identifier = os.getenv("APPLEPAY_MERCHANT_IDENTIFIER")
    domain_name = os.getenv("APPLEPAY_DOMAIN_NAME")
    display_name = os.getenv("APPLEPAY_DISPLAY_NAME")
    cert = os.getenv("APPLEPAY_CERT", "").replace("\\n", "\n")
    key = os.getenv("APPLEPAY_KEY", "").replace("\\n", "\n")

    if not cert or not key:
        raise ValueError("Certificado ou chave não configurados!")

    cert_path = None
    key_path = None

    try:
        with tempfile.NamedTemporaryFile(delete=False) as cert_file, tempfile.NamedTemporaryFile(delete=False) as key_file:
            cert_file.write(cert.encode())
            key_file.write(key.encode())
            cert_path = cert_file.name
            key_path = key_file.name

        response = requests.post(
            validation_url,
            json={
                "merchantIdentifier": merchant_identifier,
                "domainName": domain_name,
                "displayName": display_name
            },
            cert=(cert_path, key_path)
        )

        response.raise_for_status()
        return response.json()
    finally:
        if cert_path and os.path.exists(cert_path):
            os.remove(cert_path)
        if key_path and os.path.exists(key_path):
            os.remove(key_path)

def handle_split_response(resp: requests.Response) -> Optional[Response]:
    if status.is_success(resp.status_code):
        return None

    try:
        error_detail = resp.json().get('detail', '')
    except Exception:
        error_detail = ''

    if not error_detail:
        print(f"Erro inesperado do Split: {resp.status_code} - {resp.text}")
        error_detail = 'Erro interno no servidor. Tente novamente mais tarde.'

    return Response({'detail': error_detail}, status=resp.status_code)
