from django.db import models
from django.db.models import Q
from django_filters import rest_framework as filters

from gateway.models import Order, PaymentStatus, Subscription
from product.filters import CharFieldInFilter


class OrderFilter(filters.FilterSet):
    product = filters.CharFilter(method='filter_product')
    customer = filters.CharFilter(method='filter_customer')
    affiliate = filters.CharFilter(method='filter_affiliate')
    products = CharFieldInFilter(field_name='product')
    checkout = CharFieldInFilter()
    status = CharFieldInFilter()
    refId = CharFieldInFilter()
    offer_type = CharFieldInFilter()
    offerType = CharFieldInFilter(field_name='offer_type')
    type = CharFieldInFilter()
    paymentMethod = CharFieldInFilter()
    paymentMethods = CharFieldInFilter(field_name='paymentMethod')
    offer = CharFieldInFilter()
    utm_source = filters.CharFilter(lookup_expr='icontains')
    utm_medium = filters.CharFilter(lookup_expr='icontains')
    utm_campaign = filters.CharFilter(lookup_expr='icontains')
    utm_term = filters.CharFilter(lookup_expr='icontains')
    utm_content = filters.CharFilter(lookup_expr='icontains')
    sck = filters.CharFilter(lookup_expr='icontains')
    couponCode = filters.CharFilter(lookup_expr='icontains')
    subscription_type = filters.CharFilter(method='filter_subscription_type')
    coproductionType = filters.CharFilter(method='filter_coproductionType')

    class Meta:
        model = Order
        fields = {
            'id': ['exact'],
            'checkoutUrl': ['icontains'],
            'refererUrl': ['icontains'],
            'baseAmount': ['exact', 'gt', 'lt', 'lte', 'gte'],
            'discount': ['exact', 'gt', 'lt', 'lte', 'gte'],
            'amount': ['exact', 'gt', 'lt', 'lte', 'gte'],
            'reason': ['icontains'],
            'installments': ['exact'],
            'createdAt': ['exact', 'gt', 'lt', 'lte', 'gte'],
            'updatedAt': ['exact', 'gt', 'lt', 'lte', 'gte'],
            'refundedAt': ['exact', 'gt', 'lt', 'lte', 'gte'],
            'chargedbackAt': ['exact', 'gt', 'lt', 'lte', 'gte'],
            'due_date': ['exact', 'gt', 'lt', 'lte', 'gte'],
            'canceledAt': ['exact', 'gt', 'lt', 'lte', 'gte'],
            'paidAt': ['exact', 'gt', 'lt', 'lte', 'gte'],
        }

    def filter_queryset(self, queryset):
        """
        Customized method from django_filters/filterset.py::BaseFilterSet::filter_queryset to:

        1. Save the queryset before status filter.

        2. Exclude SCHEDULED status if status filter isn't provided.
        """

        cleaned_data_items = self.form.cleaned_data.items()

        # Remove status and replace it in the end of the list
        status = self.form.cleaned_data.pop('status')
        cleaned_data_items = list(self.form.cleaned_data.items())
        cleaned_data_items.append(('status', status))

        for name, value in cleaned_data_items:
            is_status_filter = name == 'status'

            # save queryset before status filter
            if is_status_filter:
                queryset_before_status_filter = queryset

            # If the value is empty, we want to filter by all statuses except SCHEDULED
            if is_status_filter and not value:
                queryset = queryset.exclude(status=PaymentStatus.SCHEDULED.id)

            queryset = self.filters[name].filter(queryset, value)
            assert isinstance(
                queryset, models.QuerySet
            ), "Expected '%s.%s' to return a QuerySet, but got a %s instead." % (
                type(self).__name__,
                name,
                type(queryset).__name__,
            )

            # set the queryset before status filter to the queryset
            if is_status_filter:
                setattr(queryset, 'queryset_before_status_filter', queryset_before_status_filter)  # type: ignore

        return queryset

    def filter_product(self, queryset, name, value):
        return queryset.filter(
            Q(product=value)
            | Q(product__short_id=value)
            | Q(product__name__icontains=value)
        )

    def filter_customer(self, queryset, name, value):
        if value.isdigit():
            # If the value is numeric, assume it's a customer ID
            return queryset.filter(
                Q(customer=value)
                | Q(customer__docNumber=value)
            )
        else:
            return queryset.filter(
                Q(customer__email__icontains=value)
                | Q(customer__name__icontains=value)
            )

    def filter_affiliate(self, queryset, name, value):
        value = value.strip().split(',')
        if all(item.isdigit() for item in value):
            # If values is all numeric, assume they are customer ID's
            return queryset.filter(
                Q(affiliate__in=value)
            )
        else:
            # Otherwise, assume they are short IDs or emails
            return queryset.filter(
                Q(affiliate__short_id__in=value)
                | Q(affiliate__user__email__in=value)
            )

    def filter_subscription_type(self, queryset, name, value):
        if value == 'new':
            return queryset.filter(subscription_period=1)
        elif value == 'renewed':
            return queryset.filter(subscription_period__gt=1)
        return queryset

    TYPE_MAP = {
        'production':   'producer',
        'coproduction': 'coproducer',
        'affiliate':    'affiliate',
    }

    def filter_coproductionType(self, qs, name, value):
        split_type = self.TYPE_MAP.get(value)
        user = getattr(self.request, 'user', None)

        if split_type and user and user.is_authenticated:
            return qs.filter(
                splits__user=user,
                splits__type=split_type,
            ).distinct()

        return qs


class SubscriptionFilter(filters.FilterSet):
    id = CharFieldInFilter()
    status = CharFieldInFilter()
    paymentMethod = CharFieldInFilter()
    current_situation = filters.CharFilter(method='filter_current_situation')

    class Meta:
        model = Subscription
        fields = {
            'recurrence_period': ['exact', 'gt', 'lt', 'lte', 'gte'],
            'current_period': ['exact', 'gt', 'lt', 'lte', 'gte'],
            'paid_payments_quantity': ['exact', 'gt', 'lt', 'lte', 'gte'],
            'quantity_recurrences': ['exact', 'gt', 'lt', 'lte', 'gte'],
            'trial_days': ['exact', 'gt', 'lt', 'lte', 'gte'],
            'max_retries': ['exact', 'gt', 'lt', 'lte', 'gte'],
            'amount': ['exact', 'gt', 'lt', 'lte', 'gte'],
            'retry_interval': ['exact', 'gt', 'lt', 'lte', 'gte'],
            'next_payment_date': ['exact', 'gt', 'lt', 'lte', 'gte'],
            'createdAt': ['exact', 'gt', 'lt', 'lte', 'gte'],
            'updatedAt': ['exact', 'gt', 'lt', 'lte', 'gte'],
            'canceledAt': ['exact', 'gt', 'lt', 'lte', 'gte'],
        }

    def filter_current_situation(self, queryset, name, value):
        if value == 'new':
            return queryset.filter(paid_payments_quantity=1)
        elif value == 'renewed':
            return queryset.filter(paid_payments_quantity__gt=1)
        return queryset
