from django_rq import job

from user.models import User
from gateway.services.email_marketing_factory import EmailMarketingStrategyFactory
from gateway.strategies.base_email_marketing import BaseEmailMarketingStrategy


def send_user_to_email_marketing(user: User, provider: str = 'MailChimp') -> None:
    strategy = EmailMarketingStrategyFactory.get_strategy(provider)
    if strategy.has_prerequisites():
        send_user_to_email_marketing_task.delay(strategy, user=user)

@job('default')
def send_user_to_email_marketing_task(user: User, strategy: BaseEmailMarketingStrategy) -> None:
    strategy.handle_contact_send(user)
