import os
from unittest import mock

from django.urls import reverse

from cakto.tests.base import BaseTestCase
from financial.enums import CompanyStatus


class TestCompanyWebhook(BaseTestCase):
    def setUp(self):
        setup_result = super().setUp()
        self.user._company.companyName = 'Test company'
        self.user._company.companyCnpj = '**************'
        self.user._company.cpf = '**********'
        self.user._company.completeName = 'Test Company'
        self.user._company.phone = '**********'
        self.user._company.save()

        self.url = reverse('webhook-splitpay')
        return setup_result

    def test_should_call_handle_company_if_webhook_type_is_account_type(self):
        url = reverse('webhook-splitpay')
        all_account_types = ['account.changed', 'account.approved', 'account.rejected']
        expected_data = {
            'status': CompanyStatus.PENDING.id,
            'email': self.user.email,
            'id': self.user.id,
        }

        for account_type in all_account_types:
            with self.subTest(account_type=account_type):
                payload = {
                    'secret': os.getenv('WEBHOOK_SECRETS', '').split(',')[0],
                    'type': account_type,
                    'data': expected_data,
                }

                with mock.patch('gateway.views.WebhookAPIView.handle_company') as handle_company_mock:  # noqa: E501
                    handle_company_mock.return_value = True
                    response = self.client.post(url, data=payload, format='json')

                    self.assertEqual(response.status_code, 200, response.json())
                    handle_company_mock.assert_called_once_with(expected_data)

    def test_should_update_all_company_fields_when_webhook_has_all_fields(self):

        expected_data = {
            'status': CompanyStatus.PENDING.id,
            'email': self.user.email,
            'id': self.user.id,
            'commercialName': 'this is commercial name',
            'cnpj': '************',
            'cpf': '1234567',
            'first_name': 'first name',
            'last_name': 'last name',
            'cellphone': '**********'
        }

        payload = {
            'secret': os.getenv('WEBHOOK_SECRETS', '').split(',')[0],
            'type': 'account.changed',
            'data': expected_data,
        }

        response = self.client.post(self.url, data=payload, format='json')

        self.user.refresh_from_db()
        company = self.user._company
        company.refresh_from_db()
        self.assertEqual(response.status_code, 200, response.json())
        self.assertEqual(company.companyName, expected_data['commercialName'])
        self.assertEqual(company.companyCnpj, expected_data['cnpj'])
        self.assertEqual(company.cpf, expected_data['cpf'])
        self.assertEqual(
            company.completeName,
            f"{expected_data['first_name']} {expected_data['last_name']}".strip()
        )
        self.assertEqual(company.phone, expected_data['cellphone'])

    def test_should_clear_company_cpf_field_when_webhook_cpf_field_is_empty(self):
        expected_data = {
            'status': CompanyStatus.PENDING.id,
            'email': self.user.email,
            'id': self.user.id,
            'commercialName': 'this is commercial name',
            'cnpj': '************',
            'cpf': '',
            'first_name': 'first name',
            'last_name': 'last name',
            'cellphone': '**********'
        }

        payload = {
            'secret': os.getenv('WEBHOOK_SECRETS', '').split(',')[0],
            'type': 'account.changed',
            'data': expected_data,
        }

        response = self.client.post(self.url, data=payload, format='json')

        self.user.refresh_from_db()
        company = self.user._company
        company.refresh_from_db()
        self.assertEqual(response.status_code, 200, response.json())
        self.assertEqual(company.cpf, '')

    def test_should_not_clear_company_cpf_field_when_webhook_cpf_field_is_missing(self):
        expected_data = {
            'status': CompanyStatus.PENDING.id,
            'email': self.user.email,
            'id': self.user.id,
            'commercialName': 'this is commercial name',
            'cnpj': '************',
            'first_name': 'first name',
            'last_name': 'last name',
            'cellphone': '**********'
        }

        company = self.user._company
        original_cpf = company.cpf

        payload = {
            'secret': os.getenv('WEBHOOK_SECRETS', '').split(',')[0],
            'type': 'account.changed',
            'data': expected_data,
        }

        response = self.client.post(self.url, data=payload, format='json')

        self.user.refresh_from_db()
        company.refresh_from_db()
        self.assertEqual(response.status_code, 200, response.json())
        self.assertEqual(company.cpf, original_cpf)

    def test_should_clear_company_cnpj_field_when_webhook_cnpj_field_is_empty(self):
        expected_data = {
            'status': CompanyStatus.PENDING.id,
            'email': self.user.email,
            'id': self.user.id,
            'commercialName': 'this is commercial name',
            'cnpj': '',
            'cpf': '***********',
            'first_name': 'first name',
            'last_name': 'last name',
            'cellphone': '**********'
        }

        payload = {
            'secret': os.getenv('WEBHOOK_SECRETS', '').split(',')[0],
            'type': 'account.changed',
            'data': expected_data,
        }

        response = self.client.post(self.url, data=payload, format='json')

        self.user.refresh_from_db()
        company = self.user._company
        company.refresh_from_db()
        self.assertEqual(response.status_code, 200, response.json())
        self.assertEqual(company.companyCnpj, '')

    def test_should_not_clear_company_cnpj_field_when_webhook_cnpj_field_is_missing(self):
        expected_data = {
            'status': CompanyStatus.PENDING.id,
            'email': self.user.email,
            'id': self.user.id,
            'commercialName': 'this is commercial name',
            'cpf': '***********',
            'first_name': 'first name',
            'last_name': 'last name',
            'cellphone': '**********'
        }

        payload = {
            'secret': os.getenv('WEBHOOK_SECRETS', '').split(',')[0],
            'type': 'account.changed',
            'data': expected_data,
        }

        company = self.user._company
        original_cnpj = company.companyCnpj
        response = self.client.post(self.url, data=payload, format='json')

        self.user.refresh_from_db()
        company.refresh_from_db()
        self.assertEqual(response.status_code, 200, response.json())
        self.assertEqual(company.companyCnpj, original_cnpj)

    def test_should_clear_company_companyName_field_when_webhook_commercialName_field_is_empty(self):  # noqa: E501 N802
        expected_data = {
            'status': CompanyStatus.PENDING.id,
            'email': self.user.email,
            'id': self.user.id,
            'commercialName': '',
            'cnpj': '************',
            'cpf': '***********',
            'first_name': 'first name',
            'last_name': 'last name',
            'cellphone': '**********'
        }

        payload = {
            'secret': os.getenv('WEBHOOK_SECRETS', '').split(',')[0],
            'type': 'account.changed',
            'data': expected_data,
        }

        response = self.client.post(self.url, data=payload, format='json')

        self.user.refresh_from_db()
        company = self.user._company
        company.refresh_from_db()
        self.assertEqual(response.status_code, 200, response.json())
        self.assertEqual(company.companyName, '')

    def test_should_not_clear_company_companyName_field_when_webhook_commercialName_field_is_missing(self):  # noqa: E501 N802
        expected_data = {
            'status': CompanyStatus.PENDING.id,
            'email': self.user.email,
            'id': self.user.id,
            'cnpj': '************',
            'cpf': '***********',
            'first_name': 'first name',
            'last_name': 'last name',
            'cellphone': '**********'
        }

        payload = {
            'secret': os.getenv('WEBHOOK_SECRETS', '').split(',')[0],
            'type': 'account.changed',
            'data': expected_data,
        }

        company = self.user._company
        original_name = company.companyName
        response = self.client.post(self.url, data=payload, format='json')

        self.user.refresh_from_db()
        company.refresh_from_db()
        self.assertEqual(response.status_code, 200, response.json())
        self.assertEqual(company.companyName, original_name)

    def test_should_clear_company_completeName_field_when_webhook_first_name_and_last_name_fields_are_empty(self):  # noqa: E501 N802
        expected_data = {
            'status': CompanyStatus.PENDING.id,
            'email': self.user.email,
            'id': self.user.id,
            'commercialName': 'test company',
            'cnpj': '************',
            'cpf': '***********',
            'first_name': '',
            'last_name': '',
            'cellphone': '**********'
        }

        payload = {
            'secret': os.getenv('WEBHOOK_SECRETS', '').split(',')[0],
            'type': 'account.changed',
            'data': expected_data,
        }

        response = self.client.post(self.url, data=payload, format='json')

        self.user.refresh_from_db()
        company = self.user._company
        company.refresh_from_db()
        self.assertEqual(response.status_code, 200, response.json())
        self.assertEqual(company.completeName, '')

    def test_should_not_clear_company_completeName_field_when_webhook_first_name_and_last_name_fields_are_missing(self):  # noqa: E501 N802
        expected_data = {
            'status': CompanyStatus.PENDING.id,
            'email': self.user.email,
            'id': self.user.id,
            'commercialName': 'test company',
            'cnpj': '************',
            'cpf': '***********',
            'cellphone': '**********'
        }

        payload = {
            'secret': os.getenv('WEBHOOK_SECRETS', '').split(',')[0],
            'type': 'account.changed',
            'data': expected_data,
        }
        company = self.user._company

        original_name = company.completeName

        response = self.client.post(self.url, data=payload, format='json')

        self.user.refresh_from_db()
        company.refresh_from_db()
        self.assertEqual(response.status_code, 200, response.json())
        self.assertEqual(company.completeName, original_name)

    def test_should_clear_company_phone_field_when_webhook_cellphone_field_is_empty(self):
        expected_data = {
            'status': CompanyStatus.PENDING.id,
            'email': self.user.email,
            'id': self.user.id,
            'commercialName': 'test company',
            'cnpj': '************',
            'cpf': '***********',
            'first_name': 'first name',
            'last_name': ' last name',
            'cellphone': ''
        }

        payload = {
            'secret': os.getenv('WEBHOOK_SECRETS', '').split(',')[0],
            'type': 'account.changed',
            'data': expected_data,
        }

        response = self.client.post(self.url, data=payload, format='json')

        self.user.refresh_from_db()
        company = self.user._company
        company.refresh_from_db()
        self.assertEqual(response.status_code, 200, response.json())
        self.assertEqual(company.phone, '')

    def test_should_not_clear_company_phone_field_when_webhook_cellphone_field_is_missing(self):
        expected_data = {
            'status': CompanyStatus.PENDING.id,
            'email': self.user.email,
            'id': self.user.id,
            'commercialName': 'test company',
            'cnpj': '************',
            'cpf': '***********',
            'first_name': 'first name',
            'last_name': ' last name',
        }

        payload = {
            'secret': os.getenv('WEBHOOK_SECRETS', '').split(',')[0],
            'type': 'account.changed',
            'data': expected_data,
        }
        company = self.user._company

        original_phone = company.phone

        response = self.client.post(self.url, data=payload, format='json')

        self.user.refresh_from_db()
        company.refresh_from_db()
        self.assertEqual(response.status_code, 200, response.json())
        self.assertEqual(company.phone, original_phone)

    def test_should_clear_company_session_data_when_webhook_status_is_rejected(self):
        expected_data = {
            'status': CompanyStatus.REJECTED.id,
            'email': self.user.email,
            'id': self.user.id,
            'commercialName': '',
            'cnpj': '************',
            'cpf': '***********',
            'first_name': 'first name',
            'last_name': 'last name',
            'cellphone': '**********',
            'rejectedReasons': ['reason 1', 'reason 2']
        }

        payload = {
            'secret': os.getenv('WEBHOOK_SECRETS', '').split(',')[0],
            'type': 'account.changed',
            'data': expected_data,
        }

        response = self.client.post(self.url, data=payload, format='json')

        self.user.refresh_from_db()
        company = self.user._company
        company.refresh_from_db()
        self.assertEqual(response.status_code, 200, response.json())
        self.assertEqual(company.status, expected_data['status'])
        self.assertEqual(company.rejectedReasons, expected_data['rejectedReasons'])
        self.assertIsNone(company.sessionId)
        self.assertIsNone(company.sessionUrl)
        self.assertIsNone(company.sessionToken)
        self.assertIsNone(company.sessionTokenExpires)

    def test_should_update_company_rejected_reason_field_when_webhook_status_is_resubmission_requested(self):  # noqa: E501
        expected_data = {
            'status': CompanyStatus.RESUBMISSION_REQUESTED.id,
            'email': self.user.email,
            'id': self.user.id,
            'commercialName': '',
            'cnpj': '************',
            'cpf': '***********',
            'first_name': 'first name',
            'last_name': 'last name',
            'cellphone': '**********',
            'rejectedReasons': []
        }

        payload = {
            'secret': os.getenv('WEBHOOK_SECRETS', '').split(',')[0],
            'type': 'account.changed',
            'data': expected_data,
        }

        response = self.client.post(self.url, data=payload, format='json')

        self.user.refresh_from_db()
        company = self.user._company
        company.refresh_from_db()
        self.assertEqual(response.status_code, 200, response.json())
        self.assertEqual(company.status, expected_data['status'])
        self.assertEqual(company.rejectedReasons, expected_data['rejectedReasons'])

    def test_should_not_clean_company_data_when_webhook_status_is_rejected(self):  # noqa: E501
        expected_data = {
            'status': CompanyStatus.REJECTED.id,
            'email': self.user.email,
            'id': self.user.id
        }

        payload = {
            'secret': os.getenv('WEBHOOK_SECRETS', '').split(',')[0],
            'type': 'account.changed',
            'data': expected_data,
        }

        company = self.user._company
        original_company_name = company.companyName
        original_cnpj = company.companyCnpj
        original_cpf = company.cpf
        original_complete_name = company.completeName
        original_cellphone = company.phone

        response = self.client.post(self.url, data=payload, format='json')

        self.user.refresh_from_db()
        company.refresh_from_db()
        self.assertEqual(response.status_code, 200, response.json())
        self.assertEqual(company.status, expected_data['status'])
        self.assertEqual(company.companyName, original_company_name)
        self.assertEqual(company.companyCnpj, original_cnpj)
        self.assertEqual(company.cpf, original_cpf)
        self.assertEqual(company.completeName, original_complete_name)
        self.assertEqual(company.phone, original_cellphone)

    @mock.patch("gateway.views.cache.delete_many")
    def test_should_enable_three_ds_without_status(self, mock_delete_many):
        self.assertFalse(self.user._company.threeDsEnabled)

        payload = {
            "secret": os.getenv("WEBHOOK_SECRETS", "").split(",")[0],
            "type": "user.three_ds_changed",
            "data": {
                "id": self.user.id,
                "email": self.user.email,
                "threeDs": True,
            },
        }

        response = self.client.post(self.url, data=payload, format="json")

        self.user.refresh_from_db()
        self.user._company.refresh_from_db()
        self.assertEqual(response.status_code, 200, response.json())
        self.assertTrue(self.user._company.threeDsEnabled)
        mock_delete_many.assert_called()

    @mock.patch("gateway.views.cache.delete_many")
    def test_should_disable_three_ds_without_status(self, mock_delete_many):
        self.user._company.threeDsEnabled = True
        self.user._company.save(update_fields=["threeDsEnabled"])

        payload = {
            "secret": os.getenv("WEBHOOK_SECRETS", "").split(",")[0],
            "type": "user.three_ds_changed",
            "data": {
                "id": self.user.id,
                "email": self.user.email,
                "threeDs": False,
            },
        }

        response = self.client.post(self.url, data=payload, format="json")

        self.user.refresh_from_db()
        self.user._company.refresh_from_db()
        self.assertEqual(response.status_code, 200, response.json())
        self.assertFalse(self.user._company.threeDsEnabled)
        mock_delete_many.assert_called()
