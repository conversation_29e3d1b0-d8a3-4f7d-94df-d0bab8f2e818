from unittest import mock
from requests import HTT<PERSON><PERSON>rror
from django.test import override_settings

from gateway.services.email_marketing_service import send_user_to_email_marketing, send_user_to_email_marketing_task
from gateway.strategies.mailchimp_strategy import MailchimpStrategy
from cakto.tests.base import BaseTestCase


@override_settings(
    MAILCHIMP_SERVER_PREFIX='test-prefix',
    MAILCHIMP_AUDIENCE_ID='12356',
    MAILCHIMP_API_KEY='someapikey',
)
class EmailMarketingTestCase(BaseTestCase):
    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()
        cls.user.emailValidated = True
        cls.user.first_name = "Francisco"
        cls.user.last_name = "Silva"
        cls.user.save()

    @mock.patch("gateway.strategies.mailchimp_strategy.requests.post")
    @mock.patch("gateway.strategies.mailchimp_strategy.requests.put")
    def test_send_user_to_mailchimp_success(self, mock_put, mock_post):
        mock_put.return_value = self.get_response_mock(status=200)
        mock_post.return_value = self.get_response_mock(status=200)

        with mock.patch.object(self.user, 'totalSales', return_value=12000):
            send_user_to_email_marketing_task(strategy=MailchimpStrategy(), user=self.user)

        mock_put.assert_called()
        mock_post.assert_called()

    @mock.patch("gateway.strategies.mailchimp_strategy.requests.post")
    @mock.patch("gateway.strategies.mailchimp_strategy.requests.put")
    def test_send_user_to_mailchimp_error(self, mock_put, mock_post):
        mock_put.side_effect = HTTPError("Error on put")
        mock_post.side_effect = HTTPError("Error on post")

        with self.assertRaises(HTTPError):
            send_user_to_email_marketing_task(strategy=MailchimpStrategy(), user=self.user)

    def test_get_sales_tag(self):
        self.assertEqual(MailchimpStrategy.get_sales_tag(500), "Abaixo de 10k")
        self.assertEqual(MailchimpStrategy.get_sales_tag(10000), "Acima de 10k")
        self.assertEqual(MailchimpStrategy.get_sales_tag(50000), "Acima de 50k")
        self.assertEqual(MailchimpStrategy.get_sales_tag(100000), "Acima de 100k")
        self.assertEqual(MailchimpStrategy.get_sales_tag(250000), "Acima de 250k")
        self.assertEqual(MailchimpStrategy.get_sales_tag(500000), "Acima de 500k")
        self.assertEqual(MailchimpStrategy.get_sales_tag(1000000), "Acima de 1M")

    @mock.patch("gateway.services.email_marketing_service.EmailMarketingStrategyFactory.get_strategy")
    @mock.patch("gateway.services.email_marketing_service.send_user_to_email_marketing_task.delay")
    def test_send_user_to_email_marketing_with_prerequisites(self, mock_job_delay, mock_get_strategy):
        # Arrange
        mock_strategy = mock.Mock()
        mock_strategy.has_prerequisites.return_value = True
        mock_get_strategy.return_value = mock_strategy

        # Act
        send_user_to_email_marketing(self.user)

        # Assert
        mock_get_strategy.assert_called_once_with('MailChimp')
        mock_strategy.has_prerequisites.assert_called_once()
        mock_job_delay.assert_called_once_with(mock_strategy, user=self.user)

    @mock.patch("gateway.services.email_marketing_service.EmailMarketingStrategyFactory.get_strategy")
    @mock.patch("gateway.services.email_marketing_service.send_user_to_email_marketing_task.delay")
    def test_send_user_to_email_marketing_without_prerequisites(self, mock_job_delay, mock_get_strategy):
        # Arrange
        mock_strategy = mock.Mock()
        mock_strategy.has_prerequisites.return_value = False
        mock_get_strategy.return_value = mock_strategy

        # Act
        send_user_to_email_marketing(self.user)

        # Assert
        mock_get_strategy.assert_called_once_with('MailChimp')
        mock_strategy.has_prerequisites.assert_called_once()
        mock_job_delay.assert_not_called()

    @mock.patch("gateway.services.email_marketing_service.EmailMarketingStrategyFactory.get_strategy")
    @mock.patch("gateway.services.email_marketing_service.send_user_to_email_marketing_task.delay")
    def test_send_user_to_email_marketing_custom_strategy(self, mock_job_delay, mock_get_strategy):
        # Arrange
        mock_strategy = mock.Mock()
        mock_strategy.has_prerequisites.return_value = True
        mock_get_strategy.return_value = mock_strategy
        custom_strategy_provider = 'CustomStrategy'

        # Act
        send_user_to_email_marketing(self.user, provider=custom_strategy_provider)

        # Assert
        mock_get_strategy.assert_called_once_with(custom_strategy_provider)
        mock_strategy.has_prerequisites.assert_called_once()
        mock_job_delay.assert_called_once_with(mock_strategy, user=self.user)

    def test_send_user_to_email_marketing_task_calls_handle_contact_send(self):
        strategy_mock = mock.Mock()

        send_user_to_email_marketing_task(self.user, strategy=strategy_mock)

        strategy_mock.handle_contact_send.assert_called_once_with(self.user)
