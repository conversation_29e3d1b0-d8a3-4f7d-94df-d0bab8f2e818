from cakto.tests.base import BaseTestCase
from customer.models import Address
from gateway.models import Split
from gateway.utils import user_has_access_to_order_customer_data
from django.urls import reverse


class GatewayUtilsTest(BaseTestCase):
    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()
        cls.create_paymentMethods()

        cls.customer = cls.create_customer(
            email=cls.user.email,
            birthDate='1990-01-01',
            phone='1234567890',
            docType='cpf',
            docNumber='12345678901',
        )

        cls.address = Address.objects.create(
            customer=cls.customer,
            street='123 Main St',
            neighborhood='Downtown',
            city='Metropolis',
            state='NY',
            zipcode='12345',
            country='US',
            number='100',
            complement='Apt 1B',
        )

        cls.product = cls.create_product(user=cls.user)

        cls.order = cls.create_order(cls.customer, product=cls.product, amount=10)
        cls.order.address = cls.address
        cls.order.save()

    def test_user_has_access_to_order_customer_data_returns_true_for_producer(self):
        self.assertTrue(user_has_access_to_order_customer_data(
            user=self.user,
            order=self.order
        ))

    def test_user_has_access_to_order_customer_data_returns_false_for_non_producer(self):
        non_producer_user = self.create_user()
        self.assertFalse(user_has_access_to_order_customer_data(
            user=non_producer_user,
            order=self.order
        ))

    def test_user_has_access_to_order_customer_data_returns_true_for_affiliate_and_contact_flag_true(self):
        affiliate_user = self.create_user()
        self.order.product.affiliateContact = True
        self.order.product.save()

        self.assertTrue(user_has_access_to_order_customer_data(
            user=affiliate_user,
            order=self.order
        ))

    def test_user_has_access_to_order_customer_data_returns_false_for_affiliate_and_contact_flag_false(self):
        affiliate_user = self.create_user()
        self.order.product.affiliateContact = False
        self.order.product.save()

        self.assertFalse(user_has_access_to_order_customer_data(
            user=affiliate_user,
            order=self.order
        ))

    def test_user_has_access_to_order_customer_data_returns_true_for_coproducer(self):
        coproducer_user = self.create_user()
        Split.objects.create(
            order=self.order,
            user=coproducer_user,
            type='coproducer',
        )

        self.assertTrue(user_has_access_to_order_customer_data(
            user=coproducer_user,
            order=self.order
        ))

    def test_list_order_does_not_return_customer_data_for_affiliate_when_contact_flag_false(self):
        affiliate_user = self.create_user()
        self.order.product.affiliateContact = False
        self.order.product.save()
        self.order.commissionedUsers.add(affiliate_user)

        url = reverse('order-list')
        response = self.client.get(url, headers=self.build_user_auth_headers(affiliate_user))

        result = response.json()['results'][0]

        self.assertEqual(response.status_code, 200, response.content.decode())
        self.assertNotIn('email', result['customer'])
        self.assertNotIn('phone', result['customer'])
        self.assertNotIn('birthDate', result['customer'])
        self.assertNotIn('docType', result['customer'])
        self.assertNotIn('docNumber', result['customer'])

        # Address also should not be present
        self.assertIsNone(result.get('address'))

    def test_list_order_returns_customer_data_for_affiliate_when_contact_flag_true(self):
        affiliate_user = self.create_user()
        self.order.product.affiliateContact = True
        self.order.product.save()
        self.order.commissionedUsers.add(affiliate_user)

        url = reverse('order-list')
        response = self.client.get(url, headers=self.build_user_auth_headers(affiliate_user))

        result = response.json()['results'][0]
        res_customer = result['customer']
        res_address = result['address']

        self.assertEqual(response.status_code, 200, response.content.decode())
        self.assertEqual(res_customer['email'], self.customer.email)
        self.assertEqual(res_customer['name'], self.customer.name)
        self.assertEqual(res_customer['phone'], self.customer.phone)
        self.assertEqual(res_customer['birthDate'], self.customer.birthDate.isoformat())  # type:ignore
        self.assertEqual(res_customer['docType'], self.customer.docType)
        self.assertEqual(res_customer['docNumber'], self.customer.docNumber)

        # Address should be present
        self.assertIsNotNone(res_address)
        self.assertEqual(res_address['street'], self.address.street)
        self.assertEqual(res_address['number'], self.address.number)
        self.assertEqual(res_address['neighborhood'], self.address.neighborhood)
        self.assertEqual(res_address['city'], self.address.city)
        self.assertEqual(res_address['state'], self.address.state)
        self.assertEqual(res_address['zipcode'], self.address.zipcode)
        self.assertEqual(res_address['country'], self.address.country)
        self.assertEqual(res_address['complement'], self.address.complement)
        self.assertNotIn('id', res_address)
        self.assertNotIn('is_default', res_address)
