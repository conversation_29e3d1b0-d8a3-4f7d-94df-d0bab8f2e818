from django.core.management.base import BaseCommand
from django.db import transaction

from user.models import User
from gateway.services.email_marketing_service import send_user_to_email_marketing


class Command(BaseCommand):
    help = 'Performs the migration of existing sellers to Mailchimp'

    def handle(self, *args, **options):
        users = User.objects.filter(product__order__status='paid').distinct().iterator()

        total = 0
        success = 0
        errors = 0

        for user in users:

            if user.totalSales() <= 0:
                continue

            total += 1
            try:
                with transaction.atomic():
                    send_user_to_email_marketing(str(user.id))


                success += 1
                self.stdout.write(self.style.SUCCESS(f'Successfully sent to Mailchimp: {user.email}'))

            except Exception as e:
                errors += 1
                self.stdout.write(self.style.ERROR(f'Failed to send {user.email}: {str(e)}'))

        self.stdout.write(self.style.WARNING(f'Total users processed: {total}'))
        self.stdout.write(self.style.SUCCESS(f'Successful: {success}'))
        self.stdout.write(self.style.ERROR(f'Failed: {errors}'))
