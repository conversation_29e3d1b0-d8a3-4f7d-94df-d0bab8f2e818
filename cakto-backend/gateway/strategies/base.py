from decimal import ROUND_HALF_UP, Decimal
from typing import Any, Iterable, Mapping, MutableMapping, Sequence, Union
from urllib.parse import parse_qs, urlparse

from django.db.models import Sum
from django.utils import timezone
from rest_framework import status
from rest_framework.exceptions import ValidationError

from apps.services.event_manager import dispatch_pixel_events
from cakto.utils import normalize_datetime_input
from customer.utils import CustomerPaymentProcessor
from financial.utils import (handle_approval, handle_chargeback, handle_med, handle_refund, handle_refused,
                             handle_subcription_renewal_payment, handle_subscription_canceled,
                             sync_company_status_with_split)
from gateway.enums import SubscriptionStatus
from gateway.models import Order, Payment, PaymentStatus, Split, Subscription
from gateway.sdk.splitpay import SplitPay
from notifications.models import NotificationType
from notifications.utils import is_notification_enabled
from product.enums import ProductType
from product.models import Affiliate, Coproduction, Offer, PaymentMethod, Product
from product.pixel_strategies.facebook import send_purchase_event_to_fb
from user.models import User
from user.notification import send_notification


class SubscriptionCreationError(Exception):
    pass

class PaymentStrategy():
    split = SplitPay()
    orders: Sequence[Order] = []
    card: dict | None = None
    googlepay: dict | None = None
    applepay: dict | None = None
    extra_tx_data: dict = {}

    def executePayment(self, *args, **kwargs) -> Payment:
        raise NotImplementedError(f'{self.__class__.__name__} must implement executePayment')

    def beforePayment(self, *args, **kwargs) -> None:
        """Set all data for payment processing."""
        raise NotImplementedError(f'{self.__class__.__name__} must implement beforePayment')

    def set_orders_split_data(self, paymentData: MutableMapping[str, str | float | None | bool], orders: Sequence[Order]) -> None:
        raise NotImplementedError(f'{self.__class__.__name__} must implement set_orders_split_data')

    def afterPaymentProcess(self, response_data, *args, **kwargs):
        pass

    def handle_notifications(self, order: Order):
        if order.paymentMethodType in ['pix', 'boleto', 'picpay'] and order.status == 'waiting_payment':
            if order.paymentMethodType == 'pix':
                title = 'Pix gerado!'
            elif order.paymentMethodType == 'boleto':
                title = 'Boleto gerado!'
            elif order.paymentMethodType == 'openfinance_nubank':
                title = 'Nubank gerado!'
            else:
                title = 'PicPay gerado!'

            for user in order.commissionedUsers.all():
                if is_notification_enabled(user, NotificationType.ORDER_GENERATED):
                    amount = order.splits.filter(user=user).aggregate(total=Sum('totalAmount')).get('total') or 0
                    send_notification(user, title, 'Sua comissão: {amount}', amount)

    @staticmethod
    def process_payment(paymentData: Mapping[str, Any], orders: Iterable[Order], subscriptions: Iterable[Subscription] = []) -> Payment:
        """Get/Udpate or Create a Payment object and set its related fields."""
        paymentMethod = PaymentMethod.get_payment_method_instance(
            paymentData.get('paymentMethod', ''),
            user=orders[0].product.user,  # type:ignore
        )

        threeDsFinished = PaymentStrategy.threeDsFinished_from_paymentData(paymentData)

        defaults = dict(
            status=paymentData.get('status'),
            amount=paymentData.get('amount'),
            installments=paymentData.get('installments') or 1,
            acquirerType=paymentData.get('acquirerType'),
            paymentMethod=paymentMethod,
            pix=paymentData.get('pix'),
            card=paymentData.get('card'),
            boleto=paymentData.get('boleto'),
            picpay=paymentData.get('picpay'),
            googlepay=paymentData.get('googlepay'),
            applepay=paymentData.get('applepay'),
            openFinanceNubank=paymentData.get('openFinanceNubank'),
            paidAt=paymentData.get('paidAt'),
            due_date=paymentData.get('due_date'),
            refundedAt=paymentData.get('refundedAt'),
            chargedbackAt=paymentData.get('chargedbackAt'),
            threeDs=paymentData.get('threeDs'),
            nextStep=paymentData.get('nextStep'),
            threeDsFinished=threeDsFinished
        )

        payment, created = Payment.objects.get_or_create(
            externalId=paymentData.get('id'),
            defaults=defaults
        )

        if not created:
            # Update fields if payment already exists
            for key, value in defaults.items():
                setattr(payment, key, value)
            payment.save(update_fields=defaults.keys())

        payment.orders.set(orders)

        if subscriptions:
            payment.subscriptions.set(subscriptions)
        if not created and not subscriptions:
            payment.subscriptions.clear()

        return payment

    @staticmethod
    def threeDsFinished_from_paymentData(paymentData) -> bool:
        createdAt = timezone.datetime.fromisoformat(paymentData.get('createdAt', '')) if paymentData.get('createdAt') else None
        updatedAt = timezone.datetime.fromisoformat(paymentData.get('updatedAt', '')) if paymentData.get('updatedAt') else None
        if createdAt and updatedAt:
            threeDsFinished = bool(paymentData.get('threeDs') and (createdAt < updatedAt))
        else:
            threeDsFinished = bool(paymentData.get('threeDs'))
        return threeDsFinished

    def createTransaction(self):
        order = self.orders[0]

        splits = self.get_coproduction_splits(order.offer, order.affiliate)  # type:ignore
        payload = self.get_payment_payload(order=order, orders=self.orders, splits=splits)

        tx_response = self.split.createTransaction(**payload)

        self.handle_transaction_response(order, tx_response)

        return tx_response.json()

    def create_subscription(self, *args, **kwargs):
        raise NotImplementedError(f'{self.__class__.__name__} must implement process_parent_order_subscription')

    def validate_subscription_transaction_response(self, payload, transaction_response):
        if not status.is_success(transaction_response.status_code):
            log_data = payload.copy()
            if 'card' in log_data:
                log_data['card'] = 'REDACTED'
            if 'googlepay' in log_data:
                log_data['googlepay'] = 'REDACTED'
            if 'applepay' in log_data:
                log_data['applepay'] = 'REDACTED'
            raise SubscriptionCreationError('payload:', log_data, 'response:', transaction_response.content.decode())

    def create_new_subscription_order(
        self,
        parent_order,
        subscription,
        webhook_data: dict = {},
    ) -> Order:

        order = Order.objects.create(
            subscription=subscription,
            customer=parent_order.customer,
            amount=webhook_data.get('amount'),
            baseAmount=parent_order.baseAmount,
            paymentMethod=parent_order.paymentMethod,
            product=parent_order.product,
            offer=parent_order.offer,
            parent_order=parent_order,
            type='subscription',
            card=parent_order.card,
            affiliate=parent_order.affiliate,
            subscription_period=subscription.current_period + 1,
            coupon=parent_order.coupon,
            couponCode=parent_order.couponCode,
            checkout=parent_order.checkout,
        )

        self.set_orders_split_data(paymentData=webhook_data, orders=[order])

        return order

    def process_new_subscription_payment(self, subscription: Subscription, webhook_data: dict) -> Payment:
        parent_order = subscription.parent_order

        order = self.create_new_subscription_order(parent_order, subscription, webhook_data)

        payment = self.process_payment(paymentData=webhook_data, orders=[order], subscriptions=[subscription])

        payment.orders.set([order])

        subscription.update_recurrence_metrics(commit=False)
        next_payment_date: str | timezone.datetime = webhook_data.get('subscription', {}).get('next_payment_date') or subscription.next_payment_date  # type:ignore
        subscription.set_next_payment_date(datetime=next_payment_date, commit=False)
        subscription.save()

        return payment

    def to_subscription_model(
        self,
        parent_order: Order,
        subscription: Subscription | None = None,
        commit: bool = True
    ) -> Subscription:
        if not subscription:
            subscription = Subscription()

        subscription.amount = parent_order.baseAmount
        subscription.paymentMethod = parent_order.paymentMethod
        subscription.customer = parent_order.customer
        subscription.product = parent_order.product
        subscription.offer = parent_order.offer
        subscription.parent_order = parent_order
        subscription.current_period = 1

        if commit:
            subscription.save()

        parent_order.subscription = subscription
        parent_order.subscription_period = 1

        if commit:
            parent_order.save(update_fields=['subscription', 'subscription_period'])

        return subscription

    def set_split_data_to_subscription(
            self,
            subscription: Subscription,
            subscription_split_data: dict,
            commit: bool = True
    ) -> None:
        """
        Set the subscription fields based on the split data.
        """
        subscription.externalId = subscription_split_data['id']
        subscription.status = subscription_split_data['status']
        subscription.recurrence_period = subscription_split_data.get('recurrence_period')
        subscription.quantity_recurrences = subscription_split_data.get('quantity_recurrences')
        subscription.trial_days = subscription_split_data.get('trial_days')
        subscription.max_retries = subscription_split_data.get('max_retries')
        subscription.retry_interval = subscription_split_data.get('retry_interval')
        subscription.next_payment_date = subscription_split_data.get('next_payment_date')
        subscription.amount = subscription_split_data.get('first_payment', {}).get('amount') or subscription.amount

        if commit:
            subscription.save()

    def get_subscription_payload(self, parent_order: Order) -> dict:
        offer: Offer = parent_order.offer  # type:ignore
        splits = self.get_coproduction_splits(offer, parent_order.affiliate)
        payment_payload = self.get_payment_payload(order=parent_order, orders=[parent_order], splits=splits)
        payment_payload.update({
            'type': 'subscription',
            'amount': float((parent_order.baseAmount or 0) - (parent_order.discount or 0)),
            'recurrence_period': offer.recurrence_period,
            # Caps max recurrences to 100 or -1 if unlimited
            'quantity_recurrences': min(offer.quantity_recurrences, 100) if offer.quantity_recurrences > 0 else -1,
            # Caps trial days to 365
            'trial_days': min(offer.trial_days, 365),
            'max_retries': offer.max_retries,
            'retry_interval': offer.retry_interval,
        })
        if parent_order.card:
            payment_payload['card'] = {
                'token': parent_order.card.token,
            }

        return payment_payload

    def handle_transaction_response(self, order, tx_response):
        if status.is_client_error(tx_response.status_code):
            message = tx_response.json().get('message')
            errors = tx_response.json().get('errors')
            messages = [
                'Esta conta está bloqueada',
                'Esse produto ainda não está disponível para venda.',
            ]
            if message in messages:
                sync_company_status_with_split(order.product.user.company)
            raise ValidationError({'detail': message, 'errors': errors})
        elif not status.is_success(tx_response.status_code):
            raise Exception(tx_response.text)

    @staticmethod
    def create_order_splits(order: Order) -> None:
        """Creates the splits based on order commissions field values."""
        splits_to_create: list[Split] = []
        for commission in order.commissions:
            user = User.objects.filter(id=commission.get('userId')).first()
            if not user:
                continue

            new_split = Split(
                order=order,
                user=user,
                type=commission.get('type'),
                totalAmount=commission.get('commissionValue', 0),
                percentage=commission.get('commissionPercentage', 0),
            )
            splits_to_create.append(new_split)

        Split.objects.bulk_create(splits_to_create, ignore_conflicts=True)

    def get_payment_payload(self, order: Order, orders: Sequence[Order], splits: list[dict] = []) -> dict:
        cart = self.build_cart_from_orders(orders)

        amount = sum(order.amount or 0 for order in orders) if orders else 0

        payload = {
            'amount': float(amount or order.amount or 0),
            'paymentMethod': order.paymentMethodType,
            'customer': {
                'name': order.customer.name,
                'email': order.customer.email,
                'phone': order.customer.phone,
                'birthDate': str(order.customer.birthDate) if order.customer.birthDate else None,
                'docType': order.customer.docType,
                'docNumber': order.customer.docNumber,
            },
            'ip': order.client_ip,
            'userAgent': order.client_user_agent,
            'address': self.build_address_payload(order) if order.address else {},
            'installments': order.installments,
            'exId': ','.join(item.pop('exId', '') for item in cart),
            'feeByItem': True,
            'items': cart,
            'seller_id': str(order.product.user.company.externalId),
            'splits': splits,
            'expiresInDays': order.product.ticketExpiration if order.paymentMethodType == 'boleto' else 1,
            'pixExpiresIn': order.product.pixExpiresIn,
            'checkoutUrl': order.checkoutUrl,
            'saveCard': order.saveCard,
            **self.extra_tx_data,
        }
        if order.refererUrl:
            payload['refererUrl'] = order.refererUrl

        if self.card:
            self.card['saveCard'] = order.saveCard
            payload['card'] = self.card

        if self.googlepay:
            payload['googlepay'] = self.googlepay

        if self.applepay:
            payload['applepay'] = self.applepay

        if order.affiliate:
            payload['affiliate'] = {
                'id': order.affiliate.user.company.externalId,
                'percentage': float(order.affiliate.commission),
            }

        return payload

    @staticmethod
    def build_address_payload(order: Order) -> dict:
        return {
            'country': order.address.country,
            'state': order.address.state,
            'city': order.address.city,
            'zipcode': order.address.zipcode,
            'street': order.address.street,
            'neighborhood': order.address.neighborhood,
            'complement': order.address.complement,
            'number': order.address.number,
        }

    def get_commissions(self, offer: Offer, amountAfterCaktoFees: Decimal, affiliate: Union[Affiliate, None]):
        commissionedUsers = []
        commissions = []
        totalPercentage = float(100)
        totalCommissions = Decimal()

        # Affiliate
        amountAfterAffiliateCommission = amountAfterCaktoFees
        if affiliate is not None:
            commissionValue = (amountAfterCaktoFees * (affiliate.commission / 100)).quantize(Decimal('1.00'), ROUND_HALF_UP)
            commissionedUsers.append(affiliate.user)
            commissions.append({
                'userId': affiliate.user.pk,
                'type': 'affiliate',
                'commissionPercentage': float(affiliate.commission),
                'commissionValue': float(round(commissionValue, 2)),
            })
            amountAfterAffiliateCommission = amountAfterCaktoFees - (amountAfterCaktoFees * (affiliate.commission / 100))
            totalCommissions += commissionValue

        # Coproductions
        amountAfterCoproducersCommission = amountAfterAffiliateCommission
        coproductions = Coproduction.objects.filter(product=offer.product)
        for coproduction in coproductions:
            if coproduction.status != 'active':
                continue

            producer_sale = affiliate is None
            receive_from_both = coproduction.receiveSalesFromAffiliate and coproduction.receiveSalesFromProducer
            receive_only_from_producer = coproduction.receiveSalesFromProducer and (not coproduction.receiveSalesFromAffiliate)
            receive_only_from_affiliate = (not coproduction.receiveSalesFromProducer) and coproduction.receiveSalesFromAffiliate

            if not receive_from_both:
                if (receive_only_from_affiliate and producer_sale) or (receive_only_from_producer and not producer_sale):
                    continue

            commissionValue = (amountAfterAffiliateCommission * (coproduction.amount / 100)).quantize(Decimal('1.00'), ROUND_HALF_UP)
            coproducer = coproduction.user
            commissionedUsers.append(coproducer)
            commissions.append({
                'userId': coproducer.pk,
                'type': 'coproducer',
                'commissionPercentage': float(coproduction.amount),
                'commissionValue': float(round(commissionValue, 2)),
            })
            amountAfterCoproducersCommission -= amountAfterAffiliateCommission * (coproduction.amount / 100)
            totalPercentage -= float(coproduction.amount)
            totalCommissions += commissionValue

        # Producer
        producer = offer.product.user
        commissionedUsers.append(producer)
        commissions.append({
            'userId': producer.pk,
            'type': 'producer',
            'commissionPercentage': totalPercentage,
            'commissionValue': float(round(amountAfterCaktoFees - totalCommissions, 2)),
        })

        return commissionedUsers, commissions

    def get_coproduction_splits(self, offer: Offer, affiliate: Union[Affiliate, None]):
        splits: list[dict[str, str | float]] = []

        # Coproductions
        coproductions = Coproduction.objects.filter(product=offer.product)
        for coproduction in coproductions:
            # Filter only active coproductions in this momment
            if coproduction.status != 'active':
                continue

            # Checks if the coproduction is allowed to receive sales from the affiliate or producer or both
            producer_sale = affiliate is None
            receive_from_both = coproduction.receiveSalesFromAffiliate and coproduction.receiveSalesFromProducer
            receive_only_from_producer = coproduction.receiveSalesFromProducer and (not coproduction.receiveSalesFromAffiliate)
            receive_only_from_affiliate = (not coproduction.receiveSalesFromProducer) and coproduction.receiveSalesFromAffiliate
            if not receive_from_both:
                if (receive_only_from_affiliate and producer_sale) or (receive_only_from_producer and not producer_sale):
                    continue

            coproduction_percentage = round(float(coproduction.amount), 2)
            splits.append({
                'seller_id': coproduction.user.company.externalId,
                'percentage': coproduction_percentage,
            })

        return splits

    def build_cart_item_obj(self, order: Order) -> dict:
        return {
            'title': order.product.name,
            'unitPrice': float(order.amount),  # type:ignore
            'quantity': 1,
            'tangible': False,
            'externalRef': order.offer.id,
            'exId': order.refId,
        }

    def build_cart_from_orders(self, orders: Iterable[Order]) -> list[dict]:
        cart = []
        for order in orders:
            cart.append(self.build_cart_item_obj(order))
        return cart

    @classmethod
    def handle_payment_status_change(
        cls,
        payment: Payment,
        current_status: str,
        new_status: str,
        webhook_data: dict
    ) -> None:
        if current_status == new_status:
            return

        payment_orders = payment.orders.all()
        handler_function = None
        order_date_field = None

        is_approved_now = current_status != PaymentStatus.PAID.id and new_status == PaymentStatus.PAID.id

        if is_approved_now:
            cls.process_pixel_approval_event(payment)
            handler_function = handle_approval
            order_date_field = 'paidAt'
            payment.paidAt = normalize_datetime_input(webhook_data.get('paidAt') or timezone.now())
        elif current_status == PaymentStatus.PAID.id and new_status == PaymentStatus.REFUNDED.id:
            handler_function = handle_refund
            order_date_field = 'refundedAt'
        elif current_status == PaymentStatus.PAID.id and new_status == PaymentStatus.MED.id:
            handler_function = handle_med
            order_date_field = 'chargedbackAt'
        elif current_status == PaymentStatus.PAID.id and new_status == PaymentStatus.CHARGEDBACK.id:
            handler_function = handle_chargeback
            order_date_field = 'chargedbackAt'
        elif current_status == PaymentStatus.WAITING_PAYMENT.id and new_status == PaymentStatus.REFUSED.id:
            handler_function = handle_refused
        elif current_status == PaymentStatus.SCHEDULED.id and new_status == PaymentStatus.WAITING_PAYMENT.id:
            handler_function = handle_subcription_renewal_payment.delay
        elif current_status != PaymentStatus.CANCELED.id and new_status == PaymentStatus.CANCELED.id:
            order_date_field = 'canceledAt'

        for order in payment_orders:
            order.status = new_status
            order.releaseDate = cls.extract_release_date_from_webhook_data(webhook_data)
            received_date = normalize_datetime_input(webhook_data.get(order_date_field) or timezone.now())
            setattr(order, order_date_field, received_date) if order_date_field else None

        order_fields_to_update = ['status', 'releaseDate']
        order_fields_to_update.append(order_date_field) if order_date_field else None

        Order.objects.bulk_update(payment_orders, order_fields_to_update)
        payment.status = new_status
        payment.save(update_fields=['status', 'paidAt'])

        if handler_function:
            [handler_function(payment=payment, order=order, webhook_data=webhook_data) for order in payment_orders]

        if is_approved_now:
            send_purchase_event_to_fb.delay(orders=list(payment_orders))

    @classmethod
    def process_pixel_approval_event(cls, payment):
        payment_orders = payment.orders.all()

        order = payment_orders[0]

        is_new_subscription = order.subscription and (order.subscription.current_period == 1)

        if not order.subscription or is_new_subscription:
            dispatch_pixel_events.delay(
                event='purchase_approved',
                orders=payment_orders,
                payment=payment,
            )

    @classmethod
    def extract_release_date_from_webhook_data(cls, webhook_data):
        release_date = None

        for split in webhook_data.get('splits', []):
            if split.get('type') == 'producer':
                release_date = (split.get('pendingBalance') or {}).get('releaseDate')
                break

        if not release_date:
            release_date = (webhook_data.get('pending') or {}).get('releaseDate')

        return release_date

    @classmethod
    def handle_subscription_status_change(
        cls,
        subscription: Subscription,
        current_status: str,
        new_status: str,
        webhook_data: dict
    ) -> None:
        if current_status == new_status:
            return

        subscription.status = new_status

        if current_status == SubscriptionStatus.ACTIVE.id and new_status == SubscriptionStatus.CANCELED.id:
            handle_subscription_canceled(subscription=subscription, webhook_data=webhook_data)

    @staticmethod
    def get_main_product_from_paymentData(paymentData: Mapping[str, Any]) -> Product:
        # assuming that the first item in the items list is the main product
        # following the same order that we send to split
        # i did some tests and it seems to be the case
        offer_id = paymentData.get('items', [])[0]['externalRef']
        product = Product.objects.filter(offers__pk=offer_id).select_related('user').first()
        if not product:
            raise Exception('Product not found')
        return product

    @staticmethod
    def get_affiliate_from_paymentData(paymentData: Mapping[str, Any], product: Product) -> Affiliate | None:
        affiliate_email = next(
            (split.get('user', {}).get('email') for split in paymentData.get('splits', []) if split.get('type') == 'affiliate'),
            None
        )

        if not affiliate_email:
            return None

        # Try to get the affiliate from the checkoutUrl
        checkout_url_params = parse_qs(urlparse(paymentData.get('checkoutUrl')).query)
        if 'affiliate' in checkout_url_params:
            afid = checkout_url_params.get('affiliate', [None])  # type:ignore
            affiliate = Affiliate.objects.filter(id=afid[0], user__email=affiliate_email, product=product).first()
            if affiliate:
                return affiliate

        affiliate = Affiliate.objects.filter(user__email=affiliate_email, product=product).first()

        if not affiliate:
            raise Exception('Affiliate not found')

        return affiliate

    def get_orders_and_subscriptions_from_paymentData(self, paymentData: Mapping[str, Any]) -> tuple[list[Order], list[Subscription]]:
        customer_processor = CustomerPaymentProcessor(customer_data=paymentData.get('customer'))
        customer = customer_processor.get_or_new_customer()

        product = self.get_main_product_from_paymentData(paymentData=paymentData)

        affiliate = self.get_affiliate_from_paymentData(paymentData=paymentData, product=product)

        parent_order = None
        orders = []
        subscriptions = []

        exIds = paymentData.get('exId', '').split(',')

        for index, item_exId in enumerate(zip(paymentData.get('items', []), exIds)):
            item, exId = item_exId

            offer = Offer.objects.get(id=item.get('externalRef'))

            offer_type = 'main' if index == 0 else 'orderbump'

            paymentMethod = PaymentMethod.get_payment_method_instance(
                paymentData.get('paymentMethod', ''),
                user=product.user,
            )

            defaults = {
                'externalId': paymentData.get('id'),
                'parent_order': parent_order,
                'customer': customer,
                'product': offer.product,
                'affiliate': affiliate,
                'offer': offer,
                'offer_type': offer_type or 'main',
                'type': offer.type,
                'baseAmount': item.get('unitPrice') or offer.price,
                'paymentMethod': paymentMethod,
                'installments': paymentData.get('installments') or 1,
                'checkoutUrl': paymentData.get('checkoutUrl'),
                'refererUrl': paymentData.get('refererUrl'),

                # Fields that we can't get from the split data
                # coupon=coupon,
                # couponCode=coupon.code if coupon else None,
                # discount=offer.price - order_amount,
                # saveCard=data.get('saveCard') or False,
                # utm_source=metadata.get('utm_source'),
                # utm_medium=metadata.get('utm_medium'),
                # utm_campaign=metadata.get('utm_campaign'),
                # utm_term=metadata.get('utm_term'),
                # utm_content=metadata.get('utm_content'),
                # sck=metadata.get('sck'),
                # client_user_agent=metadata.get('client_user_agent') or self.request.META.get('HTTP_USER_AGENT'),
                # client_ip=get_client_ip(self.request),
                # fbc=data.get('fbc'),
                # fbp=data.get('fbp'),
            }

            order, _ = Order.objects.get_or_create(
                refId=exId,
                defaults=defaults
            )
            orders.append(order)

            parent_order = order if index == 0 else parent_order

            subscription = self.get_subscription_from_paymentData(
                paymentData=paymentData,
                parent_order=order,
            )
            subscriptions.append(subscription) if subscription else None

        return orders, subscriptions

    def get_subscription_from_paymentData(
        self,
        paymentData: Mapping[str, Any],
        parent_order: Order,
    ) -> Subscription | None:
        from gateway.strategies.pix_strategy import PixStrategy
        is_two_step_payment = parent_order.paymentMethod in ['pix', 'picpay', 'boleto', 'openfinance_nubank']

        if is_two_step_payment and parent_order.type == ProductType.SUBSCRIPTION.id and not paymentData.get('subscription'):
            return PixStrategy.create_inital_subscription(parent_order=parent_order)

        if not paymentData.get('subscription'):
            return

        subscription_data = paymentData.get('subscription', {})

        # at some point paymentData['subscription'] can be just the subscription id (int)
        if isinstance(subscription_data, int):
            subscription_id = subscription_data
            amount = 0
        else:
            subscription_id = subscription_data.get('id')
            amount = subscription_data.get('amount') or 0

        defaults = {
            'amount': amount,
            'paymentMethod': parent_order.paymentMethod,
            'customer': parent_order.customer,
            'product': parent_order.product,
            'offer': parent_order.offer,
            'parent_order': parent_order,
            'current_period': 1,
        }

        subscription, _ = Subscription.objects.get_or_create(
            externalId=subscription_id,
            defaults=defaults
        )

        if parent_order.subscription is None:
            parent_order.subscription = subscription

        self.process_subscription_parent_order(parent_order, subscription_id, subscription)

        subscription.update_recurrence_metrics(commit=False)

        subscription.save()

        parent_order.save()

        return subscription

    def process_subscription_parent_order(self, order: Order, subscription_id, subscription: Subscription):
        # import pdb; pdb.set_trace()
        subscription_split_data = SplitPay().getSubscription(subscription_id=subscription_id).json()

        # Find current order index in subscription payments
        subscription_period = subscription_split_data.get('payments', []).index(order.externalId) + 1

        # If not the first payment(parent order), get parent order for the current order
        if subscription_period > 1:
            subscription_main_order_split_data = SplitPay().getPayment(
                id=subscription_split_data.get('payments')[0]
            ).json()

            subscription_main_orders, _ = self.get_orders_and_subscriptions_from_paymentData(
                paymentData=subscription_main_order_split_data
            )

            from gateway.services.payment_factory import PaymentStrategyFactory
            strategy = PaymentStrategyFactory.get_strategy(subscription_main_order_split_data.get('paymentMethod', ''))

            strategy.set_orders_split_data(paymentData=subscription_main_order_split_data, orders=subscription_main_orders)

            strategy.process_payment(
                paymentData=subscription_main_order_split_data,
                orders=subscription_main_orders,
                subscriptions=[subscription]
            )

            order.parent_order = subscription_main_orders[0]

        order.subscription_period = subscription_period

        PaymentStrategy().set_split_data_to_subscription(
            subscription=subscription,
            subscription_split_data=subscription_split_data,
            commit=False
        )

    def process_payment_from_split_data(self, paymentData) -> Payment:
        from gateway.services.payment_factory import PaymentStrategyFactory

        orders, subscriptions = self.get_orders_and_subscriptions_from_paymentData(paymentData=paymentData)

        strategy = PaymentStrategyFactory.get_strategy(paymentData.get('paymentMethod', ''))

        strategy.set_orders_split_data(paymentData=paymentData, orders=orders)

        payment = strategy.process_payment(
            paymentData=paymentData,
            orders=orders,
            subscriptions=subscriptions
        )

        # Change current status following the reverse of the handle_payment_status_change
        # to make the last event of the payment status change be dispatched
        current_status = PaymentStatus.WAITING_PAYMENT.id
        new_status = payment.status

        if new_status in [
            PaymentStatus.REFUNDED.id,
            PaymentStatus.MED.id,
            PaymentStatus.CHARGEDBACK.id
        ]:
            current_status = PaymentStatus.PAID.id
        elif new_status == PaymentStatus.WAITING_PAYMENT.id:
            current_status = PaymentStatus.SCHEDULED.id

        strategy.handle_payment_status_change(
            payment=payment,
            current_status=current_status,
            new_status=new_status,
            webhook_data=paymentData
        )

        return payment
