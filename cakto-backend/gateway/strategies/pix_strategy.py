from decimal import Decimal
from typing import Iterable, MutableMapping, Sequence

from apps.services.event_manager import dispatch_app_events, dispatch_pixel_events
from email_service.mail import send_payment_mail
from gateway.enums import SubscriptionStatus
from gateway.models import Order, Payment, Subscription
from gateway.utils import STATUS_MAPPING
from product.enums import ProductType

from .base import PaymentStrategy


class PixStrategy(PaymentStrategy):
    def beforePayment(self, *args, **kwargs):
        self.orders = kwargs.get('orders', [])
        self.extra_tx_data = kwargs.get('extra_tx_data', {})

    def executePayment(self) -> Payment:
        tx = self.createTransaction()
        data = tx.get('data', {})

        self.set_orders_split_data(paymentData=data, orders=self.orders)

        payment = self.process_payment(paymentData=data, orders=self.orders)

        self.handle_payment_email(payment=payment)

        self.handle_new_payment_event(payment=payment, orders=self.orders)

        self.handle_initial_subscriptions(orders=self.orders, payment=payment)

        return payment

    def set_orders_split_data(self, paymentData: MutableMapping, orders: Sequence[Order]) -> None:
        """Sets the fees, commissions, amount, and external info for pix, boleto and picpay orders."""
        assert all(order.paymentMethodType in ['pix', 'boleto', 'picpay', 'openfinance_nubank'] for order in orders), 'Only pix, boleto, openfinance_nubank and picpay orders allowed'
        paymentData['status'] = STATUS_MAPPING.get(paymentData['status'], paymentData['status'])  # type:ignore

        fixedFee = Decimal(paymentData.get('fixedFee') or 0)
        variableFee = Decimal(paymentData.get('percentageFee') or 0)
        itemFixedFee = fixedFee / len(orders)  # type:ignore

        for order in orders:
            # get baseAmount from received paymentData or order.baseAmount
            payment_items = paymentData.get('items') or []
            order_baseAmount = Decimal(next(
                (item['unitPrice'] for item in payment_items if item['externalRef'] == order.offer.id),
                (order.baseAmount - (order.discount or 0)) or 0
            ))

            orderTotalPercentage = order_baseAmount / Decimal(paymentData.get('baseAmount'))  # type:ignore
            itemVariableFee = (orderTotalPercentage * variableFee)
            orderLiquidAmount = order_baseAmount - itemFixedFee - itemVariableFee  # type:ignore

            commissionedUsers, commissions = self.get_commissions(order.offer, orderLiquidAmount, order.affiliate)  # type:ignore
            order.commissions = commissions
            order.commissionedUsers.set(commissionedUsers)

            self.create_order_splits(order)

            order.status = paymentData.get('status', order.status)
            order.reason = paymentData.get('reason', order.reason)
            order.amount = Decimal(paymentData.get('amount') or 0) * orderTotalPercentage
            order.externalId = paymentData.get('id')
            order.acquirerType = paymentData.get('acquirerType')
            order.fees = itemFixedFee + itemVariableFee
            order.checkoutUrl = paymentData.get('checkoutUrl')
            order.due_date = paymentData.get('due_date')

        Order.objects.bulk_update(orders, [
            'amount', 'status', 'installments', 'acquirerType', 'externalId',
            'fees', 'commissions', 'due_date', 'checkoutUrl', 'reason',
        ])

    @staticmethod
    def handle_payment_email(payment: Payment) -> None:
        send_payment_mail.delay(payment)

    @staticmethod
    def handle_new_payment_event(payment: Payment, orders: Iterable[Order]) -> None:
        if payment.paymentMethodType == 'pix':
            event = 'pix_gerado'
        elif payment.paymentMethodType == 'boleto':
            event = 'boleto_gerado'
        elif payment.paymentMethodType == 'openfinance_nubank':
            event = 'openfinance_nubank_gerado'
        else:
            event = 'picpay_gerado'

        [dispatch_app_events.delay(event, order=order, payment=payment) for order in orders]
        dispatch_pixel_events.delay(event, orders=orders, payment=payment)

    def handle_initial_subscriptions(self, orders: Iterable[Order], payment: Payment) -> None:
        payment_subscriptions = []
        for order in orders:
            if order.type == ProductType.SUBSCRIPTION.id:
                subscription = self.create_initial_subscription(order)
                payment_subscriptions.append(subscription)

        if payment_subscriptions:
            payment.subscriptions.set(payment_subscriptions)

    def create_initial_subscription(self, parent_order: Order) -> Subscription:
        defaults = {
            'externalId': None,
            'status': SubscriptionStatus.INACTIVE.id,
            'paymentMethod': parent_order.paymentMethod,
            'customer': parent_order.customer,
            'current_period': 1,
            'amount': parent_order.amount,
            'product': parent_order.product,
            'offer': parent_order.offer,
        }

        subscription, created = Subscription.objects.get_or_create(
            parent_order=parent_order,
            defaults=defaults
        )

        if created:
            subscription.set_config_fields_from_offer(parent_order.offer, save=False)  # type:ignore
            subscription.save()

        parent_order.subscription = subscription
        parent_order.subscription_period = 1
        parent_order.save(update_fields=['subscription', 'subscription_period'])

        return subscription

    def create_subscription(self, parent_order: Order) -> Subscription:
        subscription = parent_order.subscription

        if subscription and (subscription.externalId is not None):
            return subscription

        payload = self.get_subscription_payload(parent_order)
        payload['trial_days'] = min(subscription.recurrence_period, 365)

        response = self.split.createSubscription(**payload)

        self.validate_subscription_transaction_response(payload, response)

        split_data = response.json().get('subscription', {})

        subscription = self.to_subscription_model(
            parent_order,
            subscription=subscription,
            commit=False,
        )

        self.set_split_data_to_subscription(
            subscription,
            split_data,
            commit=False,
        )

        subscription.status = SubscriptionStatus.ACTIVE.id

        subscription.update_recurrence_metrics(commit=False)

        subscription.save()

        return subscription
