import hashlib
import requests

from django.conf import settings

from user.models import User
from gateway.strategies.base_email_marketing import BaseEmailMarketingStrategy


class MailchimpStrategy(BaseEmailMarketingStrategy):
    API_BASE_URL = f"https://{settings.MAILCHIMP_SERVER_PREFIX}.api.mailchimp.com/3.0"
    AUDIENCE_ID = settings.MAILCHIMP_AUDIENCE_ID
    API_KEY = settings.MAILCHIMP_API_KEY

    def has_prerequisites(self):
        return settings.MAILCHIMP_SERVER_PREFIX \
            and settings.MA<PERSON>CHIMP_AUDIENCE_ID \
            and settings.MAILCHIMP_API_KEY

    def handle_contact_send(self, user: User) -> None:
        tag = self.get_sales_tag(user.totalSales())

        url = f"{self.API_BASE_URL}/lists/{self.AUDIENCE_ID}/members/{self.get_subscriber_hash(user.email)}"
        payload = {
            "email_address": user.email,
            "status": "subscribed",
            "merge_fields": {
                "FNAME": user.first_name or '',
                "LNAME": user.last_name or '',
            },
        }

        response = requests.put(
            url,
            auth=("anystring", self.API_KEY),
            json=payload,
        )

        response.raise_for_status()

        # Adiciona tag
        tag_url = f"{self.API_BASE_URL}/lists/{self.AUDIENCE_ID}/members/{self.get_subscriber_hash(user.email)}/tags"
        tag_payload = {
            "tags": [
                {
                    "name": tag,
                    "status": "active"
                }
            ]
        }
        response = requests.post(
            tag_url,
            auth=("anystring", self.API_KEY),
            json=tag_payload,
        )

        response.raise_for_status()

    @staticmethod
    def get_subscriber_hash(email: str) -> str:
        return hashlib.md5(email.lower().encode()).hexdigest()

    @staticmethod
    def get_sales_tag(total_sales: float) -> str:
        tags = [
            (1_000_000, "Acima de 1M"),
            (500_000, "Acima de 500k"),
            (250_000, "Acima de 250k"),
            (100_000, "Acima de 100k"),
            (50_000, "Acima de 50k"),
            (10_000, "Acima de 10k"),
        ]

        for threshold, tag in tags:
            if total_sales >= threshold:
                return tag

        return "Abaixo de 10k"
