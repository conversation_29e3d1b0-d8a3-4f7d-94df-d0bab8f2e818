from rest_framework.permissions import BasePermission
from oauth2_provider.contrib.rest_framework import TokenHasScope
from oauth2_provider.contrib.rest_framework.authentication import OAuth2Authentication

class TokenHasScopeIfOauth(BasePermission):
    def has_permission(self, request, view):
        if isinstance(getattr(request, 'successful_authenticator', None), OAuth2Authentication):
            return TokenHasScope().has_permission(request, view)
        return True  # Skip if not using OAuth2
