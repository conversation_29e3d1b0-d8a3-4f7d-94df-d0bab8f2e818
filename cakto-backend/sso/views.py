from django.contrib.auth import login as auth_login
from django.contrib.auth.forms import AuthenticationForm
from django.shortcuts import render, redirect
from django.views.decorators.cache import never_cache
from django.views.decorators.csrf import csrf_protect
from django.contrib.sites.shortcuts import get_current_site
from django.conf import settings
from django.views.decorators.debug import sensitive_post_parameters
from django_otp.forms import OTPAuthenticationForm
from user.models import MFAMethod
from django.http import Http404

@never_cache
@csrf_protect
@sensitive_post_parameters()
def sso_login_view(request):
    mfa_methods = []
    sent = None
    # host need to be sso.cakto.com.br
    if request.META.get('HTTP_HOST') != 'sso.cakto.com.br':
        # Return 404
        raise Http404("Page is not available.")

    if not request.GET.get('next') and not request.session.get('next'):
        return redirect('https://app.cakto.com.br/auth/login')

    if request.user.is_authenticated:
        redirect_to = request.GET.get('next') or request.session.get('next', settings.LOGIN_REDIRECT_URL)
        return redirect(redirect_to)

    if request.method == 'GET':
        request.session['user_id'] = None
        if 'next' in request.GET:
            request.session['next'] = request.GET.get('next')

    if request.method == 'POST':
        # Handle OTP device selection and token sending
        if request.POST.get('otp_device') and request.session.get('user_id'):
            mfa = MFAMethod.objects.get(id=request.POST.get('otp_device'), user__id=request.session['user_id'])
            request.session['otp_device'] = mfa.device.persistent_id
            if mfa.confirmed:
                sent = mfa

        # Save session and user data for authentication
        form = AuthenticationForm(request, data=request.POST)
        if form.is_valid():
            request.session['users'] = request.session.get('users', {})
            request.session['users'][str(form.get_user().id)] = {
                'id': form.get_user().id,
                'full_name': form.get_user().get_full_name(),
                'email': form.cleaned_data.get('username'),
                'image': form.get_user().picture.url if form.get_user().picture else None,
                'password': request.POST.get('password', '')
            }
            request.session['user_id'] = str(form.get_user().id)

        # Override with session data for authentication
        user_id = request.session.get('user_id') or request.POST.get('user_id')
        if user_id:
            request.POST = request.POST.copy()
            user_data = request.session.get('users').get(user_id, {})
            request.POST['username'] = user_data.get('email', request.POST.get('email', ''))
            request.POST['password'] = user_data.get('password', request.POST.get('password', ''))
            request.POST['otp_device'] = request.session.get('otp_device',  '')

            request.session['user_id'] = str(user_data.get('id'))

        # Validate OTP and login user
        form = OTPAuthenticationForm(request, data=request.POST)
        if form.is_valid():
            auth_login(request, form.get_user())
            redirect_to = request.session.get('next', settings.LOGIN_REDIRECT_URL)
            return redirect(redirect_to)
    else:
        # Inicializa o form
        form = AuthenticationForm(request)

    current_site = get_current_site(request)

    if form.get_user():
        mfa_methods = form.get_user().mfa_methods.filter(confirmed=True)
        if not mfa_methods:
            mfa_methods = [MFAMethod.get_email_method(user=form.get_user(), confirmed=True)]

    context = {
        'form': form,
        'site': current_site,
        'site_name': current_site.name,
        'next': request.GET.get('next', ''),
        'sent': sent,
        'users': request.session.get('users', {}).items(),
        'mfa_methods':mfa_methods,
    }
    return render(request, 'sso/login.html', context)
