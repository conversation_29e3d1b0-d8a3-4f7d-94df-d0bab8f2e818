from urllib.parse import urljoin

import django_apscheduler.util
import requests
import telegram
from django.conf import settings
from django.utils import timezone
from django_rq import job
from rest_framework import status
from rest_framework.response import Response
from telegram.request import RequestData
from telegram.request._requestparameter import RequestParameter

from cakto.utils import is_job_scheduled
from members.sdk.base import ProductDeliveryConfigError, set_error_state_to_productDelivery
from product.models import DeliveryAccess, ProductDelivery


class Telegram:
    def __init__(self):
        self.bot_token = settings.TELEGRAM_BOT_TOKEN
        self.bot = telegram.Bot(token=self.bot_token)
        self.base_url: str = f"https://api.telegram.org/bot{self.bot_token}"
        self.front_base_url = settings.FRONT_END_BASE_URL

    def _post(self, endpoint, **data):
        data = {key: value for key, value in data.items() if value is not None}
        data = RequestData(
            parameters=[RequestParameter.from_input(key, value) for key, value in data.items()],
        ).json_parameters

        res = requests.post(f'{self.base_url}/{endpoint}', json=data)

        if status.is_success(res.status_code):
            return res.json()

        self._handle_error(endpoint, res)

    def _handle_error(self, endpoint: str, res: requests.Response):
        """
        Handles expected errors, some cases we will want to ignore and
        others we will disable the integration for that no more errors
        will come from the specific integration.
        """
        if status.is_client_error(res.status_code):
            error_message = res.json().get('description', '')

            IGNORE_ERRORS = [
                'Bad Request: user is an administrator of the chat',
                'Bad Request: can\'t remove chat owner',
                'Bad Request: USER_NOT_PARTICIPANT',
            ]

            DISABLE_ERRORS = [
                'Bad Request: chat not found',
                'Bad Request: not enough rights to restrict/unrestrict chat member',
                'Bad Request: not enough rights to manage chat invite link',
            ]

            if error_message in IGNORE_ERRORS:
                return

            if error_message in DISABLE_ERRORS:
                raise ProductDeliveryConfigError(error_message)

        # Raise exception if is a server error or if the error isn't
        # a expected case
        raise Exception(
            f'Error making Telegram request on {endpoint}, '
            f'status_code: {res.status_code}, '
            f'response: {res.content.decode()}',
        )

    def toUpdate(self, data) -> telegram.Update | None:
        return telegram.Update.de_json(data, self.bot)

    def sendMessage(self, **kwargs):
        return self._post('sendMessage', **kwargs)

    def create_chat_invite_link(self, **kwargs):
        return self._post('createChatInviteLink', **kwargs)

    def revokeChatInviteLink(self, **kwargs):
        return self._post('revokeChatInviteLink', **kwargs)

    def ban_chat_member(self, **kwargs):
        return self._post('banChatMember', **kwargs)

    def unban_chat_member(self, **kwargs):
        return self._post('unbanChatMember', **kwargs)

    def handle_start(self, message: telegram.Message):
        full_name = f'{message.from_user.first_name} {message.from_user.last_name}'.strip()  # type:ignore
        chat_id = message.chat.id
        self.sendMessage(chat_id=chat_id, text=f'Olá, {full_name}!')
        self.members_area_menu(message)
        return Response({"success": True})

    def handle_callback(self, callback: telegram.CallbackQuery):
        if callback.data == '/start' and callback.message:
            chat_id = callback.message.chat.id
            full_name = f'{callback.message.chat.first_name} {callback.message.chat.last_name}'.strip()  # type:ignore

            self.sendMessage(chat_id=chat_id, text=f'Olá, {full_name}!')

            buttons = [[telegram.InlineKeyboardButton(text='Acessar Área de Membros', url=urljoin(self.front_base_url, '/student/courses'))]]
            reply_markup = telegram.InlineKeyboardMarkup(buttons)
            text = (
                'Se você tiver um código de acesso, por favor, digite-o.'
                '\nCaso contrário, Acesse sua área de membros para ter acesso ao conteúdo:'
            )

            self.sendMessage(chat_id=chat_id, text=text, reply_markup=reply_markup)

        return Response({"success": True})

    def handle_customer_access(self, message: telegram.Message):
        text: str = message.text  # type:ignore
        first_name: str = message.from_user.first_name  # type:ignore
        last_name: str = message.from_user.last_name  # type:ignore
        tl_user_id = message.from_user.id  # type:ignore
        full_name = f'{message.from_user.first_name} {message.from_user.last_name}'.strip()  # type:ignore
        chat_id = message.chat.id

        if message.text and 'start' in message.text:
            self.sendMessage(chat_id=chat_id, text=f'Olá, {full_name}!')

        deliveryAccess = DeliveryAccess.objects.filter(fields__access_code=text.split(' ')[-1]).first()
        if not deliveryAccess:
            self.sendMessage(chat_id=chat_id, text='Código de acesso inválido ou já utilizado. Por favor, tente novamente.')
            self.members_area_menu(message)
        elif deliveryAccess.status == 'active' and deliveryAccess.fields.get('invite_link'):
            chat_type = deliveryAccess.productDelivery.fields.get('chat_type')
            text = 'Acessar Canal' if chat_type == 'channel' else 'Acessar Grupo'
            buttons = [[telegram.InlineKeyboardButton(text=text, url=deliveryAccess.fields.get('invite_link'))]]
            reply_markup = telegram.InlineKeyboardMarkup(buttons)
            self.sendMessage(chat_id=chat_id, text=f'Clique abaixo para acessar "{deliveryAccess.productDelivery.fields["chat_title"]}"', reply_markup=reply_markup)
        else:
            productDelivery = deliveryAccess.productDelivery
            try:
                invite_link = self.process_delivery_invite(message, productDelivery)
            except telegram.error.BadRequest as e:
                if e.message == 'Chat not found':
                    productDelivery.status = 'disabled'
                    productDelivery.save()
                    self.sendMessage(chat_id=chat_id, text='Grupo não encontrado. Por favor, entre em contato com o suporte.')
                return Response({"success": True})

            deliveryAccess.fields['tl_user_id'] = tl_user_id
            deliveryAccess.fields['first_name'] = first_name
            deliveryAccess.fields['last_name'] = last_name
            deliveryAccess.fields['invite_link'] = invite_link
            deliveryAccess.status = 'active'

            access_time = deliveryAccess.calculate_access_time()
            deliveryAccess.expiresAt = access_time if isinstance(access_time, timezone.datetime) else None
            deliveryAccess.configuredAt = timezone.now()

            deliveryAccess.save()

        return Response({"success": True})

    def handle_unknown_command(self, message: telegram.Message):
        chat_id = message.chat.id

        buttons = [[telegram.InlineKeyboardButton(text='Recomeçar', callback_data='/start')]]
        reply_markup = telegram.InlineKeyboardMarkup(buttons)

        self.sendMessage(chat_id=chat_id, text='Não entendi sua mensagem.', reply_markup=reply_markup)

        return Response({"success": True})

    def members_area_menu(self, message: telegram.Message):
        chat_id = message.chat.id

        buttons = [[telegram.InlineKeyboardButton(text='Acessar Área de Membros', url=urljoin(self.front_base_url, '/student/courses'))]]
        reply_markup = telegram.InlineKeyboardMarkup(buttons)
        text = (
            'Se você tiver um código de acesso, por favor, digite-o.'
            '\nCaso contrário, Acesse sua área de membros para ter acesso ao conteúdo:'
        )

        self.sendMessage(chat_id=chat_id, text=text, reply_markup=reply_markup)

    def process_delivery_invite(self, message: telegram.Message, productDelivery: ProductDelivery) -> str:
        chat_id = message.chat.id

        invite_link = self.create_chat_invite_link(
            chat_id=productDelivery.fields['chat_id'],
            expire_date=(timezone.now() + timezone.timedelta(days=30)),
            member_limit=1
        )

        url = invite_link.get('result', {}).get('invite_link')  # type:ignore
        reply_markup = telegram.InlineKeyboardMarkup([[telegram.InlineKeyboardButton(text='Acessar Grupo', url=url)]])
        self.sendMessage(chat_id=chat_id, text=f'Clique abaixo para acessar "{productDelivery.fields["chat_title"]}"', reply_markup=reply_markup)
        return url

    def process_productDelivery_config(self, message: telegram.Message):
        chat_id = message.chat.id
        text = message.text
        chat_type = message.chat.type
        message_id = message.message_id
        chat_title = message.chat.title

        productDelivery = ProductDelivery.objects.filter(id=text, contentDelivery__type='telegram').exclude(fields__chat_id=chat_id).first()
        if not productDelivery:
            return Response({"success": True})
        else:
            productDelivery.fields = {'chat_id': chat_id, 'chat_type': chat_type, 'chat_title': chat_title}
            productDelivery.status = 'active'
            productDelivery.save()
            self.sendMessage(chat_id=chat_id, reply_to_message_id=message_id, text='✔')
            return Response({"success": True})

    def handle_new_chat_title(self, message: telegram.Message):
        chat_id = message.chat.id
        chat_title = message.new_chat_title
        productDelivery = ProductDelivery.objects.filter(fields__chat_id=chat_id).first()

        if productDelivery:
            productDelivery.fields['chat_title'] = chat_title
            productDelivery.save()

        return Response({"success": True})

    def remove_user_from_group(self, chat_id: int, tl_user_id: int, invite_link: str = ''):
        self.ban_chat_member(chat_id=chat_id, user_id=tl_user_id)
        self.unban_chat_member(chat_id=chat_id, user_id=tl_user_id, only_if_banned=True)
        self.revokeChatInviteLink(chat_id=chat_id, invite_link=invite_link)

@job
def remove_telegram_access_from_order(order):
    deliveryAccesses = order.productDeliveryAccesses.filter(
        productDelivery__contentDelivery__type='telegram',
        productDelivery__status='active',
    )

    if not deliveryAccesses.exists():
        return

    telegram = Telegram()

    for deliveryAccess in deliveryAccesses:
        try:
            if deliveryAccess.status != 'waiting_config':
                telegram.remove_user_from_group(
                    deliveryAccess.productDelivery.fields['chat_id'],
                    deliveryAccess.fields['tl_user_id'],
                    deliveryAccess.fields['invite_link'],
                )
            deliveryAccess.status = 'canceled'
        except ProductDeliveryConfigError as disable_error:
            set_error_state_to_productDelivery(
                deliveryAccess.productDelivery,
                disable_error
            )

    DeliveryAccess.objects.bulk_update(deliveryAccesses, ['status'])

@job
def remove_telegram_access_job(deliveryAccess: DeliveryAccess, status: str = 'expired'):
    telegram = Telegram()
    deliveryAccess.refresh_from_db()

    try:
        telegram.remove_user_from_group(
            deliveryAccess.productDelivery.fields['chat_id'],
            deliveryAccess.fields['tl_user_id'],
            deliveryAccess.fields['invite_link'],
        )
    except ProductDeliveryConfigError as disable_error:
        productDelivery = deliveryAccess.productDelivery
        set_error_state_to_productDelivery(productDelivery, disable_error)
        # Returns without error, so when the integration is fixed by the user
        # the job will be retried
        return

    deliveryAccess.status = status
    deliveryAccess.save(update_fields=['status'])


@django_apscheduler.util.close_old_connections
def remove_telegram_expired_accesses():
    print('Removing expired telegram accesses')

    deliveryAccesses = DeliveryAccess.objects.filter(
        expiresAt__lte=timezone.now(),
        status='active',
        productDelivery__contentDelivery__type='telegram',
        productDelivery__status='active',
    )

    count = 0
    for deliveryAccess in deliveryAccesses:
        job_id = f'remove_telegram_access_job_{deliveryAccess.id}'
        if not is_job_scheduled(job_id):
            count += 1
            remove_telegram_access_job.delay(deliveryAccess, job_id=job_id)

    print(f'{count} jobs scheduled for removing expired telegram accesses')
