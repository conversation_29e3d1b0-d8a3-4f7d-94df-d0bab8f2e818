from notifications.models import Notification
from product.enums import ProductDeliveryStatus
from product.models import ProductDelivery
from django.db import transaction


class ProductDeliveryConfigError(Exception):
    """Exception raised when there is a configuration error with a ProductDelivery."""
    pass

@transaction.atomic
def set_error_state_to_productDelivery(productDelivery: ProductDelivery, error: ProductDeliveryConfigError):
    productDelivery.status = ProductDeliveryStatus.WITH_ERROR.id
    productDelivery.fields['error_detail'] = error.args[0] if error.args else ''
    productDelivery.save(update_fields=['status', 'fields'])

    # Notify user about the error
    title = 'Integração Telegram Desativada'
    product_name = (productDelivery.product.name[:15] + '...') if len(productDelivery.product.name) > 15 else productDelivery.product.name
    message = f'A integração do Telegram de "{product_name}" foi desativada devido a um erro de configuração. Por favor, verifique as configurações do produto.'

    Notification.objects.create(
        user=productDelivery.product.user,
        title=title,
        description=message,
    )
