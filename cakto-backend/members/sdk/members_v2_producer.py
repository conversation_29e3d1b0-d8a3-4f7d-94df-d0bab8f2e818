import hashlib
import hmac
import json
import logging
from typing import Dict, Any, Optional
from urllib.parse import urljoin

import requests
from django.conf import settings
from django_rq import job
from rq import Retry

from user.models import User
from product.models import Product

logger = logging.getLogger(__name__)


class MembersV2ProducerService:
    def __init__(self):
        self.api_url = settings.CAKTO_MEMBERS_V2_BASE_URL
        self.api_key = settings.CAKTO_MEMBERS_V2_API_KEY
        self.webhook_url = urljoin(self.api_url, '/api/webhooks/cakto/producer-created')

    def _get_auth_headers(self) -> Dict[str, str]:
        headers = {
            'Content-Type': 'application/json',
            'User-Agent': 'CaktoBot/1.0',
        }

        if self.api_key:
            headers['X-API-Key'] = self.api_key

        return headers

    def _prepare_producer_data(self, user: User) -> Dict[str, Any]:
        return {
            "mainAppUserId": str(user.id),
            "name": f"{user.first_name} {user.last_name}".strip() or user.email,
            "email": user.email,
            "role": "producer",
            "emailVerified": user.emailValidated,
            "onboardingComplete": user.register_profile_completed,
        }

    def _generate_hmac_signature(self, payload: str) -> str:
        return ""

    def _prepare_producer_payload(self, user: User, product: Product) -> Dict[str, Any]:
        payload = {
            "secret": str(self.api_key),
            "event": "producer_created",
            "data": {
                "producer": {
                    "id": str(user.id),
                    "name": user.name or user.email,
                    "email": user.email,
                    "phone": user.phone or "",
                },
                "product": {
                    "id": str(product.id),
                    "name": product.name,
                    "contentDelivery": product.contentDelivery,
                }
            }
        }

        return payload

    def create_producer(self, user: User) -> Optional[str]:
        try:
            if not self.api_url or not self.api_key:
                logger.error("Cakto Members V2 API not configured")
                return None

            url = urljoin(self.api_url, '/api/admin/users/producer')
            headers = self._get_auth_headers()
            data = self._prepare_producer_data(user)

            logger.info(f"Creating producer in Cakto Members V2: {user.email}")

            response = requests.post(
                url,
                json=data,
                headers=headers,
                timeout=30
            )

            if response.status_code == 200:
                result = response.json()
                producer_id = result.get('user', {}).get('id')
                logger.info(f"Producer created successfully in Cakto Members V2: {producer_id}")
                return producer_id
            else:
                logger.error(f"Failed to create producer in Cakto Members V2: {response.status_code} - {response.text}")
                return None

        except Exception as e:
            logger.error(f"Error creating producer in Cakto Members V2: {str(e)}", exc_info=True)
            return None

    def update_producer(self, user: User, members_v2_id: str) -> bool:
        try:
            if not self.api_url or not self.api_key:
                logger.error("Cakto Members V2 API not configured")
                return False

            url = urljoin(self.api_url, f'/api/admin/users/{members_v2_id}')
            headers = self._get_auth_headers()
            data = self._prepare_producer_data(user)

            logger.info(f"Updating producer in Cakto Members V2: {members_v2_id}")

            response = requests.put(
                url,
                json=data,
                headers=headers,
                timeout=30
            )

            if response.status_code == 200:
                logger.info(f"Producer updated successfully in Cakto Members V2: {members_v2_id}")
                return True
            else:
                logger.error(f"Failed to update producer in Cakto Members V2: {response.status_code} - {response.text}")
                return False

        except Exception as e:
            logger.error(f"Error updating producer in Cakto Members V2: {str(e)}")
            return False

    def get_producer_by_main_app_id(self, main_app_user_id: str) -> Optional[Dict[str, Any]]:
        try:
            if not self.api_url or not self.api_key:
                logger.error("Cakto Members V2 API not configured")
                return None

            url = urljoin(self.api_url, f'/api/admin/users/main-app/{main_app_user_id}')
            headers = self._get_auth_headers()

            response = requests.get(
                url,
                headers=headers,
                timeout=30
            )

            if response.status_code == 200:
                result = response.json()
                return result.get('data')
            elif response.status_code == 404:
                return None
            else:
                logger.error(f"Failed to get producer from Cakto Members V2: {response.status_code} - {response.text}")
                return None

        except Exception as e:
            logger.error(f"Error getting producer from Cakto Members V2: {str(e)}", exc_info=True)
            return None

    def send_producer_webhook(self, user: User, product: Product) -> bool:
        try:
            if not self.webhook_url:
                logger.error("Cakto Members V2 webhook URL not configured")
                return False

            if product.contentDelivery != 'cakto':
                logger.info(f"Product {product.id} does not use Cakto Members delivery, skipping producer webhook")
                return True

            payload = self._prepare_producer_payload(user, product)

            headers = {
                'Content-Type': 'application/json',
                'User-Agent': 'CaktoBot/1.0',
            }

            logger.info(f"Sending producer webhook to {self.webhook_url} for product {product.id}")

            response = requests.post(
                self.webhook_url,
                json=payload,
                headers=headers,
                timeout=30
            )

            if response.status_code == 200:
                logger.info(f"Producer webhook sent successfully for product {product.id}")
                return True
            else:
                logger.error(f"Producer webhook failed for product {product.id}: {response.status_code} - {response.text}")
                return False

        except Exception as e:
            logger.error(f"Error sending producer webhook for product {product.id}: {str(e)}", exc_info=True)
            return False


# Global instance
members_v2_producer_service = MembersV2ProducerService()


@job('default', retry=Retry(max=3, interval=[5, 30, 300]))
def send_producer_webhook_to_members_v2(user: User, product: Product) -> bool:
    """Background job to send producer webhook to Cakto Members V2"""
    return members_v2_producer_service.send_producer_webhook(user, product)


@job('default', retry=Retry(max=3, interval=[5, 30, 300]))
def sync_producer_to_members_v2(user: User, product: Product) -> Optional[str]:
    try:
        if product.contentDelivery != 'cakto':
            logger.info(f"Product {product.id} does not use Cakto Members delivery, skipping producer sync")
            return None

        # Send webhook to create producer in Members V2
        send_producer_webhook_to_members_v2.delay(user, product)

        existing_producer = members_v2_producer_service.get_producer_by_main_app_id(str(user.id))

        if existing_producer:
            producer_id = existing_producer['id']
            success = members_v2_producer_service.update_producer(user, producer_id)
            if success:
                logger.info(f"Producer updated in Cakto Members V2: {producer_id}")
                return producer_id
            else:
                logger.error(f"Failed to update producer in Cakto Members V2: {user.email}")
                return None
        else:
            producer_id = members_v2_producer_service.create_producer(user)
            if producer_id:
                logger.info(f"Producer created in Cakto Members V2: {producer_id}")
                return producer_id
            else:
                logger.error(f"Failed to create producer in Cakto Members V2: {user.email}")
                return None

    except Exception as e:
        logger.error(f"Error syncing producer to Cakto Members V2: {str(e)}", exc_info=True)
        return None
