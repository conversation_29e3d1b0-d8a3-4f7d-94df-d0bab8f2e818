from unittest import mock
from urllib.parse import urljoin

from django.conf import settings
from rest_framework import status

from cakto.tests.base import BaseTestCase
from financial.utils import (
    handle_purchase_course_access,
)
from members.sdk.members_v2_producer import sync_producer_to_members_v2
from product.models import Product


class NewMembersV2TestCase(BaseTestCase):
    def setUp(self):
        super().setUp()
        self.product = self.create_product(contentDelivery='cakto')
        self.product.contentDeliveries.add(self.product.contentDelivery)
        self.order = self.create_order(product=self.product)
        self.members_base_url = settings.CAKTO_MEMBERS_V2_BASE_URL
        self.members_webhook_url = urljoin(self.members_base_url, '/api/webhooks/cakto/purchase')
        self.members_webhook_secret = settings.CAKTO_MEMBERS_V2_API_KEY or '1340098d-340d-488a-af83-f80e0eaaa773'

    def test_handle_purchase_course_access_calls_send_purchase_webhook_to_members_v2(self):
        with mock.patch(
            'members.sdk.members_v2_webhook.send_purchase_webhook_to_members_v2.delay',
            mock.Mock()
        ) as send_purchase_webhook_to_members_v2_mock:

            handle_purchase_course_access(order=self.order, user=self.user, payment=self.order.paymentMethod)

            assert self.order.product.contentDelivery == 'cakto'
            assert self.order.product.contentDeliveries.filter(type='cakto').exists()
            send_purchase_webhook_to_members_v2_mock.assert_called_once_with(
                order=self.order,
                payment=self.order.paymentMethod
            )

    def test_create_product_request_with_contentDelivery_cakto_must_calls_sync_producer_to_members_v2(self):
        with mock.patch(
            'members.sdk.members_v2_producer.sync_producer_to_members_v2.delay',
            mock.Mock()
        ) as sync_producer_to_members_v2_mock:

            response = self.client.post(
                '/api/products/',
                data={
                    'name': 'Test Product New Members V2',
                    'contentDelivery': 'cakto',
                    'contentDeliveries': ['cakto', ],
                    'description': 'A new description' * 10,
                    'price': 30.0,
                },
                headers=self.build_user_auth_headers(),
                format='json'
            )
            product_created = Product.objects.get(name='Test Product New Members V2')
            assert response.status_code == status.HTTP_201_CREATED
            assert Product.objects.get(name='Test Product New Members V2').contentDelivery == 'cakto'
            sync_producer_to_members_v2_mock.assert_called_once_with(user=self.user, product=product_created)

    def test_sync_producer_to_members_v2_must_return_none_if_product_content_delivery_is_not_cakto(self):
        product = self.create_product(contentDelivery='external')
        response = sync_producer_to_members_v2(user=self.user, product=product)
        assert response is None

    def test_sync_producer_to_members_v2_calls_update_producer_if_existing_producer(self):
        get_producer_by_main_app_id_mock = mock.patch(
            'members.sdk.members_v2_producer.MembersV2ProducerService.get_producer_by_main_app_id',
            mock.Mock(return_value={'id': '123'})
        ).start()

        update_producer_mock = mock.patch(
            'members.sdk.members_v2_producer.MembersV2ProducerService.update_producer',
            mock.Mock(return_value=True)
        ).start()

        producer_cakto_members_id = sync_producer_to_members_v2(
            user=self.user, product=self.product
        )

        assert producer_cakto_members_id == '123'

        get_producer_by_main_app_id_mock.assert_called_once_with(
            str(self.user.id)
        )

        update_producer_mock.assert_called_once_with(
            self.user,
            '123'
        )
