from notifications.models import Notification
from cakto.tests.base import BaseTestCase
from members.sdk.base import ProductDeliveryConfigError, set_error_state_to_productDelivery
from product.enums import ProductDeliveryStatus
from product.models import ContentDelivery, ProductDelivery


class BaseMembersSDKTests(BaseTestCase):
    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()

        cls.product = cls.create_product(user=cls.user)

        cls.telegram_delivery, _ = ContentDelivery.objects.get_or_create(
            type='telegram',
            name='Telegram',
        )

        cls.productDelivery = ProductDelivery.objects.create(
            product=cls.product,
            contentDelivery=cls.telegram_delivery,
            name='Telegram',
            status='active',
        )

    def test_set_error_state_to_productDelivery_change_status_to_with_error(self):
        self.productDelivery.status = 'active'
        self.productDelivery.save()

        # Call
        set_error_state_to_productDelivery(
            self.productDelivery,
            ProductDeliveryConfigError(),
        )

        self.assertEqual(
            self.productDelivery.status,
            ProductDeliveryStatus.WITH_ERROR.id,
        )

    def test_set_error_state_to_productDelivery_add_error_detail_in_fields(self):
        error_message = 'This is an error message'

        # Call
        set_error_state_to_productDelivery(
            self.productDelivery,
            ProductDeliveryConfigError(error_message),
        )

        self.assertIn('error_detail', self.productDelivery.fields)
        self.assertEqual(
            self.productDelivery.fields['error_detail'],
            error_message,
        )

    def test_set_error_state_to_productDelivery_create_notification(self):
        set_error_state_to_productDelivery(
            self.productDelivery,
            ProductDeliveryConfigError('Configuration error'),
        )

        product_name = self.product.name[:15] + '...' if len(self.product.name) > 15 else self.product.name
        expected_description = (
            f'A integração do Telegram de "{product_name}" '
            'foi desativada devido a um erro de configuração. '
            'Por favor, verifique as configurações do produto.'
        )
        expected_title = 'Integração Telegram Desativada'

        notification: Notification = Notification.objects.first()  # type: ignore

        self.assertIsNotNone(notification)

        self.assertEqual(notification.user, self.product.user)
        self.assertEqual(notification.title, expected_title)
        self.assertEqual(notification.description, expected_description)
