from unittest import mock

from django.utils import timezone

from cakto.tests.base import BaseTestCase
from members.sdk.discord import remove_discord_access_from_order, remove_discord_expired_accesses
from product.enums import ProductDeliveryStatus
from product.models import ContentDelivery, DeliveryAccess, ProductDelivery


class DiscordMembersSDKTests(BaseTestCase):
    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()

        cls.create_paymentMethods()

        cls.product = cls.create_product(user=cls.user)

        cls.customer_user = cls.create_user()

        cls.order = cls.create_order(
            product=cls.product,
            customer=cls.create_customer(email=cls.customer_user.email),
        )

        cls.discord_contentDelivery, _ = ContentDelivery.objects.get_or_create(
            type='discord',
            name='Discord'
        )

        cls.productDelivery = ProductDelivery.objects.create(
            product=cls.product,
            contentDelivery=cls.discord_contentDelivery,
            name='Discord Product Delivery',
            status='active',
            fields={
                'userId': '100',
                'guildId': '100',
                'roleId': '100',
            }
        )

        cls.deliveryAccess = DeliveryAccess.objects.create(
            productDelivery=cls.productDelivery,
            user=cls.customer_user,
            status='active',
            order=cls.order,
            fields={
                'userId': '1',
            }
        )

        # Create ProductDeliveries with different statuses
        other_statuses = ProductDeliveryStatus.as_id_list()
        other_statuses.remove(ProductDeliveryStatus.ACTIVE.id)
        for status in other_statuses:
            prod_delivery = ProductDelivery.objects.create(
                product=cls.product,
                contentDelivery=cls.discord_contentDelivery,
                name=f'Discord Delivery with status {status}',
                status=status,
            )
            # Create more delivery accesses that is linked to the
            # product delivery with different statuses
            DeliveryAccess.objects.create(
                productDelivery=prod_delivery,
                user=cls.customer_user,
                status='active',
                order=cls.order,
                fields={
                    'userId': '9',
                }
            )

    def test_remove_discord_access_from_order_filters_only_active_discord_productDeliveries(self):
        remove_guild_role_to_user_mock = mock.patch(
            'members.sdk.discord.Discord.remove_guild_role_to_user',
        ).start()

        mock.patch('django.db.close_old_connections').start()

        remove_discord_access_from_order(self.order)

        remove_guild_role_to_user_mock.assert_called_once_with(
            guild_id=self.productDelivery.fields['guildId'],
            role_id=self.productDelivery.fields['roleId'],
            user_id=self.deliveryAccess.fields['userId'],
        )

    def test_remove_discord_expired_accesses_filters_only_active_discord_productDeliveries(self):
        remove_discord_access_job_mock = mock.patch(
            'members.sdk.discord.remove_discord_access_job.delay',
        ).start()

        # Update all DeliveryAccesses to be expired and active
        DeliveryAccess.objects.update(
            expiresAt=timezone.now() - timezone.timedelta(days=1),
            status='active',
        )

        mock.patch('django.db.close_old_connections').start()

        remove_discord_expired_accesses()

        remove_discord_access_job_mock.assert_called_once_with(
            self.deliveryAccess,
            job_id=f'remove_discord_access_job_{self.deliveryAccess.id}',
        )
