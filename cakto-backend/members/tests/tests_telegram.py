from unittest import mock

import responses
from django.test import override_settings
from django.utils import timezone

from cakto.tests.base import BaseTestCase
from members.sdk.base import ProductDeliveryConfigError
from members.sdk.telegram import (Telegram, remove_telegram_access_from_order, remove_telegram_access_job,
                                  remove_telegram_expired_accesses)
from product.enums import ProductDeliveryStatus
from product.models import ContentDelivery, DeliveryAccess, ProductDelivery

TELEGRAM_BOT_TOKEN_MOCK = '123456789:ABCDEFghijklmnopqrstuvwxyz1234567890'

@override_settings(
    TELEGRAM_BOT_TOKEN=TELEGRAM_BOT_TOKEN_MOCK,
)
class TelegramMembersSDKTests(BaseTestCase):
    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()

        cls.create_paymentMethods()

        cls.product = cls.create_product(user=cls.user)

        cls.customer_user = cls.create_user()

        cls.order = cls.create_order(
            product=cls.product,
            customer=cls.create_customer(email=cls.customer_user.email),
        )

        cls.telegram_delivery, _ = ContentDelivery.objects.get_or_create(
            type='telegram',
            name='Telegram',
        )

        cls.productDelivery = ProductDelivery.objects.create(
            product=cls.product,
            contentDelivery=cls.telegram_delivery,
            name='Telegram Product Delivery',
            status='active',
            fields={'chat_id': '100'}
        )

        cls.deliveryAccess = DeliveryAccess.objects.create(
            productDelivery=cls.productDelivery,
            user=cls.customer_user,
            status='active',
            order=cls.order,
            fields={
                'tl_user_id': '999',
                'invite_link': 'https://t.me/joinchat/AAAAAAA1234567890',
            }
        )

        # Create ProductDeliveries with different statuses
        other_statuses = ProductDeliveryStatus.as_id_list()
        other_statuses.remove(ProductDeliveryStatus.ACTIVE.id)
        for status in other_statuses:
            prod_delivery = ProductDelivery.objects.create(
                product=cls.product,
                contentDelivery=cls.telegram_delivery,
                name=f'Telegram Delivery with status {status}',
                status=status,
            )
            # Create more delivery accesses that is linked to the
            # product delivery with different statuses
            DeliveryAccess.objects.create(
                productDelivery=prod_delivery,
                user=cls.customer_user,
                status='active',
                order=cls.order,
                fields={
                    'userId': '9',
                }
            )

    def test_remove_telegram_access_from_order_filters_only_active_telegram_productDeliveries(self):
        remove_user_from_group_mock = mock.patch(
            'members.sdk.telegram.Telegram.remove_user_from_group',
        ).start()

        remove_telegram_access_from_order(self.order)

        remove_user_from_group_mock.assert_called_once_with(
            self.productDelivery.fields['chat_id'],
            self.deliveryAccess.fields['tl_user_id'],
            self.deliveryAccess.fields['invite_link'],
        )

    def test_remove_telegram_access_from_order_apply_error_status_when_expected_errors_is_raised(self):
        remove_user_from_group_mock = mock.patch(
            'members.sdk.telegram.Telegram.remove_user_from_group',
        ).start()
        remove_user_from_group_mock.side_effect = ProductDeliveryConfigError('Test error')

        remove_telegram_access_from_order(self.order)

        self.productDelivery.refresh_from_db()
        self.assertEqual(self.productDelivery.status, ProductDeliveryStatus.WITH_ERROR.id)
        self.assertIn('error_detail', self.productDelivery.fields)
        self.assertEqual(self.productDelivery.fields['error_detail'], 'Test error')

        self.deliveryAccess.refresh_from_db()
        self.assertEqual(
            self.deliveryAccess.status,
            'active',
            'DeliveryAccess should not be updated if the integration fail',
        )

    def test_remove_telegram_expired_accesses_filters_only_active_telegram_productDeliveries(self):
        remove_telegram_access_job_mock = mock.patch(
            'members.sdk.telegram.remove_telegram_access_job.delay',
        ).start()

        # Update all DeliveryAccesses to be expired and active
        DeliveryAccess.objects.update(
            expiresAt=timezone.now() - timezone.timedelta(days=1),
            status='active',
        )

        mock.patch('django.db.close_old_connections').start()

        remove_telegram_expired_accesses()

        remove_telegram_access_job_mock.assert_called_once_with(
            self.deliveryAccess,
            job_id=f'remove_telegram_access_job_{self.deliveryAccess.id}',
        )

    def test_remove_telegram_access_job_apply_error_status_when_expected_errors_is_raised(self):
        remove_user_from_group_mock = mock.patch(
            'members.sdk.telegram.Telegram.remove_user_from_group',
        ).start()
        remove_user_from_group_mock.side_effect = ProductDeliveryConfigError('Test error')

        remove_telegram_access_job(self.deliveryAccess, status='expired')

        self.productDelivery.refresh_from_db()
        self.deliveryAccess.refresh_from_db()

        self.assertEqual(self.productDelivery.status, ProductDeliveryStatus.WITH_ERROR.id)
        self.assertIn('error_detail', self.productDelivery.fields)
        self.assertEqual(
            self.productDelivery.fields['error_detail'],
            'Test error',
        )

        self.assertEqual(
            self.deliveryAccess.status,
            'active',
            'DeliveryAccess should not be updated if the integration fail',
        )

    @responses.activate
    def test__post_raises_ProductDeliveryConfigError(self):
        disable_cases = [
            'Bad Request: chat not found',
            'Bad Request: not enough rights to restrict/unrestrict chat member',
        ]

        for case in disable_cases:
            with self.subTest(case=case):
                responses.add(
                    responses.POST,
                    f'https://api.telegram.org/bot{TELEGRAM_BOT_TOKEN_MOCK}/kickChatMember',
                    json={'ok': False, 'error_code': 400, 'description': case},
                    status=400,
                )

                telegram = Telegram()
                with self.assertRaises(ProductDeliveryConfigError):
                    telegram._post('kickChatMember')

    @responses.activate
    def test__post_ignore_cases(self):
        ignore_cases = [
            'Bad Request: user is an administrator of the chat',
            'Bad Request: can\'t remove chat owner',
            'Bad Request: USER_NOT_PARTICIPANT',
        ]

        for case in ignore_cases:
            with self.subTest(case=case):
                responses.add(
                    responses.POST,
                    f'https://api.telegram.org/bot{TELEGRAM_BOT_TOKEN_MOCK}/kickChatMember',
                    json={'ok': False, 'error_code': 400, 'description': case},
                    status=400,
                )

                telegram = Telegram()
                response = telegram._post('kickChatMember')
                self.assertIsNone(response, f'Expected None for case: {case}')

    @responses.activate
    def test__post_raises_error_when_unexpected_error_response(self):
        responses.add(
            responses.POST,
            f'https://api.telegram.org/bot{TELEGRAM_BOT_TOKEN_MOCK}/kickChatMember',
            json={'ok': False, 'error_code': 400, 'description': 'Some unexpected error'},
            status=400,
        )

        telegram = Telegram()
        with self.assertRaises(Exception) as cm:
            telegram._post('kickChatMember')

        self.assertIn('status_code: 400', str(cm.exception))
        self.assertIn('Some unexpected error', str(cm.exception))
        self.assertIn('kickChatMember', str(cm.exception))

    @responses.activate
    def test__post_sucess(self):
        json_response = {'ok': True, 'result': 'success'}

        responses.add(
            responses.POST,
            f'https://api.telegram.org/bot{TELEGRAM_BOT_TOKEN_MOCK}/kickChatMember',
            json=json_response,
            status=200,
        )

        telegram = Telegram()
        result = telegram._post('kickChatMember')

        self.assertEqual(
            result,
            json_response,
            'Expected the response to match the mocked response',
        )
