# cakto-backend
Có<PERSON> fonte da aplicação cakto-backend da empresa cakto

## Especificações
Esta aplicação foi escrita em `Python 3.11` com framework `Django 4.2.5`


## Desenvolvimento via devcontainer
Esta aplicação conta com a configuração [devcontainer](https://code.visualstudio.com/docs/devcontainers/containers) para reduzir o tempo de configuração do ambiente de desenvolvimento das pessoas desenvolvedoras.

Isso significa que é possível criar e configurar, de maneira simples e rápida, todo o ambiente de desenvolvimento e, também, as dependências da aplicação, tais como banco de dados, filas, etc.

### Pré-requisitos
Para utilização do `devcontainer`, os pré-requisitos abaixo são necessários na máquina da pessoa desenvolvedora:
- [Docker Engine](https://docs.docker.com/engine/install/)
- [Docker compose](https://docs.docker.com/compose/install/)
- [VS Code](https://code.visualstudio.com/download)
- [Remote development extensions](https://marketplace.visualstudio.com/items?itemName=ms-vscode-remote.vscode-remote-extensionpack) instalada no VSCode

### Criar e configurar o ambiente de desenvolvimento
Para criar e configurar automaticamente o ambiente de desenvolvimento, siga os passos abaixo:

1. Abra o `VSCode` e abra a pasta raíz desta aplicação [/](/);
1. Pressione as teclas `CTRL+P` ou `F1`;
1. No menu que irá aparecer, escolha a opção `Dev Containers: Rebuild and Reopen in Container`;
1. Aguarde alguns minutos até que o VSCode gere todas as dependências da aplicação e realize a migration no banco de dados que acaba sde ser criado.


#### Criar usuário de testes dentro do devcontainer
Para criar um usuário de testes dentro do `devcontainer`, siga os passos abaixo **após realizar todos os passos da seção [criar e configurar o ambiente de desenvolvimento](#criar-e-configurar-o-ambiente-de-desenvolvimento)**:
1. Abra o terminal do VSCode, dentro do `devcontainer` e acesse a pasta raíz do repositório [/](/);
1. Digite o comando `task create-superuser`
1. Será criado um usuário, no banco de dados local, com as informações abaixo:
    - username: `teste`
    - email: `<EMAIL>`
    - password: `abc123`


#### Iniciando a API principal dentro do devcontainer
Para iniciar a API principal (Django) dentro do `devcontainer`, siga os passos abaixo **após realizar todos os passos da seção [criar e configurar o ambiente de desenvolvimento](#criar-e-configurar-o-ambiente-de-desenvolvimento)**:
1. Abra o terminal do VSCode, dentro do `devcontainer` e acesse a pasta raíz do repositório [/](/);
1. Digite o comando `task run-dev`.

> A aplicação poderá ser acessada, localmente, através do endereço [http://localhost:8000/admin](http://localhost:8000/admin)


#### Iniciando a API de checkout dentro do devcontainer
Para iniciar a API de checkout (FastAPI) dentro do `devcontainer`, siga os passos abaixo **após realizar todos os passos da seção [criar e configurar o ambiente de desenvolvimento](#criar-e-configurar-o-ambiente-de-desenvolvimento)**:
1. Abra o terminal do VSCode, dentro do `devcontainer` e acesse a pasta raíz do repositório [/](/);
1. Digite o comando `run-dev-checkout`.

> A aplicação poderá ser acessada, localmente, através do endereço [http://localhost:8080/](http://localhost:8080/)


#### Testando a aplicação dentro do devcontainer
Para realizar os testes da aplicação dentro do `devcontainer`, siga os passos abaixo **após realizar todos os passos da seção [criar e configurar o ambiente de desenvolvimento](#criar-e-configurar-o-ambiente-de-desenvolvimento)**:
1. Abra o terminal do VSCode, dentro do `devcontainer`;
1. Digite o comando `task test`.

> O comando acima irá executar todos os testes disponíveis via `pytest` e não irá excluir o banco de dados de teste (está usando a flag `--reuse-db`)


##### Passando parâmetros para os testes
Caso queira passar parâmetros adicionais para os testes, basta executar o comando anterior adicionando  `--` e os parâmetros adicionais.
Exemplo:
```bash
task test -- -x  # O parâmetro '-x' faz com que o pytest pare ao encontrar a primeira falha nos testes
```

##### Executando testes específicos
Caso queira executar apenas testes de um determinado arquivo, basta executar o comando de testes passando o caminho do arquivo a ser testado.
Exemplo:
```bash
task test -- caminho/do/arquivo/a/ser/testado.py
```

##### Executando um teste limpo
Para recriar o banco de dados local de teste e executar todos os testes, execute o comando abaixo.
```bash
task test-clean
```

#### Inicializando os workers dentro do devcontainer
Para iniciar os workers da aplicação dentro do `devcontainer`, siga os passos abaixo **após realizar todos os passos da seção [criar e configurar o ambiente de desenvolvimento](#criar-e-configurar-o-ambiente-de-desenvolvimento)**:
1. Abra o terminal do VSCode, dentro do `devcontainer` e vá para a pasta raiz do repositório [/](/);
1. Digite o comando `task run-rqworker` para iniciar o worker **default**;
1. Digite o comando `task run-cakto_pixel-rqworker` para iniciar o worker **cakto_pixel**;
1. Digite o comando `task run-metrics-rqworker` para iniciar o worker **metrics**;
1. Digite o comando `task run-runapscheduler` para iniciar o **apscheduler**;

##### Iniciando todos os workers:
Para iniciar todos os workers dentro do devcontainer, execute o comando abaixo:
```bash
task run-all-workers
```

##### Iniciando todas as aplicações do cakto-backend:
Para iniciar a **API principal**, a **API de checkout** e **todos os workers** dentro do devcontainer, execute o comando abaixo:
```bash
task run-all
```

### Dependências criadas no devcontainer

Ao iniciar o `devcontainer`, serão criadas as dependências listadas nas subseções abaixo.

#### PostgreSQL
Banco de dados `PostgreSQL`, sem nenhum objeto customizado.

Dados de acesso:
- Usuário: `caktouser`
- Senha: `caktopassword`
- Banco: `caktodb`
- Porta: `5432`
- Host: `localhost`

#### Redis
Banco de dados `Redis`, para as filas da aplicação, sem nenhum objeto customizado.

Dados de acesso:
- Porta: `6379`
- Host: `localhost`


## Desenvolvimento via docker-compose
Alternativamente, é possível executar a aplicação em modo de desenvolvimento e criar automaticamente suas as dependências, utilizando o [docker compose](https://docs.docker.com/compose/install/)

### Pré-requisitos docker-compose
Para utilização do `docker-compose`, os pré-requisitos abaixo são necessários na máquina da pessoa desenvolvedora:
- [Docker Engine](https://docs.docker.com/engine/install/)
- [Docker compose](https://docs.docker.com/compose/install/)

### Executar a aplicação e suas dependências via docker-compose
Para executar a aplicação em modo de desenvolvimento e criar automaticamente suas as dependências, siga os passos abaixo:

1. Abra um terminal e acesse a pasta raíz desta aplicação [/](/);
1. Digite o comando `docker compose up --build`;
1. Aguarde até que os containeres `cakto-backend-local` e `cakto-checkout-local` estejam sendo executados e exibam uma mensagem informando que estão acessíveis via endereço http.

<br/>

> A API principal poderá ser acessada, localmente, através do endereço [http://localhost:8000/admin](http://localhost:8000/admin)

<br/>

> A API de checkout  poderá ser acessada, localmente, através do endereço [http://localhost:8080/docs](http://localhost:8080/docs)

### Dependências criadas via docker-compose
Além das dependências listadas na seção [Dependências criadas no devcontainer](#dependências-criadas-no-devcontainer), as dependências listadas abaixo também serão criadas automaticamente via docker compose.

#### Worker metrics
Worker `cakto-rqworker-cakto-metrics` para processamento de métricas.

#### Worker pixel_events
Worker `cakto-rqworker-pixel_events` para processar eventos de pixel.

#### Worker default
Worker `cakto-rqworker` para processar dados gerais.

#### Scheduler geral
Scheduler `cakto-runapscheduler` para dados gerais da aplicação.