from typing import List, Optional

from pydantic import BaseModel, Field


class AddressModel(BaseModel):
    postal_code: str = Field(..., alias="postalCode")
    street: str
    number: str
    address_complement: Optional[str] = Field(None, alias="addressComplement")
    neighborhood: str
    city: str
    state: str

class OwnerModel(BaseModel):
    owner_type: str = Field(..., alias="ownerType")
    document_number: str = Field(..., alias="documentNumber")
    full_name: str = Field(..., alias="fullName")
    phone_number: str = Field(..., alias="phoneNumber")
    email: str
    mother_name: str = <PERSON>(..., alias="motherName")
    social_name: str = <PERSON>(..., alias="socialName")
    birth_date: str = Field(..., alias="birthDate")
    address: AddressModel
    is_politically_exposed_person: bool = Field(..., alias="isPoliticallyExposedPerson")

class PartnerParameterModel(BaseModel):
    key: str
    value: str

class PartnerModel(BaseModel):
    partner_name: str = Field(..., alias="partnerName")
    parameters: List[PartnerParameterModel]

class AccountCreateModel(BaseModel):
    client_code: str = Field(..., alias="clientCode")
    contact_number: str = Field(..., alias="contactNumber")
    document_number: str = Field(..., alias="documentNumber")
    business_email: str = Field(..., alias="businessEmail")
    business_name: str = Field(..., alias="businessName")
    trading_name: str = Field(..., alias="tradingName")
    company_type: str = Field(..., alias="companyType")
    owner: List[OwnerModel]
    business_address: AddressModel = Field(..., alias="businessAddress")
    onboarding_type: str = Field(..., alias="onboardingType")
    partner: Optional[PartnerModel]

    class Config:
        from_attributes = True
        validate_by_name = True
