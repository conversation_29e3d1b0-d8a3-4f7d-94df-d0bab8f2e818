from ninja.security import <PERSON>tt<PERSON><PERSON><PERSON><PERSON>
from rest_framework.exceptions import AuthenticationFailed
from rest_framework_simplejwt.authentication import JWTAuthentication


class BearerAuth(HttpBearer):
    def authenticate(self, request, token):
        user_auth = JWTAuthentication()
        try:
            user = user_auth.authenticate(request)
        except Exception:
            raise AuthenticationFailed("Invalid or expired token")

        return user
