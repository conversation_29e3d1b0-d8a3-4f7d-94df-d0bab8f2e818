import uuid

from django.db import models
from django.utils import timezone


def generate_random_id():
    return str(uuid.uuid4())

class ProposalStatus(models.TextChoices):
    CREATED = 'Created', 'Proposta criada'
    PENDING = 'Pending', 'Pendente Backgroundcheck'
    PENDING_DOCUMENTSCOPY = 'Pending_Documentscopy', 'Pendente Documentoscopia'
    PROCESSING_DOCUMENTSCOPY = 'Processing_Documentscopy', 'Processando Documentoscopia'
    REPROVED = 'Reproved', 'Proposta reprovada'
    RESOURCE_ERROR = 'Resource_error', 'Erro ao criar conta no BaaS'
    RESOURCE_CREATED = 'Resource_Created', 'Conta criada no BaaS'

class BaasAccount(models.Model):
    id = models.CharField(max_length=255, primary_key=True, unique=True, default=generate_random_id)
    client_code = models.CharField(max_length=255)
    provider = models.Char<PERSON>ield(max_length=50)
    document_number = models.Char<PERSON>ield(max_length=20)
    business_name = models.Char<PERSON>ield(max_length=255)
    status = models.CharField(max_length=30, choices=ProposalStatus.choices, default=ProposalStatus.PENDING)
    created_at = models.DateTimeField(default=timezone.now, editable=False)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Conta Backend"
        verbose_name_plural = "Contas Backend"
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.business_name} ({self.document_number}) - {self.status}"
