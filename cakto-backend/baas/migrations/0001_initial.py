# Generated by Django 4.2.5 on 2025-07-31 16:29

import baas.models
from django.db import migrations, models
import django.utils.timezone


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='BaasAccount',
            fields=[
                ('id', models.CharField(default=baas.models.generate_random_id, max_length=255, primary_key=True, serialize=False, unique=True)),
                ('client_code', models.<PERSON>r<PERSON><PERSON>(max_length=255)),
                ('provider', models.Char<PERSON><PERSON>(max_length=50)),
                ('document_number', models.Char<PERSON><PERSON>(max_length=20)),
                ('business_name', models.Char<PERSON>ield(max_length=255)),
                ('status', models.Char<PERSON>ield(choices=[('Created', 'Proposta criada'), ('Pending', 'Pendente Backgroundcheck'), ('Pending_Documentscopy', 'Pendente Documentoscopia'), ('Processing_Documentscopy', 'Processando Documentoscopia'), ('Reproved', 'Proposta reprovada'), ('Resource_error', 'Erro ao criar conta no BaaS'), ('Resource_Created', 'Conta criada no BaaS')], default='Pending', max_length=30)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now, editable=False)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Conta Backend',
                'verbose_name_plural': 'Contas Backend',
                'ordering': ['-created_at'],
            },
        ),
    ]
