from django.http import JsonResponse
from ninja import NinjaAPI
from requests.exceptions import RequestException

from baas.auth import <PERSON><PERSON><PERSON><PERSON>
from baas.schema import AccountCreateModel
from gateway.sdk.splitpay import SplitPay

api = NinjaAPI()
auth = BearerAuth()

@api.post('/create-account', auth=auth)
def create_account(request, data: AccountCreateModel):
    split = SplitPay()
    try:
        response = split.create_account_baas(**data.model_dump())
        resp_data = response.json()
        return {
            "proposalId": resp_data.get("proposalId"),
            "status": resp_data.get("status", "PROCESSING"),
        }

    except RequestException as e:
        return JsonResponse({
            "error": "Error communicating with <PERSON>",
            "detail": str(e)
        }, status=500)
