from unittest.mock import patch

from django.test import TestCase

from gateway.sdk.splitpay import SplitPay


class TestIntegration(TestCase):
    @patch("gateway.sdk.base.requests.Session.post")
    def test_api(self, mock_post):
        mock_post.return_value.status_code = 201
        mock_post.return_value.json.return_value = {"proposalId": "12345", "status": "PROCESSING"}

        split = SplitPay()

        payload = {
            "clientCode": "",
            "contactNumber": "+5511912345678",
            "documentNumber": "87649940000194",
            "businessEmail": "<EMAIL>",
            "businessName": "Celcoin",
            "tradingName": "Celcoin Instituição de Pagamento",
            "companyType": "PJ",
            "owner": [
                {
                    "ownerType": "SOCIO",
                    "documentNumber": "***********",
                    "fullName": "Nome Teste",
                    "phoneNumber": "+5511912345128",
                    "email": "<EMAIL>",
                    "motherName": "Nome Mae",
                    "socialName": "Nome",
                    "birthDate": "02-02-1990",
                    "address": {
                        "postalCode": "06455030",
                        "street": "Alameda Xingu",
                        "number": "50",
                        "addressComplement": "",
                        "neighborhood": "Alphaville Industrial",
                        "city": "Barueri",
                        "state": "SP"
                    },
                    "isPoliticallyExposedPerson": False
                }
            ],
            "businessAddress": {
                "postalCode": "06455030",
                "street": "Alamed Xingu",
                "number": "350",
                "addressComplement": "",
                "neighborhood": "Alphaville Industrial",
                "city": "Barueri",
                "state": "SP"
            },
            "onboardingType": "BAAS",
            "partner": {
                "partnerName": "CAF",
                "parameters": [
                    {
                        "key": "transactionId",
                        "value": "nfjdnfjdfnjdnfj-ndndknfdfdn"
                    }
                ]
            }
        }

        response = split.create_account_baas(**payload)
        self.assertEqual(response.status_code, 201)
        self.assertEqual(response.json()['status'], "PROCESSING")
