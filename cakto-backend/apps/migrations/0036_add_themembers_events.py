# Generated by Django 4.2.5 on 2025-06-25 11:55

from django.db import migrations


def add_themembers_events(apps, schema_editor):
    Event = apps.get_model('apps', 'Event')
    Platform = apps.get_model('apps', 'Platform')

    platform, _ = Platform.objects.get_or_create(
        type='themembers',
        defaults={
            'name': 'The Members',
            'type': 'themembers',
        }
    )

    events_data = [
        {"custom_id": "purchase_approved", "name": "Compra aprovada"},
        {"custom_id": "refund", "name": "<PERSON>embol<PERSON>"},
        {"custom_id": "chargeback", "name": "Chargeback"},
        {"custom_id": "subscription_created", "name": "Assinatura criada"},
        {"custom_id": "subscription_canceled", "name": "Assinatura cancelada"},
        {"custom_id": "subscription_renewed", "name": "Assinatura renovada"},
    ]

    for event_data in events_data:
        event = Event.objects.filter(custom_id=event_data["custom_id"], platform=platform).first()
        if event is None:
            event, _ = Event.objects.get_or_create(custom_id=event_data["custom_id"], defaults={"name": event_data["name"]})
            event.platform.add(platform)


class Migration(migrations.Migration):

    dependencies = [
        ('apps', '0035_alter_eventhistory_job'),
    ]

    operations = [
        migrations.RunPython(add_themembers_events, reverse_code=migrations.RunPython.noop),
    ]
