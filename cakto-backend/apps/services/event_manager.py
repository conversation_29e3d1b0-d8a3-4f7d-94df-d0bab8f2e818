import pprint
from typing import Any, Dict, Literal, Type, Unpack, overload

from django_rq import enqueue, job
from rq import Retry

from apps.models import App, Event, EventHistory
from apps.services.app_factory import AppFactory
from apps.services.types import (CheckoutAbandonmentEventKwargs, DispatchPixelInitCheckoutKwargs,
                                 DispatchPixelPaymentEventKwargs, InitiateCheckoutEventKwargs, PaymentEventKwargs,
                                 PaymentEventTypes)
from product.models import Product
from product.pixel_strategies.base import PixelEvents
from product.pixel_strategies.facebook import FacebookPixelEvents
from product.pixel_strategies.tiktok import TikTokPixelEvents

PIXELS_STRATEGIES: dict[str, Type[PixelEvents]] = {
    'facebook_pixels': FacebookPixelEvents,
    'tiktok_pixels': TikTokPixelEvents
}


def get_product_from_kwargs(kwargs: Dict[str, Any]):
    order = kwargs.get('order')

    if order is None and 'orders' in kwargs:
        orders = kwargs['orders']
        order = next(
            (o for o in orders if getattr(o, "offer_type", "") == "main"),
            None
        ) or orders[0]

    offer = kwargs.get('offer')

    if order is not None:
        if getattr(order, "offer_type", "") == "orderbump":
            bump_rel = order.offer.bumps.first()
            if bump_rel:
                return bump_rel.product
        return order.product

    if offer is not None:
        return offer.product

    return None

def get_event(event_custom_id: str):
    return Event.objects.filter(custom_id=event_custom_id, active=True).first()

def get_apps(event: Any, product: Any):
    return App.objects.filter(
        events=event,
        platform__active=True,
        user=product.user,
        products=product,
    ).select_related('platform')

def handle_kwargs(event_custom_id: str, app: App, product: Product, **kwargs):
    kwargs['event_custom_id'] = event_custom_id
    kwargs['app_platform'] = app.platform.type
    kwargs['app'] = app
    kwargs['app_id'] = app.id
    kwargs['product'] = product
    kwargs['product_id'] = product.id

    kwargs['offer'] = kwargs.get('offer', '')

    order = kwargs.get('order', '')
    kwargs['order'] = order

    kwargs['customer'] = order.customer if order else None
    kwargs['customer_email'] = order.customer.email if order else None

    return kwargs

def process_event(event_custom_id: str, app: App, product: Product, **kwargs):
    kwargs = handle_kwargs(event_custom_id, app, product, **kwargs)

    strategy = AppFactory.get_strategy(app.platform.type, init_vars=True, **kwargs)  # type:ignore
    event_method = strategy.get_event_method(event_custom_id)

    if event_method is not None:
        job = enqueue(event_method, **kwargs, retry=Retry(max=5, interval=[5, 60, 150, 360, 1800]))
        if app.platform.type != 'active_campaign':
            EventHistory.objects.create(app=app, event_id=event_custom_id, job=job.id)

@overload
def dispatch_app_events(event_custom_id: Literal['initiate_checkout'], **kwargs: Unpack[InitiateCheckoutEventKwargs]): ...

@overload
def dispatch_app_events(event_custom_id: PaymentEventTypes, **kwargs: Unpack[PaymentEventKwargs]): ...

@overload
def dispatch_app_events(event_custom_id: Literal['checkout_abandonment'], **kwargs: Unpack[CheckoutAbandonmentEventKwargs]): ...

@job('default', retry=Retry(max=5, interval=[5, 25, 30, 360]))
def dispatch_app_events(event_custom_id: str, **kwargs):
    event = get_event(event_custom_id)
    if event is None:
        return

    product = get_product_from_kwargs(kwargs)
    if not product:
        return

    apps = get_apps(event, product)
    for app in apps:
        process_event(event_custom_id, app, product, **kwargs)

def is_orderbump_event(kwargs) -> bool:
    order = kwargs.get('order')
    if order and getattr(order, 'offer_type', None) == 'orderbump':
        return True

    offer = kwargs.get('offer')
    if offer and offer.bumps.exists():
        return True

    return False

@overload
def dispatch_pixel_events(event: str, **kwargs: Unpack[DispatchPixelInitCheckoutKwargs]): ...

@overload
def dispatch_pixel_events(event: str, **kwargs: Unpack[DispatchPixelPaymentEventKwargs]): ...

@job('pixel_events')
def dispatch_pixel_events(
    event: str,
    **kwargs
):
    product = get_product_from_kwargs(kwargs)

    # If the flag is active and it is a bump event, it does not send to the pixel.
    if is_orderbump_event(kwargs):
        order_bump = kwargs.get('order')
        if not order_bump and 'orders' in kwargs:
            order_bump = next(
                (o for o in kwargs['orders'] if getattr(o, 'offer_type', '') == 'orderbump'),
                None
            )
        if order_bump and getattr(order_bump, 'payment', None):
            main_order = order_bump.payment.orders.filter(offer_type='main').first()
            if main_order and getattr(main_order.product, 'disable_orderbump_pixel_events', False):
                return

    if product and product.disable_orderbump_pixel_events and is_orderbump_event(kwargs):
        return

    tracking_pixels = get_tracking_pixels(kwargs)

    jobs = []

    for field, strategy in PIXELS_STRATEGIES.items():
        # Get all pixels for each type, e.g. facebook_pixels, google_pixels, etc
        pixels = getattr(tracking_pixels, field).all()
        for pixel in pixels:
            job = process_pixel_event.delay(
                event=event,
                tracking_pixels=tracking_pixels,
                kwargs=kwargs,
                StrategyClass=strategy,
                pixel=pixel
            )
            jobs.append(job)

    res = {
        'event': event,
        'tracking_pixel_id': tracking_pixels.pk,
        'num_job_scheduled': len(jobs),
        'job_ids': [job.id for job in jobs],
    }

    return pprint.pformat(res, sort_dicts=False, width=200)

@job('pixel_events', retry=Retry(max=5, interval=60))
def process_pixel_event(event, tracking_pixels, kwargs, StrategyClass, pixel):
    if pixel:
        pixel.refresh_from_db()
    if tracking_pixels:
        tracking_pixels.refresh_from_db()

    kwargs['pixel'] = pixel
    kwargs['tracking_pixels'] = tracking_pixels

    strategy = StrategyClass(**kwargs)

    if not strategy.check_prerequisites():
        job_response = {
            'event_sent': False,
            'reason': 'Prerequisites not met',
            'pixel_id': pixel.pk,
            'tracking_pixel_id': tracking_pixels.pk
        }
        return pprint.pformat(job_response, sort_dicts=False, width=200)

    event_method = getattr(strategy, event, None)

    if event_method:
        response = event_method()
        job_response = {
            'product_name': tracking_pixels.product.name[:30],
            'product': tracking_pixels.product.pk,
            'pixel_id': pixel.pk,
            'tracking_pixel_id': tracking_pixels.pk,
            'method_response': response
        }
        return pprint.pformat(job_response, sort_dicts=False, width=200)

def get_tracking_pixels(kwargs):
    tracking_pixels = kwargs.get('tracking_pixels')

    # if orders is present, it means that is a payment event such as purchase_approved
    # otherwise, it is a initiate_checkout event wich contains only the offer in kwargs
    orders = kwargs.get('orders', [])
    order = next((order for order in orders if order.offer_type == 'main'), None) if orders else kwargs.get('order')
    offer = kwargs.get('offer')

    # try to get the affiliate tracking pixels
    if getattr(order, 'affiliate', None):
        tracking_pixels = order.product.pixels.filter(affiliate=order.affiliate).first()  # type:ignore

    # if no affiliate tracking pixels are found, get the product's default tracking pixels
    if not tracking_pixels and order:
        tracking_pixels = order.product.pixels.filter(affiliate__isnull=True).first()  # type:ignore
    if not tracking_pixels and offer:
        tracking_pixels = offer.product.pixels.filter(affiliate__isnull=True).first()  # type:ignore

    if not tracking_pixels:
        raise Exception('No tracking pixels found')

    return tracking_pixels
