from typing import Iterable, Literal, NotRequired, TypedDict

from checkout.models import CheckoutAbandonment
from gateway.models import Order, Payment
from product.models import Offer, TrackingPixels


class PaymentEventKwargs(TypedDict):
    order: Order
    payment: Payment

class CheckoutAbandonmentEventKwargs(TypedDict):
    offer: Offer
    checkout_abandonment: CheckoutAbandonment

class InitiateCheckoutEventKwargs(TypedDict):
    offer: Offer
    client_ip: str
    client_user_agent: str
    pixelEventId: str
    checkoutUrl: str
    refererUrl: str
    fbc: str
    fbp: str
    email: str
    phone: str
    name: str

class DispatchPixelInitCheckoutKwargs(InitiateCheckoutEventKwargs):
    event_time: int
    tracking_pixels: NotRequired[TrackingPixels]

class DispatchPixelPaymentEventKwargs(TypedDict):
    orders: Iterable[Order]
    payment: Payment
    tracking_pixels: NotRequired[TrackingPixels]


PaymentEventTypes = Literal[
    'purchase_approved',
    'purchase_refused',
    'pix_gerado',
    'boleto_gerado',
    'picpay_gerado',
    'openfinance_nubank_gerado',
    'chargeback',
    'refund',
    'subscription_created',
    'subscription_canceled',
    'subscription_renewed',
    'subscription_renewal_refused',
]
