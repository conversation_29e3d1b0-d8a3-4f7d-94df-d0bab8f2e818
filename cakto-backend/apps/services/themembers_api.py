import requests

from django.conf import settings
from rest_framework import status


class TheMembersAPI:
    def __init__(self, *, platform_token: str, base_url: str) -> None:
        self.platform_token = platform_token
        self.dev_token = settings.THEMEMBERS_DEV_TOKEN
        self.base_url = base_url.rstrip("/")
        self.session = requests.Session()
        self.session.headers.update(
            {
                "Content-Type": "application/json",
                "User-Agent": "CaktoBot/1.0",
            }
        )

    def _url(self, endpoint: str, product_id: str | None = None) -> str:
        if product_id:
            return f"{self.base_url}{endpoint}/{self.dev_token}/{self.platform_token}/{product_id}"
        return f"{self.base_url}{endpoint}/{self.dev_token}/{self.platform_token}"

    def _handle(self, resp: requests.Response):
        if not status.is_success(resp.status_code):
            raise Exception(f'Erro ao realizar requisição, status: {resp.status_code}, resposta: {resp.content.decode()}')
        try:
            return resp.json() or {}
        except ValueError:
            return resp.text or {}

    def _get(self, url: str):
        return self._handle(self.session.get(url))

    def _post(self, url: str, json: dict):
        return self._handle(self.session.post(url, json=json))

    def _put(self, url: str, json: dict):
        return self._handle(self.session.put(url, json=json))

    def _delete(self, url: str):
        return self._handle(self.session.delete(url))

    def get_user_by_reference_id(self, *, reference_id: str):
        url = (
            f"{self.base_url}/users/show-reference/"
            f"{reference_id}/{self.dev_token}/{self.platform_token}"
        )
        return self._get(url)

    def create_users(self, *, users: list[dict], product_id: str):
        url = self._url("/users/create")
        payload = {"product_id": [product_id], "users": users}
        return self._post(url, payload)

    def disable_user(self, *, email: str):
        url = self._url(f"/users/destroy-email/{email}")
        return self._delete(url)

    def create_subscription(self, *, product_id: str, exp_date: str, user_id: str | None = None, reference_id: str | None = None):
        url = self._url("/users/create/user-subscription")

        payload = {
            "product_id": product_id,
            "expiration_date": exp_date,
        }

        if user_id:
            payload["user_id"] = user_id
        elif reference_id:
            payload["reference_id"] = reference_id
        else:
            raise ValueError("É necessário informar 'user_id' ou 'reference_id'.")

        return self._post(url, payload)

    def disable_subscription(
        self,
        *,
        product_id: str,
        user_id: str | None = None,
        reference_id: str | None = None
    ):
        url = self._url("/users/update/disable-user-subscription")

        payload = {
            "product_id": product_id,
        }

        if user_id:
            payload["user_id"] = user_id
        elif reference_id:
            payload["reference_id"] = reference_id
        else:
            raise ValueError("É necessário informar 'user_id' ou 'reference_id'.")

        return self._put(url, payload)
