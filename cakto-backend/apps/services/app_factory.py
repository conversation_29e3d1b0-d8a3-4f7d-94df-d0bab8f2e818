from typing import Type

from apps.strategies import ActiveCampaign, AstronMembers, Cade<PERSON>, MemberKit, SmsFunnel, Spedy, UTMify, Voxuy, Webhook, TheMembers
from apps.strategies.base import AppStrategy


class AppFactory:
    strategies: dict[str, Type[AppStrategy]] = {
        'cademi': <PERSON><PERSON>,
        'voxuy': Voxuy,
        'webhook': Webhook,
        'smsfunnel': SmsFunnel,
        'utmify': UTMify,
        'memberkit': MemberKit,
        'astron_members': AstronMembers,
        'spedy': Spedy,
        'active_campaign': ActiveCampaign,
        'themembers': TheMembers,
    }

    @classmethod
    def get_strategy(cls, platform_type: str, init_vars=False, **kwargs) -> AppStrategy:
        strategy = cls.strategies[platform_type]()
        if init_vars:
            strategy.initialize_variables(**kwargs)
        return strategy
