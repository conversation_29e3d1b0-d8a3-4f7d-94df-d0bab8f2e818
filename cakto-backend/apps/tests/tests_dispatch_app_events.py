from unittest import mock

from apps.models import Event, Platform
from apps.services.event_manager import dispatch_app_events, process_event
from cakto.tests.base import BaseTestCase
from gateway.models import Payment


class AppTests(BaseTestCase):
    @classmethod
    def setUpTestData(cls):
        super().setUpTestData()

        cls.product = cls.create_product(user=cls.user)

        cls.app = cls.create_app(product=cls.product, platform=Platform.objects.get(type='webhook'))
        cls.app.events.set(Event.objects.all())

        cls.customer = cls.create_customer()

        cls.order = cls.create_order(product=cls.product, customer=cls.customer)
        cls.payment = mock.Mock(spec=Payment)

    @property
    def events(self):
        return [
            "boleto_gerado", "pix_gerado", "purchase_approved", "purchase_refused", "refund", "chargeback",
            "subscription_created", "subscription_canceled", "subscription_renewed", "subscription_renewal_refused",
            "checkout_abandonment", "picpay_gerado", "openfinance_nubank_gerado"
        ]

    def test_all_events_exists(self):
        self.assertEqual(Event.objects.filter(custom_id__in=self.events).count(), len(self.events))
        self.assertEqual(Event.objects.all().count(), len(self.events))

    def test_event_custom_id_is_in_event_method_call_kwargs(self):
        mock.patch('apps.services.event_manager.dispatch_app_events.delay', dispatch_app_events).start()

        with mock.patch('apps.services.event_manager.process_event') as process_event_mock:
            dispatch_app_events('boleto_gerado', order=self.order, payment=mock.ANY)

        args, kwargs = process_event_mock.call_args
        self.assertEqual(args[0], 'boleto_gerado')

    def setUp_strategy_mock(self, get_strategy_mock):
        strategy_mock = mock.Mock()
        strategy_mock.get_event_method = mock.Mock(return_value=None)
        return strategy_mock

    def test_app_is_in_AppFactory_get_strategy_kwargs(self):
        with mock.patch('apps.services.event_manager.AppFactory.get_strategy') as get_strategy_mock:
            get_strategy_mock.return_value = self.setUp_strategy_mock(get_strategy_mock)

            process_event('boleto_gerado', self.app, self.product)

        args, kwargs = get_strategy_mock.call_args
        self.assertEqual(kwargs.get('app'), self.app)

    def test_order_is_in_AppFactory_get_strategy_kwargs(self):
        with mock.patch('apps.services.event_manager.AppFactory.get_strategy') as get_strategy_mock:
            get_strategy_mock.return_value = self.setUp_strategy_mock(get_strategy_mock)

            process_event('boleto_gerado', self.app, self.product, order=self.order)

        args, kwargs = get_strategy_mock.call_args
        self.assertEqual(kwargs.get('order'), self.order)

    def test_offer_is_in_AppFactory_get_strategy_kwargs(self):
        with mock.patch('apps.services.event_manager.AppFactory.get_strategy') as get_strategy_mock:
            get_strategy_mock.return_value = self.setUp_strategy_mock(get_strategy_mock)

            process_event('boleto_gerado', self.app, self.product, offer=self.product.offers.first())

        args, kwargs = get_strategy_mock.call_args
        self.assertEqual(kwargs.get('offer'), self.product.offers.first())

    @mock.patch('apps.services.event_manager.EventHistory.objects.create')
    def test_dispatch_app_events_calls_correct_event_method(self, save_history_mock):
        event_method_mock = mock.Mock()

        with mock.patch('apps.services.app_factory.Webhook.boleto_gerado') as event_method_mock:
            with mock.patch('apps.services.event_manager.enqueue') as enqueue_mock:
                dispatch_app_events('boleto_gerado', order=self.order, payment=self.payment)

        args, kwargs = enqueue_mock.call_args
        self.assertEqual(args[0], event_method_mock)

    @mock.patch('apps.services.event_manager.EventHistory.objects.create')
    def test_only_correct_app_is_called_when_different_event(self, save_history_mock):
        app_2 = self.create_app(product=self.product, platform=Platform.objects.get(type='webhook'), token='app_2')
        app_2.events.set(Event.objects.filter(custom_id='purchase_approved'))

        with mock.patch('apps.services.event_manager.enqueue') as enqueue_mock:
            dispatch_app_events('boleto_gerado', order=self.order, payment=self.payment)

        args, kwargs = enqueue_mock.call_args
        called_app = kwargs.get('app')

        enqueue_mock.assert_called_once()
        self.assertEqual(called_app, self.app)

    @mock.patch('apps.services.event_manager.EventHistory.objects.create')
    def test_only_correct_app_is_called_when_different_product_and_same_event(self, save_history_mock):
        product_2 = self.create_product(user=self.user)
        app_2 = self.create_app(product=product_2, platform=Platform.objects.get(type='webhook'), token='app_2')
        app_2.events.set(Event.objects.filter(custom_id='boleto_gerado'))

        with mock.patch('apps.services.event_manager.enqueue') as enqueue_mock:
            dispatch_app_events('boleto_gerado', order=self.order, payment=self.payment)

        args, kwargs = enqueue_mock.call_args
        called_app = kwargs.get('app')

        enqueue_mock.assert_called_once()
        self.assertEqual(called_app, self.app)

    @mock.patch('apps.services.event_manager.EventHistory.objects.create')
    def test_only_correct_app_is_called_when_different_user_and_same_event(self, save_history_mock):
        user = self.create_user('<EMAIL>')
        app_2 = self.create_app(product=self.product, user=user, platform=Platform.objects.get(type='webhook'), token='app_2')
        app_2.events.set(Event.objects.filter(custom_id='boleto_gerado'))

        with mock.patch('apps.services.event_manager.enqueue') as enqueue_mock:
            dispatch_app_events('boleto_gerado', order=self.order, payment=self.payment)

        args, kwargs = enqueue_mock.call_args
        called_app = kwargs.get('app')

        enqueue_mock.assert_called_once()
        self.assertEqual(called_app, self.app)
