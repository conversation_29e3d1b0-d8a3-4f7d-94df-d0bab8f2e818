import os
import re
import uuid
from contextlib import asynccontextmanager
from decimal import Decimal
from typing import Optional
from urllib.parse import urlencode

import django
from asgiref.sync import sync_to_async
from django.utils import timezone
from fastapi import <PERSON><PERSON>, FastAPI, HTTPException, Query, Request, Response
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import RedirectResponse, UJSONResponse
from redis import asyncio as aioredis
from slowapi import Limiter, _rate_limit_exceeded_handler
from slowapi.errors import RateLimitExceeded
from slowapi.util import get_remote_address
from typing_extensions import Union

from fapi import cache
from product.enums import ProductType

if os.path.exists('.env'):
    from dotenv import load_dotenv
    load_dotenv()

# fmt: off
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'cakto.settings')
django.setup()

# flake8: noqa:E402
from django.conf import settings

from apps.services.event_manager import dispatch_pixel_events
from apps.services.types import DispatchPixelInitCheckoutKwargs
from fapi.db import AsyncCloseConnectionsMiddleware
from fapi.utils import calculateProductInstallments
from gateway.utils import get_split_installments_fees
from product.models import Affiliate, Link, Offer, Product, TrackingPixels
from product.serializers import OfferCheckoutSerializer, TrackingPixelSerializer
from product.utils import calculateInstallments, get_affiliate_cookie_info, increase_checkout_visits

# fmt: on
limiter = Limiter(key_func=get_remote_address)

@asynccontextmanager
async def lifespan(app: FastAPI):
    # https://fastapi.tiangolo.com/advanced/events/#lifespan-function
    app.state.cache = await aioredis.from_url(os.environ.get('REDIS_URL'))
    try:
        yield
    finally:
        await app.state.cache.close()

app = FastAPI(lifespan=lifespan)
app.state.limiter = limiter
app.add_exception_handler(RateLimitExceeded, _rate_limit_exceeded_handler)  # type:ignore
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGIN_WHITELIST,
    allow_credentials=True,
)
app.add_middleware(AsyncCloseConnectionsMiddleware)

def remove_duplicated_bumps(data):
    offers_ids = [data['id']]
    bumps_to_remove = []
    for i, bump in enumerate(data['product']['bumps']):
        if bump['offer']['id'] in offers_ids:
            bumps_to_remove.append(i)
            continue
        offers_ids.append(bump['offer']['id'])
    data['product']['bumps'] = [bump for i, bump in enumerate(data['product']['bumps']) if i not in bumps_to_remove]

def unpack_offer_id_and_checkout_id(short_id: str) -> tuple[str, str | None]:
    # Remove any non-alphanumeric except hyphens and underscores
    short_id = re.sub(r'[^0-9a-zA-Z-_]', '', short_id or '')
    offer_id, *checkout_id = short_id.split('_')
    return offer_id, checkout_id[0] if checkout_id else None

def get_serialized_data(short_id: str):
    offer_id, checkout_id = unpack_offer_id_and_checkout_id(short_id)
    offer_instance = Offer.objects.filter(status='active', id__iexact=offer_id).select_related('product').first()

    if not offer_instance:
        raise HTTPException(status_code=400, detail='Oferta não encontrada.')

    offer_data = OfferCheckoutSerializer(offer_instance, context={"checkout_id": checkout_id}).data
    offer_data['tracking_pixels'].pop('id', None)  # type:ignore
    remove_duplicated_bumps(offer_data)

    return offer_data, offer_instance

async def serialize_offer(offer_checkout_id):
    offer_id, checkout_id = unpack_offer_id_and_checkout_id(offer_checkout_id)
    cache_key = Offer.cache_prefix + offer_id + (f'_{checkout_id}' if checkout_id else '')
    offer_data, offer_instance = await cache.get_or_set(app, (cache_key), sync_to_async(lambda: get_serialized_data(offer_checkout_id)), None)  # type:ignore

    increase_checkout_visits.delay(offer=offer_instance, checkout_id=checkout_id)

    return offer_data, offer_instance

async def set_affiliate_data(offer_data, offer_instance, cookie_affiliateId, query_param_affiliateId):
    affiliate_short_id = Affiliate.validate_short_id(query_param_affiliateId or cookie_affiliateId)

    if affiliate_short_id:
        offer_data['affiliate'] = await cache.get_or_set(
            app,
            f'{Affiliate.cache_prefix}{affiliate_short_id}',
            sync_to_async(lambda: get_affiliate_cookie_info(affiliate_short_id, offer_instance.product.pk)),  # type:ignore
            None
        )
    else:
        offer_data['affiliate'] = None

    return offer_data

async def enqueue_initiate_checkout_event(request, offer_instance, pixelEventId, tracking_pixels, client_ip):
    query_params = request.query_params
    kwargs: DispatchPixelInitCheckoutKwargs = {
        'offer': offer_instance,
        'client_ip': client_ip,
        'client_user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3',
        'pixelEventId': pixelEventId,
        'checkoutUrl': query_params.get('checkoutUrl'),
        'refererUrl': query_params.get('refererUrl'),
        'fbc': query_params.get('fbc'),
        'fbp': query_params.get('fbp'),
        'email': query_params.get('email'),
        'phone': query_params.get('phone'),
        'name': query_params.get('name'),
        'event_time': int(timezone.now().timestamp()),
    }
    if tracking_pixels:
        kwargs.update(tracking_pixels=tracking_pixels)
    dispatch_pixel_events.delay(event='initiate_checkout', **kwargs)

async def get_cached_tracking_pixels(offer_data: dict) -> Union[TrackingPixels, dict]:
    product_short_id = offer_data.get('product', {}).get('short_id')

    def get_tracking_pixels() -> bytes:
        product = Product.objects.filter(short_id=product_short_id).first()
        affiliate_pixel = product.pixels.filter(  # type:ignore
            affiliate__short_id=offer_data['affiliate'].get('affiliateShortId'),
            affiliate__status='active',
        ).first()
        tracking_pixels = affiliate_pixel or product.pixels.filter(affiliate__isnull=True).first()  # type:ignore
        return tracking_pixels

    if offer_data['affiliate']:
        instance_cache_name = f'{TrackingPixels.instance_cache_prefix}{product_short_id}_{offer_data["affiliate"].get("affiliateShortId")}'
    else:
        instance_cache_name = f'{TrackingPixels.instance_cache_prefix}{product_short_id}'

    tracking_pixels = await cache.get_or_set(
        app,
        instance_cache_name,
        sync_to_async(get_tracking_pixels),
        None
    )

    data_cache_name = f'{TrackingPixels.data_cache_prefix}{product_short_id}_{tracking_pixels.pk}'  # type:ignore
    data = await cache.get_or_set(app, data_cache_name, sync_to_async(lambda: TrackingPixelSerializer(tracking_pixels).data), None)  # type:ignore
    data.pop('id', None)  # type:ignore

    return tracking_pixels, data  # type:ignore

async def proccess_pixels(request, offer_instance, offer_data, client_ip) -> dict:
    tracking_pixels = None
    if offer_data['affiliate']:
        tracking_pixels, pixels_data = await get_cached_tracking_pixels(offer_data)  # type:ignore
        offer_data['tracking_pixels'] = pixels_data

    # Set unique id for the initiate_checkout event and send it to frontend use the same id.
    offer_data['pixelEventId'] = str(uuid.uuid4())

    # Send event to queue
    await enqueue_initiate_checkout_event(request, offer_instance, offer_data['pixelEventId'], tracking_pixels, client_ip)

    return offer_data

def get_client_ip(request):
    HTTP_DO_CONNECTING_IP = request.headers.get('HTTP_DO_CONNECTING_IP')
    client_ip = HTTP_DO_CONNECTING_IP or request.client.host
    return client_ip

async def get_checkout_fees(offer_instance):
    company_id = await sync_to_async(lambda: offer_instance.product.user._company.externalId)()
    return await cache.get_or_set(
        app,
        f'split_installments_fee{company_id}',
        sync_to_async(lambda: get_split_installments_fees(company_id)),  # type:ignore
        timezone.timedelta(days=1)
    )


@app.get("/api/product/checkout/{offer_checkout_id}/", response_class=UJSONResponse)
@limiter.limit("300/hour", error_message="Rate limit exceeded")
async def get_checkout(
    request: Request,
    offer_checkout_id: str,
    affiliate_short_id: Optional[str] = Cookie(None),
):
    client_ip = get_client_ip(request)

    offer_data, offer_instance = await serialize_offer(offer_checkout_id.lower())

    installmentsFee = await get_checkout_fees(offer_instance)

    offer_data = await set_affiliate_data(offer_data, offer_instance, affiliate_short_id, request.query_params.get('affiliateShortId'))

    offer_data = await proccess_pixels(request, offer_instance, offer_data, client_ip)

    offer_data = await calculateProductInstallments(offer_data, installmentsFee)

    django.db.close_old_connections()

    return offer_data

# Redirect View
def get_link_data(shortId) -> tuple[str | None, str | None]:
    link = Link.objects.filter(shortId=shortId, status='active').first()
    return (link.url, link.product.pk) if link else (None, None)

async def get_cached_url(shortId) -> tuple[str, str | None]:
    front_end_404_page = settings.FRONT_END_BASE_URL + '/404'

    shortId = Link.validate_short_id(shortId)

    if not shortId:
        return front_end_404_page, None

    link_url, product_id = await cache.get_or_set(app, (Link.cache_prefix + shortId), sync_to_async(lambda: get_link_data(shortId)), None)

    if not link_url and product_id:
        return front_end_404_page, None

    return link_url, product_id

def get_afid_data(afid_shortId, product_id) -> dict | None:
    affiliate = Affiliate.objects.filter(short_id=afid_shortId, status='active').select_related('product').first()
    return get_affiliate_cookie_info(afid_shortId, product_id) if affiliate else None

async def get_cached_afid_data(afid_shortId: str | None, product_id: str | None) -> dict | None:
    afid_shortId = Affiliate.validate_short_id(afid_shortId)

    if not afid_shortId or not product_id:
        return None

    return await cache.get_or_set(
        app,
        f'{Affiliate.cache_prefix}{afid_shortId}',
        sync_to_async(lambda: get_afid_data(afid_shortId, product_id)),  # type:ignore
        None
    )

async def set_affiliate_cookie(response, afid_data) -> None:
    cookieTime = int(afid_data['cookieTime']) if afid_data['cookieTime'] > 0 else 365
    response.set_cookie(
        key='affiliate_short_id',
        value=afid_data['affiliateShortId'],
        domain=settings.SESSION_COOKIE_DOMAIN,
        max_age=60 * 60 * 24 * cookieTime
    )

@app.get("/api/{shortId}/")
@limiter.limit("300/hour", error_message="Rate limit exceeded")
async def get_short_id(shortId: str, request: Request, response: Response) -> RedirectResponse:
    # redirect to be possible to set cookies over the cakto.com.br domain
    if request.client.host.endswith(settings.REDIRECT_DOMAIN):  # type:ignore
        return RedirectResponse(url=f'{settings.BACKEND_BASE_URL}{shortId}/?{urlencode(request.query_params)}')

    url, product_id = await get_cached_url(shortId)

    redirect_response = RedirectResponse(url=url)

    afid_data = await get_cached_afid_data(request._query_params.get('affiliate'), product_id)

    if afid_data:
        await set_affiliate_cookie(response, afid_data)

    redirect_response.headers.update(response.headers)
    return redirect_response


@app.get("/api/checkout/installments/{offer_id}/")
@limiter.limit("300/hour", error_message="Rate limit exceeded")
async def get_checkout_calculated_installments(
    offer_id: str,
    request: Request,
):
    offer_id, _ = unpack_offer_id_and_checkout_id(offer_id)

    total_param = request._query_params.get('total', '0')
    total = Decimal(re.sub(r'[^\d.]', '', total_param))

    checkout_installments = await cache.get_or_set(
        app,
        f'checkout_installments_{offer_id}_{total}',
        lambda: calculate_checkout_installments(offer_id, total),
        timezone.timedelta(seconds=60),
    )

    return checkout_installments

async def calculate_checkout_installments(offer_id: str, total_value: Decimal):
    offer_data, offer_instance = await cache.get_or_set(
        app,
        (Offer.cache_prefix + offer_id),
        sync_to_async(lambda: get_serialized_data(offer_id)),
        None
    )

    installmentsFee = await get_checkout_fees(offer_instance)

    calculated_installments = calculateInstallments(
        installments_fees=installmentsFee,
        price=total_value or offer_instance.price,
        offer_type=offer_data.get('type') or offer_data['product']['type'],
        max_installments=offer_data['product']['installments'],
        recurrence_period=offer_instance.recurrence_period,
    )

    return calculated_installments
