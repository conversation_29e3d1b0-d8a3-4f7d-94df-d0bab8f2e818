export const PLATFORMS = {
  memberkit: 'memberkit',
  active_campaign: 'active_campaign',
  webhooks: 'webhook',
  spedy: 'spedy',
  cademi: 'cademi',
  voxui: 'voxuy',
  smsfunnel: 'smsfunnel',
  utmify: 'utmify',
  astron_members: 'astron_members',
  themembers: 'themembers',
};

export const PLATFORMS_CONFIG = {
  [PLATFORMS.astron_members]: {
    type: 'astron_members',
    image: {
      light: '/assets/apps/astron_members_light.png',
      dark: '/assets/apps/astron_members.png',
    },
  },
  [PLATFORMS.memberkit]: {
    type: 'memberkit',
    image: {
      light: '/assets/apps/memberkit.png',
      dark: '/assets/apps/memberkit.png',
    },
    fields: [
      {
        name: 'secret_key',
        label: 'Chave secreta',
        type: 'password',
      },
    ],
  },
  [PLATFORMS.active_campaign]: {
    type: 'active_campaign',
    image: {
      light: '/assets/apps/active_campaign.png',
      dark: '/assets/apps/active_campaign.png',
    },
    fields: [
      {
        name: 'token',
        label: 'Api Key',
        type: 'password',
      },
    ],
  },
  [PLATFORMS.webhooks]: {
    type: 'webhook',
    image: {
      light: '/assets/apps/webhooks.png',
      dark: '/assets/apps/webhooks.png',
    },
    fields: [
      {
        name: 'secret',
        label: 'Chave secreta do webhook',
        type: 'password',
        disabled: true,
        isToCopy: true,
      },
    ],
  },
  [PLATFORMS.spedy]: {
    type: 'spedy',
    image: {
      light: '/assets/apps/spedy_light.png',
      dark: '/assets/apps/spedy.png',
    },
    fields: [
      {
        name: 'secret',
        label: 'Chave secreta da sua integração spedy',
        type: 'password',
      },
    ],
  },
  [PLATFORMS.cademi]: {
    type: 'cademi',
    image: {
      light: '/assets/apps/cademi-light.png',
      dark: '/assets/apps/cademi.png',
    },
    fields: [
      {
        name: 'token',
        label: 'Token',
        type: 'password',
      },
      {
        name: 'api_key',
        label: 'API Key',
        type: 'password',
      },
      {
        name: 'domain',
        label: 'Dominío Cademí',
        type: 'text',
      },
    ],
  },
  [PLATFORMS.voxui]: {
    type: 'voxuy',
    image: {
      light: '/assets/apps/voxuy.png',
      dark: '/assets/apps/voxuy.png',
    },
    fields: [
      {
        name: 'token',
        label: 'Token API',
        type: 'password',
      },
      {
        name: 'plan_id',
        label: 'Id do Plano',
        type: 'password',
      },
    ],
  },
  [PLATFORMS.smsfunnel]: {
    type: 'smsfunnel',
    image: {
      light: '/assets/apps/smsfunnel-light.png',
      dark: '/assets/apps/smsfunnel.png',
    },
    fields: [],
  },
  [PLATFORMS.utmify]: {
    type: 'utmify',
    image: {
      light: '/assets/apps/utmify.png',
      dark: '/assets/apps/utmify.png',
    },
    fields: [
      {
        name: 'token',
        label: 'API Token',
        type: 'password',
      },
    ],
  },
  [PLATFORMS.themembers]: {
    type: 'themembers',
    image: {
      light: '/assets/apps/themembers-light.png',
      dark: '/assets/apps/themembers.png',
    },
    fields: [
      {
        name: 'platform_token',
        label: 'Token da Plataforma',
        type: 'password',
      },
      {
        name: 'product_id',
        label: 'ID do Produto',
        type: 'text',
      },
    ],
  },
};
