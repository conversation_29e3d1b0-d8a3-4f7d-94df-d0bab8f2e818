import { Box, Typography } from '@mui/material';
import PropTypes from 'prop-types';

export const AppErrorRendererCard = ({
  field,
  message,
  errorMsg,
  fieldNameMap = {},
  hasFieldErrors,
  dataTestId,
}) => (
  <Box
    width={280}
    display="flex"
    alignItems={hasFieldErrors ? 'initial' : 'center'}
    minHeight={70}
    py={hasFieldErrors ? 2 : 0}
    data-testid={dataTestId}
  >
    <Box
      display="flex"
      justifyContent={hasFieldErrors ? 'initial' : 'center'}
      flexDirection="column"
      gap={1}
    >
      {(fieldNameMap[field] || field) && (
        <Typography variant="body2" color="error.main" fontWeight="bold" textTransform="capitalize">
          {fieldNameMap[field] || field}
        </Typography>
      )}
      <Typography variant="body2" fontWeight="600">
        {message}
      </Typography>
      {errorMsg &&
        !/^[a-z0-9_]+$/.test(errorMsg) && (
          <Typography variant="body2" color="text.secondary">
            {errorMsg}
          </Typography>
        )}
    </Box>
  </Box>
);

AppErrorRendererCard.propTypes = {
  field: PropTypes.string,
  message: PropTypes.string.isRequired,
  errorMsg: PropTypes.string,
  fieldNameMap: PropTypes.objectOf(PropTypes.string),
  dataTestId: PropTypes.string,
  hasFieldErrors: PropTypes.bool,
};
