import { useState, useMemo, useEffect } from 'react';
import PropTypes from 'prop-types';
import {
  Alert,
  Box,
  Dialog,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  IconButton,
  Grid,
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import { useFormContext } from 'react-hook-form';
import Iconify from '@/components/iconify';
import { RHFCodes } from '@/components/hook-form';
import { ResendCode } from '@/sections/@dashboard/two-factor-authenticate/components';
import { WHATSAPP_ERROR_MESSAGE } from '@/consts/auth';
import UpdateCellphoneModal from '@/sections/auth/UpdateCellphoneModal';
import useAuthLoginForm from '@/sections/auth/Login/useAuthLoginForm/useAuthLoginForm';
import useSendCodeEmail from '@/sections/@dashboard/two-factor-authenticate/useSendCodeEmail/useSendCodeEmail';

LoginSecondStepCodeInputModal.propTypes = {
  open: PropTypes.bool,
  onClose: PropTypes.func,
  viewPageAs: PropTypes.oneOf(['student', 'seller']),
};

export default function LoginSecondStepCodeInputModal({ open = false, onClose, viewPageAs }) {
  const options = {
    app_cakto: {
      description: 'Insira o código de 6 dígitos gerado no seu app Cakto.',
      label: 'App Cakto',
      icon: 'mingcute:cellphone-2-line',
    },
    email: {
      description: 'Insira o código de 6 dígitos enviado para o seu email.',
      label: 'Email',
      icon: 'ic:baseline-email',
    },
    totp: {
      description: 'Insira o código de 6 dígitos gerado no seu app de autenticação.',
      label: 'App de Autenticação',
      icon: 'mingcute:cellphone-2-line',
    },
  };

  const { loginSecondStepHook, methodsMfa } = useAuthLoginForm();
  const { sendCodeEmail, isLoadingSendCodeEmail, secondsToWait } = useSendCodeEmail();

  const initialTime = () => {
    const newTime = new Date();
    const seconds = secondsToWait < 60 ? secondsToWait : 60;
    newTime.setSeconds(newTime.getSeconds() + seconds);
    return newTime;
  };

  const [errorMessage, setErrorMessage] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const [selectedMethod, setSelectedMethod] = useState('');
  const [showMethodSelection, setShowMethodSelection] = useState(false);

  const [isUpdateCellphoneModalOpen, setIsUpdateCellPhoneModalOpen] = useState(false);

  const onOpenIsUpdateCellphoneModalOpen = () => setIsUpdateCellPhoneModalOpen(true);

  const onCloseUpdateCellPhoneModal = () => setIsUpdateCellPhoneModalOpen(false);

  useEffect(() => {
    if (methodsMfa) {
      if (methodsMfa.length > 1) {
        setShowMethodSelection(true);
        const defaultMethod = methodsMfa.find(({ primary }) => primary)?.type;
        setSelectedMethod(defaultMethod);
      } else if (methodsMfa.length === 1) {
        setSelectedMethod(methodsMfa[0].type);
        setShowMethodSelection(false);
      }
    }
  }, [methodsMfa]);

  const handleChangeMethod = async (selectMethod) => {
    try {
      if (selectMethod === 'email' || selectMethod === 'app_cakto') {
        const email = localStorage.getItem('emailForResendCode');
        const params = email ? { method: selectMethod, email } : { method: selectMethod };
        await sendCodeEmail(params);
      }
    } finally {
      setSelectedMethod(selectMethod);
      setShowMethodSelection(false);
    }
  };

  const resetError = () => setErrorMessage('');

  const { watch, reset } = useFormContext();

  const codes = watch(['code1', 'code2', 'code3', 'code4', 'code5', 'code6']);

  const isCodeComplete = useMemo(() => codes.every((value) => value?.length), [codes]);

  const submitCode = async () => {
    setIsSubmitting(true);
    const code = codes.map((value) => value).join('');

    try {
      const [codeResponse, error] = await loginSecondStepHook(code, viewPageAs);

      if (codeResponse === false) {
        setErrorMessage('Token inválido');
        setIsSubmitting(false);
      } else {
        setIsSubmitting(true);
      }

      if (error === WHATSAPP_ERROR_MESSAGE) {
        onOpenIsUpdateCellphoneModalOpen();
      }
    } catch (error) {
      setErrorMessage('Token inválido');
      setIsSubmitting(false);
    }    
  };

  const handleBack = () => {
    setErrorMessage('');
    setSelectedMethod('');
    setShowMethodSelection(true);
  };

  const closeModal = () => {
    setErrorMessage('');
    onClose();
  };

  useEffect(() => {
    reset();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [open]);

  return (
    <Dialog
      fullWidth
      maxWidth="xs"
      open={open}
      disableEscapeKeyDown
      sx={{
        '& .MuiDialog-paper': {
          borderRadius: '4px',
          padding: '10px',
        },
      }}
    >
      {onClose && (
        <Grid sx={{ display: 'flex', justifyContent: 'space-between' }}>
          <Grid item>
            {showMethodSelection ? null : (
              <IconButton
                data-testid="back-button"
                onClick={handleBack}
                sx={{ color: (theme) => theme.palette.grey[500] }}
              >
                <ArrowBackIcon />
              </IconButton>
            )}
          </Grid>
          <Grid item>
            <IconButton
              data-testid="close-button"
              onClick={closeModal}
              sx={{
                color: (theme) => theme.palette.grey[500],
              }}
            >
              <CloseIcon />
            </IconButton>
          </Grid>
        </Grid>
      )}

      <DialogContent>
        {showMethodSelection ? (
          <>
            <Typography variant="h6" color="primary" fontWeight="bold" display="inline">
              Escolha o método de autenticação
            </Typography>
            <Typography variant="body2" color="textSecondary" paragraph>
              Selecione o método que deseja utilizar para autenticação.
            </Typography>
            <Box display="flex" flexDirection="column" gap={2}>
              {methodsMfa.map((method) => (
                <Button
                  key={method.type}
                  disabled={isLoadingSendCodeEmail}
                  type="button"
                  variant={selectedMethod === method.type ? 'contained' : 'outlined'}
                  color="primary"
                  onClick={() => handleChangeMethod(method.type)}
                  sx={{
                    textTransform: 'none',
                    fontWeight: 'bold',
                    display: 'flex',
                    alignItems: 'center',
                    gap: 1,
                  }}
                  loading={isLoadingSendCodeEmail}
                >
                  <Iconify icon={options[method?.type || 'email']?.icon} width={20} height={20} />
                  {options[method?.type || 'email']?.label}
                </Button>
              ))}
            </Box>
          </>
        ) : (
          <>
            <Box display="flex" alignItems="center">
              <Typography variant="h6" color="primary" fontWeight="bold" display="inline">
                Digite o código
              </Typography>
            </Box>
            <Typography variant="body2" color="textSecondary" paragraph>
              {options[selectedMethod || 'email'].description}
            </Typography>

            <RHFCodes
              keyName="code"
              inputs={['code1', 'code2', 'code3', 'code4', 'code5', 'code6']}
              type="number"
              onChange={resetError}
            />
          </>
        )}
      </DialogContent>

      <DialogActions>
        {!showMethodSelection && (
          <Grid container spacing={2}>
            <Grid item xs={12}>
              {errorMessage !== '' && (
                <Alert data-testid="auth-login-test-error" severity="error">
                  {errorMessage}
                </Alert>
              )}
            </Grid>
            {selectedMethod === 'email' && (
              <Grid item xs={12}>
                <ResendCode expiryTimestamp={initialTime} />
              </Grid>
            )}

            <Grid item xs={12}>
              <Button
                variant="contained"
                color="primary"
                fullWidth
                onClick={submitCode}
                disabled={!isCodeComplete || isSubmitting}
                data-testid="confirm-button"
              >
                Confirmar Código
              </Button>
            </Grid>
          </Grid>
        )}
      </DialogActions>

      {isUpdateCellphoneModalOpen && (
        <UpdateCellphoneModal
          open={isUpdateCellphoneModalOpen}
          onClose={onCloseUpdateCellPhoneModal}
        />
      )}
    </Dialog>
  );
}
