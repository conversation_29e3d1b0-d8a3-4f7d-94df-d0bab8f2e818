import PropTypes from 'prop-types';
// @mui
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Button,
  IconButton,
  MenuItem,
  Typography,
} from '@mui/material';
// components
import { yupResolver } from '@hookform/resolvers/yup';
import * as datefns from 'date-fns';
import { useContext, useMemo, useState } from 'react';
import { useForm } from 'react-hook-form';
import * as yup from 'yup';
// hooks
import { Stack } from '@mui/system';
import { useDateRangePicker } from '../../../components/date-range-picker';
import DateRangePicker from '../../../components/date-range-picker/DateRangePicker';
import {
  RHFMultiCheckbox,
  RHFMultiSelect,
  RHFSelect,
  RHFTextField,
} from '../../../components/hook-form';
import Iconify from '../../../components/iconify';
import TableFilterDrawer from '../../../components/table/TableFilterDrawer';
import {
  OrderCoproductionType,
  OrderPaymentMethod,
  OrderStatus,
  OrderSubscriptionType,
  OrdersContext,
  PERIODS,
  defaultFilter,
} from '../../../contexts/OrdersContext';

MySalesFilter.propTypes = {
  open: PropTypes.bool,
  onClose: PropTypes.func,
};

const today = new Date();

const yesterday = datefns.subDays(today, 1);

const lastWeek = datefns.subDays(today, 7);

const lastMonth = datefns.subMonths(today, 1);

export default function MySalesFilter({ open, onClose }) {
  const [expanded, setExpanded] = useState(false);
  const { table, products, fetchingProducts, affiliates, fetchingAffiliates } =
    useContext(OrdersContext);

  const schema = yup.object().shape({
    period: yup.string().oneOf(Object.values(PERIODS)),
    startDate: yup.date().nullable(),
    endDate: yup.date().nullable(),
    coproductionType: yup.string(),
    utm_source: yup.string().nullable(),
    utm_medium: yup.string().nullable(),
    utm_campaign: yup.string().nullable(),
    utm_content: yup.string().nullable(),
    utm_term: yup.string().nullable(),
    sck: yup.string().nullable(),
    offerType: yup.string(),
    products: yup.array().of(yup.string()),
    affiliates: yup.array().of(yup.string()),
    paymentMethods: yup.array().of(yup.string()),
    status: yup.array().of(yup.string()),
    subscription_type: yup.array().of(yup.string()),
  });

  const { formState, ...form } = useForm({
    resolver: yupResolver(schema),
    defaultValues: {
      period: table.filter.period,
      startDate: table.filter.startDate,
      endDate: table.filter.endDate,
      coproductionType: table.filter.coproductionType,
      offerType: table.filter.offerType,
      products: table.filter.products,
      utm_source: table.filter.utm_source,
      utm_medium: table.filter.utm_medium,
      utm_campaign: table.filter.utm_campaign,
      utm_content: table.filter.utm_content,
      utm_term: table.filter.utm_term,
      sck: table.filter.sck,
      affiliates: table.filter.affiliates,
      paymentMethods: table.filter.paymentMethods,
      status: table.filter.status,
      subscription_type: table.filter.subscription_type,
    },
  });

  const {
    startDate: pickerStartDate,
    endDate: pickerEndDate,
    onChangeStartDate: onChangePickerStartDate,
    onChangeEndDate: onChangePickerEndDate,
    open: openPicker,
    onOpen: onOpenPicker,
    onClose: onClosePicker,
    isSelected: isSelectedValuePicker,
    isError,
  } = useDateRangePicker(new Date(), new Date());

  const handlePeriodChange = (newPeriod) => {
    switch (newPeriod) {
      case PERIODS.TODAY:
        form.setValue('startDate', today);
        form.setValue('endDate', today);
        break;
      case PERIODS.YESTERDAY:
        form.setValue('startDate', yesterday);
        form.setValue('endDate', yesterday);
        break;
      case PERIODS.LAST_WEEK:
        form.setValue('startDate', lastWeek);
        form.setValue('endDate', today);
        break;
      case PERIODS.LAST_MONTH:
        form.setValue('startDate', lastMonth);
        form.setValue('endDate', today);
        break;
      case PERIODS.ALL_TIME:
        form.setValue('startDate', null);
        form.setValue('endDate', null);
        break;
      case PERIODS.CUSTOM:
        form.setValue('startDate', pickerStartDate);
        form.setValue('endDate', pickerEndDate);
        break;
      default:
        break;
    }
    form.setValue('period', newPeriod);
  };

  const startDate = form.getValues('startDate');

  const endDate = form.getValues('endDate');

  const shortLabelDate = useMemo(() => {
    if (!startDate && endDate) return ` -  ${datefns.format(endDate, 'dd MMM yy')}`;

    if (!endDate && startDate) return `${datefns.format(startDate, 'dd MMM yy')} - `;

    if (!startDate && !endDate) {
      return '-';
    }
    const isCurrentYear =
      datefns.isSameYear(startDate, endDate) &&
      datefns.getYear(startDate) === datefns.getYear(new Date());
    if (!isCurrentYear) {
      return `${datefns.format(endDate, 'dd MMM yy')} - ${datefns.format(endDate, 'dd MMM yy')}`;
    }
    if (!datefns.isSameMonth(startDate, endDate)) {
      return `${datefns.format(startDate, 'dd MMM')} - ${datefns.format(endDate, 'dd MMM yy')}`;
    }
    if (!datefns.isSameDay(startDate, endDate)) {
      return `${datefns.format(startDate, 'dd')} - ${datefns.format(endDate, 'dd MMM yy')}`;
    }
    return datefns.format(endDate, 'dd MMM yy');
  }, [startDate, endDate]);

  const { dirtyFields } = formState;

  const productsOptions = useMemo(
    () => products?.map((product) => ({ label: product?.name, value: product?.id })) || [],
    [products]
  );

  const affiliatesOptions = useMemo(() => {
    if (!affiliates) return [];

    const emailMap = new Map();
    const duplicates = new Set();

    const uniqueAffiliates = affiliates.filter((affiliate) => {
      const email = affiliate.user?.email;
      if (!email) return false;

      if (emailMap.has(email)) {
        duplicates.add(email);
        return false;
      }
      emailMap.set(email, true);
      return true;
    });

    if (duplicates.size > 0) {
      console.warn('E-mails duplicados encontrados:', Array.from(duplicates));
    }

    return uniqueAffiliates.map((affiliate) => ({
      label: affiliate.user.email,
      value: affiliate.id,
    }));
  }, [affiliates]);

  return (
    <TableFilterDrawer
      open={open}
      onClose={onClose}
      form={form}
      defaultFilter={defaultFilter}
      onSubmit={(data) => {
        onClose();
        table.setDirtyFilters(Object.keys(dirtyFields).length);
        table.setFilter(data);
      }}
    >
      <RHFSelect
        name="period"
        value={form.watch('period')}
        label="Data de criação"
        size="small"
        onChange={(event) => handlePeriodChange(event.target.value)}
      >
        <MenuItem value={PERIODS.TODAY}>Hoje</MenuItem>
        <MenuItem value={PERIODS.YESTERDAY}>Ontem</MenuItem>
        <MenuItem value={PERIODS.LAST_WEEK}>Últimos 7 dias</MenuItem>
        <MenuItem value={PERIODS.LAST_MONTH}>Últimos 30 dias</MenuItem>
        <MenuItem value={PERIODS.ALL_TIME}>Sempre</MenuItem>
        <MenuItem value={PERIODS.CUSTOM}>
          <Stack
            direction="row"
            justifyContent="space-between"
            alignItems="center"
            width={1}
            gap={1}
          >
            <Typography variant="body2">{shortLabelDate}</Typography>
            <IconButton
              onClick={(event) => {
                event.stopPropagation();
                onOpenPicker();
              }}
              size="small"
            >
              <Iconify icon="eva:calendar-fill" />
            </IconButton>
          </Stack>
        </MenuItem>
      </RHFSelect>

      <RHFSelect name="coproductionType" label="Tipo" size="small">
        <MenuItem value={OrderCoproductionType.all.value}>Todos</MenuItem>
        <MenuItem value={OrderCoproductionType.production.value}>Sou produtor</MenuItem>
        <MenuItem value={OrderCoproductionType.coproduction.value}>Sou co-produtor</MenuItem>
        <MenuItem value={OrderCoproductionType.affiliate.value}>Sou afiliado</MenuItem>
      </RHFSelect>

      <RHFSelect name="offerType" label="Tipo de oferta" size="small">
        <MenuItem value="all">Todos</MenuItem>
        <MenuItem value="main">Principal</MenuItem>
        <MenuItem value="upsell">Upsell</MenuItem>
        <MenuItem value="downsell">Downsell</MenuItem>
        <MenuItem value="orderbump">Bump</MenuItem>
      </RHFSelect>

      <RHFMultiSelect
        size="small"
        checkbox
        chip
        hiddenLabel
        name="products"
        label="Produtos"
        options={productsOptions}
        disabled={fetchingProducts}
      />

      <RHFMultiSelect
        size="small"
        checkbox
        chip
        name="affiliates"
        label="Afiliados"
        options={affiliatesOptions}
        disabled={fetchingAffiliates}
      />

      <Accordion
        expanded={expanded}
        onChange={() => setExpanded((prev) => !prev)}
        sx={{
          backgroundColor: 'transparent !important',
          '::before': { display: 'none !important' },
        }}
      >
        <AccordionSummary sx={{ p: 0 }}>
          <Button variant="outlined" type="button">
            Parâmetros de URL{' '}
            <Iconify
              sx={{ marginLeft: 1, transform: expanded ? 'rotate(180deg)' : 'rotate(0deg)' }}
              icon="eva:arrow-ios-downward-fill"
            />
          </Button>
        </AccordionSummary>

        <AccordionDetails sx={{ display: 'flex', flexDirection: 'column', gap: 2, p: 0 }}>
          <RHFTextField size="small" name="utm_source" label="UTM Source" />

          <RHFTextField size="small" name="utm_medium" label="UTM Medium" />

          <RHFTextField size="small" name="utm_campaign" label="UTM Campaign" />

          <RHFTextField size="small" name="utm_content" label="UTM Content" />

          <RHFTextField size="small" name="utm_term" label="UTM Term" />

          <RHFTextField size="small" name="sck" label="SCK" />
        </AccordionDetails>
      </Accordion>

      <RHFMultiCheckbox
        size="small"
        name="paymentMethods"
        label="Métodos de pagamento"
        options={Object.keys(OrderPaymentMethod).map((key) => ({
          label: OrderPaymentMethod[key].label,
          value: key,
        }))}
      />

      <RHFMultiCheckbox
        size="small"
        name="status"
        label="Status"
        options={Object.keys(OrderStatus).map((key) => ({
          label: OrderStatus[key].label,
          value: key,
        }))}
      />

      <RHFMultiCheckbox
        size="small"
        name="subscription_type"
        label="Situação de assinatura"
        options={Object.keys(OrderSubscriptionType).map((key) => ({
          label: OrderSubscriptionType[key].label,
          value: key,
        }))}
      />

      <DateRangePicker
        title="Selecione o período"
        variant="static"
        startDate={pickerStartDate}
        endDate={pickerEndDate}
        onConfirm={async () => {
          const isValid = await form.trigger();

          if (!isValid) return;

          form.setValue('startDate', pickerStartDate);
          form.setValue('endDate', pickerEndDate);
          form.setValue('period', PERIODS.CUSTOM);
        }}
        onChangeStartDate={onChangePickerStartDate}
        onChangeEndDate={onChangePickerEndDate}
        open={openPicker}
        onClose={() => {
          onClosePicker();
        }}
        isSelected={isSelectedValuePicker}
        isError={isError}
      />
    </TableFilterDrawer>
  );
}
