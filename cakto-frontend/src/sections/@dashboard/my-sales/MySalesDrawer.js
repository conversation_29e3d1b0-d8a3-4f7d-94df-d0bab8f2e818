import PropTypes from 'prop-types';
// @mui
import {
  <PERSON>,
  Divider,
  Drawer,
  IconButton,
  ListItemText,
  Paper,
  Tab,
  Typography,
} from '@mui/material';
// components
import { useContext, useState, useMemo } from 'react';
// utils
import { TabContext, <PERSON>b<PERSON>ist, TabPanel } from '@mui/lab';
import { Stack } from '@mui/system';
import { AuthContext } from '../../../auth/JwtContext';
import Iconify from '../../../components/iconify';
import Label from '../../../components/label';
import { OrderPaymentMethod, OrderStatus, OrderType } from '../../../contexts/OrdersContext';
import { fCurrency } from '../../../utils/formatNumber';
import { fDate } from '../../../utils/formatTime';

MySalesDrawer.propTypes = {
  open: PropTypes.bool,
  onClose: PropTypes.func,
  sale: PropTypes.func,
};

export default function MySalesDrawer({ open, onClose, sale }) {
  const [tabValue, setTabValue] = useState('order');

  const { user: loggedUser } = useContext(AuthContext);

  const commissionsUsers = useMemo(() => {
    if (Object.keys(sale)?.length) {
      const commissionedUsersMap = sale?.commissionedUsers?.reduce((acc, user) => {
        acc[String(user.id)] = user.email;
        return acc;
      }, {});

      return sale?.commissions
        ?.filter((commission) => commissionedUsersMap[String(commission.userId)])
        ?.map((commission) => ({
          email: commissionedUsersMap[String(commission.userId)],
          commissionValue: commission.commissionValue,
        }))
        ?.reduce((acc, { email, commissionValue }) => {
          const existing = acc.find((item) => item.email === email);

          if (existing) {
            existing.commissionValue += commissionValue;
          } else {
            acc.push({
              email,
              commissionValue,
            });
          }

          return acc;
        }, []);
    }

    return [];
  }, [sale]);

  const loggedUserCommission = sale?.commissions?.find?.(
    (commission) => `${commission?.userId}` === `${loggedUser.id}`
  ) || { commissionValue: 0 };

  const phoneNumber = sale?.customer?.phone;

  return (
    <Drawer
      anchor="right"
      open={open}
      onClose={onClose}
      PaperProps={{
        sx: {
          p: 2,
          width: {
            xs: '100%',
            sm: 400,
          },
        },
      }}
    >
      <Stack direction="row" alignItems="center" gap={1}>
        <IconButton onClick={onClose}>
          <Iconify icon="eva:chevron-right-fill" />
        </IconButton>
        <Typography variant="subtitle1">Detalhes da Venda</Typography>
      </Stack>
      <Box p={2}>
        <TabContext value={tabValue}>
          <TabList
            value={tabValue}
            onChange={(event, newTab) => {
              setTabValue(newTab);
            }}
          >
            <Tab value="order" label="Venda" />
            <Tab value="customer" label="Cliente" />
            <Tab value="amount" label="Valores" />
          </TabList>
          <TabPanel value="order" sx={{ px: 0, py: 2 }}>
            <Stack spacing={2}>
              <ListItemText
                primary={
                  <Typography variant="body2" fontWeight="bold">
                    ID da venda
                  </Typography>
                }
                secondary={
                  <Typography variant="body2" color="text.secondary">
                    {sale.refId}
                  </Typography>
                }
              />

              <ListItemText
                primary={
                  <Typography variant="body2" fontWeight="bold">
                    Status
                  </Typography>
                }
                secondary={
                  <Label variant="soft" color={OrderStatus[sale.status]?.color || 'default'}>
                    {OrderStatus[sale.status]?.label || 'Indefinido'}
                  </Label>
                }
              />

              {sale.refund_reason !== null && (
                <ListItemText
                  style={{ marginTop: -2 }}
                  primary={
                    <Typography variant="body2" fontWeight="bold">
                      Motivo do reembolso
                    </Typography>
                  }
                  secondary={
                    <Typography variant="body2" color="text.secondary">
                      {sale.refund_reason}
                    </Typography>
                  }
                />
              )}

              {sale.reason !== null && (
                <ListItemText
                  style={{ marginTop: -2 }}
                  primary={
                    <Typography variant="body2" fontWeight="bold">
                      Motivo da recusa
                    </Typography>
                  }
                  secondary={
                    <Typography variant="body2" color="text.secondary">
                      {sale.reason}
                    </Typography>
                  }
                />
              )}

              <ListItemText
                primary={
                  <Typography variant="body2" fontWeight="bold">
                    Tipo
                  </Typography>
                }
                secondary={
                  <Typography variant="body2" color="text.secondary">
                    {OrderType[sale.type]?.label || 'Indefinido'}
                  </Typography>
                }
              />

              <ListItemText
                primary={
                  <Typography variant="body2" fontWeight="bold">
                    Valor líquido
                  </Typography>
                }
                secondary={
                  <Typography variant="body2" color="text.secondary">
                    {fCurrency(parseFloat(loggedUserCommission.commissionValue))}
                  </Typography>
                }
              />

              <ListItemText
                primary={
                  <Typography variant="body2" fontWeight="bold">
                    Produto
                  </Typography>
                }
                secondary={
                  <Typography variant="body2" color="text.secondary">
                    {sale?.product?.name || '-'}
                  </Typography>
                }
              />

              <ListItemText
                primary={
                  <Typography variant="body2" fontWeight="bold">
                    Método de pagamento
                  </Typography>
                }
                secondary={
                  <Typography variant="body2" color="text.secondary">
                    {OrderPaymentMethod[sale.paymentMethod]?.label}
                  </Typography>
                }
              />

              <ListItemText
                primary={
                  <Typography variant="body2" fontWeight="bold">
                    Parcelas
                  </Typography>
                }
                secondary={
                  <Typography variant="body2" color="text.secondary">
                    {sale.installments}
                  </Typography>
                }
              />

              <ListItemText
                primary={
                  <Typography variant="body2" fontWeight="bold">
                    Recorrência
                  </Typography>
                }
                secondary={
                  <Typography variant="body2" color="text.secondary">
                    {sale.subscription_period}
                  </Typography>
                }
              />

              {/* UTMS */}
              <ListItemText
                primary={
                  <Typography variant="body2" fontWeight="bold">
                    utm_source
                  </Typography>
                }
                secondary={
                  <Typography
                    variant="body2"
                    color="text.secondary"
                    sx={{ wordWrap: 'break-word' }}
                  >
                    {sale.utm_source || 'Não informado'}
                  </Typography>
                }
              />
              <ListItemText
                primary={
                  <Typography variant="body2" fontWeight="bold">
                    utm_campaign
                  </Typography>
                }
                secondary={
                  <Typography
                    variant="body2"
                    color="text.secondary"
                    sx={{ wordWrap: 'break-word' }}
                  >
                    {sale.utm_campaign || 'Não informado'}
                  </Typography>
                }
              />
              <ListItemText
                primary={
                  <Typography variant="body2" fontWeight="bold">
                    utm_medium
                  </Typography>
                }
                secondary={
                  <Typography
                    variant="body2"
                    color="text.secondary"
                    sx={{ wordWrap: 'break-word' }}
                  >
                    {sale.utm_medium || 'Não informado'}
                  </Typography>
                }
              />
              <ListItemText
                primary={
                  <Typography variant="body2" fontWeight="bold">
                    utm_content
                  </Typography>
                }
                secondary={
                  <Typography
                    variant="body2"
                    color="text.secondary"
                    sx={{ wordWrap: 'break-word' }}
                  >
                    {sale.utm_content || 'Não informado'}
                  </Typography>
                }
              />
              <ListItemText
                primary={
                  <Typography variant="body2" fontWeight="bold">
                    utm_term
                  </Typography>
                }
                secondary={
                  <Typography
                    variant="body2"
                    color="text.secondary"
                    sx={{ wordWrap: 'break-word' }}
                  >
                    {sale.utm_term || 'Não informado'}
                  </Typography>
                }
              />
              <ListItemText
                primary={
                  <Typography variant="body2" fontWeight="bold">
                    sck
                  </Typography>
                }
                secondary={
                  <Typography
                    variant="body2"
                    color="text.secondary"
                    sx={{ wordWrap: 'break-word' }}
                  >
                    {sale.sck || 'Não informado'}
                  </Typography>
                }
              />

              <ListItemText
                primary={
                  <Typography variant="body2" fontWeight="bold">
                    Data de criação
                  </Typography>
                }
                secondary={
                  <Typography variant="body2" color="text.secondary">
                    {fDate(sale.createdAt, 'dd/MM/yyyy HH:mm')}
                  </Typography>
                }
              />
            </Stack>
          </TabPanel>
          <TabPanel value="customer" sx={{ px: 0, py: 2 }}>
            <Stack spacing={2}>
              <ListItemText
                primary={
                  <Typography variant="body2" fontWeight="bold">
                    Nome
                  </Typography>
                }
                secondary={
                  <Typography variant="body2" color="text.secondary">
                    {sale?.customer?.name || '-'}
                  </Typography>
                }
              />

              <ListItemText
                primary={
                  <Typography variant="body2" fontWeight="bold">
                    E-mail
                  </Typography>
                }
                secondary={
                  <Typography variant="body2" color="text.secondary">
                    {sale?.customer?.email || '-'}
                  </Typography>
                }
              />

              <ListItemText
                primary={
                  <Typography variant="body2" fontWeight="bold">
                    Celular
                  </Typography>
                }
                secondary={
                  <Stack direction="row" spacing={1} alignItems="center">
                    <Typography variant="body2" color="text.secondary">
                      {phoneNumber || '-'}
                    </Typography>
                    {!!phoneNumber && (
                      <IconButton
                        size="small"
                        href={`https://api.whatsapp.com/send?phone=${phoneNumber.startsWith('55') ? phoneNumber : `55${phoneNumber}`
                          }`}
                        target="_blank"
                      >
                        <Iconify icon="logos:whatsapp-icon" />
                      </IconButton>
                    )}
                  </Stack>
                }
              />

              <ListItemText
                primary={
                  <Typography variant="body2" fontWeight="bold">
                    CPF
                  </Typography>
                }
                secondary={
                  <Typography variant="body2" color="text.secondary">
                    {sale?.customer?.docNumber || '-'}
                  </Typography>
                }
              />

              <ListItemText
                primary={
                  <Typography variant="body2" fontWeight="bold">
                    IP
                  </Typography>
                }
                secondary={
                  <Typography variant="body2" color="text.secondary">
                    {sale?.customer?.ip || '-'}
                  </Typography>
                }
              />
            </Stack>
          </TabPanel>
          <TabPanel value="amount" sx={{ px: 0, py: 2 }}>
            <Stack spacing={2}>
              <ListItemText
                primary={
                  <Typography variant="body2" fontWeight="bold">
                    Valor base do produto
                  </Typography>
                }
                secondary={
                  <Typography variant="body2" color="text.secondary">
                    {fCurrency(parseFloat(sale.baseAmount))}
                  </Typography>
                }
              />

              <ListItemText
                primary={
                  <Typography variant="body2" fontWeight="bold">
                    Desconto
                  </Typography>
                }
                secondary={
                  <Typography
                    variant="body2"
                    color="text.secondary"
                    sx={{ wordBreak: 'break-all' }}
                  >
                    {fCurrency(parseFloat(sale.discount || 0))}{' '}
                    {sale.couponCode ? `(Cupom: ${sale.couponCode})` : ''}
                  </Typography>
                }
              />

              <ListItemText
                primary={
                  <Typography variant="body2" fontWeight="bold">
                    Valor Pago pelo Cliente
                  </Typography>
                }
                secondary={
                  <Typography variant="body2" color="text.secondary">
                    {fCurrency(parseFloat(sale.amount))}
                  </Typography>
                }
              />

              <ListItemText
                primary={
                  <Typography variant="body2" fontWeight="bold">
                    Taxas
                  </Typography>
                }
                secondary={
                  <Typography variant="body2" color="text.secondary">
                    {fCurrency(parseFloat(sale.fees || 0))}
                  </Typography>
                }
              />

              <ListItemText
                primary={
                  <Typography variant="body2" fontWeight="bold">
                    Divisão dos valores
                  </Typography>
                }
                secondary={commissionsUsers.map((item) => (
                  <Stack direction="row" gap={2} alignItems="center" justifyContent="space-between">
                    <Typography
                      variant="body2"
                      color="text.secondary"
                      fontWeight="bold"
                      sx={{
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        whiteSpace: 'nowrap',
                      }}
                    >
                      {item.email}
                    </Typography>
                    <Divider
                      orientation="horizontal"
                      variant="fullWidth"
                      sx={{
                        flex: 1,
                        borderStyle: 'dashed',
                      }}
                    />
                    <Typography variant="body2" color="text.secondary">
                      {fCurrency(item.commissionValue)}
                    </Typography>
                  </Stack>
                ))}
              />

              <Paper
                variant="outlined"
                sx={{ p: 2, borderStyle: 'dashed', borderColor: 'text.secondary' }}
              >
                <Stack direction="row" spacing={1} mb={2} alignItems="center">
                  <Iconify icon="mdi:invoice" color="text.secondary" />
                  <Typography variant="body2" fontWeight="thin" color="text.secondary">
                    Seu recebimento
                  </Typography>
                </Stack>
                <Stack spacing={1}>
                  <ListItemText
                    primary={
                      <Typography variant="body2" fontWeight="bold">
                        Status
                      </Typography>
                    }
                    secondary={
                      <Label variant="soft" color={OrderStatus[sale.status]?.color || 'default'}>
                        {OrderStatus[sale.status]?.label || 'Indefinido'}
                      </Label>
                    }
                  />

                  <ListItemText
                    primary={
                      <Typography variant="body2" fontWeight="bold">
                        Valor
                      </Typography>
                    }
                    secondary={
                      <Typography variant="body2" color="text.secondary">
                        {fCurrency(loggedUserCommission.commissionValue)}
                      </Typography>
                    }
                  />

                  <ListItemText
                    primary={
                      <Typography variant="body2" fontWeight="bold">
                        Data estimada de liberação
                      </Typography>
                    }
                    secondary={
                      <Typography variant="body2" color="text.secondary">
                        {fDate(sale.releaseDate, 'dd/MM/yyyy')}
                      </Typography>
                    }
                  />
                </Stack>
              </Paper>
            </Stack>
          </TabPanel>
        </TabContext>
      </Box>
    </Drawer>
  );
}
