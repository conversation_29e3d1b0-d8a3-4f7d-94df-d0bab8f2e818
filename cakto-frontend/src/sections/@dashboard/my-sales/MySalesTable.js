import PropTypes from 'prop-types';
// @mui
import { IconButton, MenuItem, TableCell, Typography } from '@mui/material';
// components
import { LoadingButton } from '@mui/lab';
import { Stack } from '@mui/system';
import { useQueryClient } from '@tanstack/react-query';
import { useContext, useState, useMemo } from 'react';
import { useAuthContext } from '../../../auth/useAuthContext';
import ConfirmDialog from '../../../components/confirm-dialog';
import Iconify from '../../../components/iconify/Iconify';
import Label from '../../../components/label';
import MenuPopover from '../../../components/menu-popover/MenuPopover';
import SearchableTable from '../../../components/table/SearchableTable';
import { OrderPaymentMethod, OrderStatus, OrdersContext } from '../../../contexts/OrdersContext';
import { fCurrency } from '../../../utils/formatNumber';
import { fDate } from '../../../utils/formatTime';
import MySalesDrawer from './MySalesDrawer';
import MySalesFilter from './MySalesFilter';

// ----------------------------------------------------------------------

MySalesTable.propTypes = {};

const labels = [
  { id: 'date', label: 'Data' },
  { id: 'product', label: 'Produto' },
  { id: 'customer', label: 'Cliente' },
  { id: 'status', label: 'Status' },
  { id: 'value', label: 'Valor Líquido' },
  { id: 'actions', label: '' },
];

export default function MySalesTable() {
  const { orders, table, total, fetching, refund, resedOrderEmail } = useContext(OrdersContext);
  const queryClient = useQueryClient();

  const [openOrderDetails, setOpenOrderDetails] = useState(false);

  const [selectedOrder, setSelectedOrder] = useState({});

  const [anchorEl, setAnchorEl] = useState(null);

  const [confirmRefund, setConfirmRefund] = useState(false);

  const isRefunding = queryClient.isMutating({
    mutationKey: ['orders.refund'],
  });

  return (
    <>
      <MySalesFilter open={table.showFilter} onClose={() => table.setShowFilter(false)} />

      <MySalesDrawer
        sale={selectedOrder}
        open={openOrderDetails}
        onClose={() => setOpenOrderDetails(false)}
      />

      <ConfirmDialog
        title="Confirmação"
        content="Tem certeza que deseja reembolsar essa venda?"
        open={confirmRefund}
        onClose={() => {
          setConfirmRefund(false);
        }}
        action={
          <LoadingButton
            variant="contained"
            color="error"
            onClick={() => {
              refund(selectedOrder).then(() => {
                setConfirmRefund(false);
                setSelectedOrder({});
              });
            }}
            loading={isRefunding}
          >
            Reeembolsar
          </LoadingButton>
        }
      />

      <MenuPopover
        open={anchorEl}
        onClose={() => setAnchorEl(null)}
        sx={{ width: selectedOrder.status === 'paid' ? 270 : 160 }}
      >
        <MenuItem
          onClick={() => {
            setAnchorEl(null);
            setOpenOrderDetails(true);
          }}
        >
          Detalhes
        </MenuItem>
        <MenuItem
          onClick={() => {
            setAnchorEl(null);
            setSelectedOrder(selectedOrder);
            setConfirmRefund(true);
          }}
        >
          Reembolsar venda
        </MenuItem>
        {selectedOrder.status === 'paid' && (
          <MenuItem
            onClick={() => {
              setAnchorEl(null);
              resedOrderEmail(selectedOrder?.id);
            }}
          >
            Reenviar e-mail de compra aprovada
          </MenuItem>
        )}
      </MenuPopover>
      <SearchableTable
        count={total}
        fetching={fetching}
        labels={labels}
        rows={orders}
        table={table}
        filterable
        renderRow={(row) => (
          <MySalesTableRow
            row={row}
            onClick={(event) => {
              setSelectedOrder(row);
              setAnchorEl(event.currentTarget);
            }}
          />
        )}
        onRowClick={(row) => {
          setSelectedOrder(row);
          setOpenOrderDetails(true);
        }}
      />
    </>
  );
}

// ----------------------------------------------------------------------

MySalesTableRow.propTypes = {
  row: PropTypes.shape({
    createdAt: PropTypes.string,
    paymentMethod: PropTypes.string,
    subscription_period: PropTypes.number,
    commissions: PropTypes.array,
    product: PropTypes.shape({
      name: PropTypes.string,
    }),
    customer: PropTypes.shape({
      name: PropTypes.string,
      email: PropTypes.string,
    }),
    status: PropTypes.string,
    type: PropTypes.string,
    amount: PropTypes.string,
    installments: PropTypes.string,
    commissionedUsers: PropTypes.array,
  }),
  onClick: PropTypes.func,
};

function MySalesTableRow({ row, onClick }) {
  const { user: loggedUser } = useAuthContext();

  const userCommission = useMemo(() => {
    if (Object.keys(row)?.length) {
      const commissionedUsersMap = row?.commissionedUsers?.reduce((acc, user) => {
        acc[String(user.id)] = user.email;
        return acc;
      }, {});

      return row?.commissions
        ?.filter((commission) => commissionedUsersMap[String(commission.userId)])
        ?.map((commission) => ({
          email: commissionedUsersMap[String(commission.userId)],
          commissionValue: commission.commissionValue,
        }))
        ?.reduce((acc, { email, commissionValue, userId }) => {
          const existing = acc.find((item) => item.email === email);

          if (existing) {
            existing.commissionValue += commissionValue;
          } else {
            acc.push({
              email,
              commissionValue,
              userId,
            });
          }

          return acc;
        }, [])
        .reduce(
          (sum, user) => (user.email === loggedUser.email ? sum + user.commissionValue : sum),
          0
        );
    }

    return 0;
  }, [row, loggedUser]);

  return (
    <>
      <TableCell>{fDate(row?.createdAt, 'dd/MM/yyyy HH:mm')}</TableCell>

      <TableCell>{row?.product?.name || '-'}</TableCell>

      <TableCell>
        <Stack alignItems="start">
          <Typography variant="subtitle2"> {row?.customer?.name || '-'}</Typography>
          <Typography variant="body2" sx={{ color: 'text.secondary' }}>
            {row?.customer?.email || '-'}
          </Typography>
        </Stack>
      </TableCell>

      <TableCell>
        <Stack alignItems="start" gap={1}>
          <Label variant="soft" color={OrderStatus[row?.status]?.color || 'default'}>
            {OrderStatus[row?.status]?.label || '-'}
            {row?.status === 'refused' && '!'}
          </Label>
          <Stack gap={0.5}>
            {row.type === 'subscription' && row.subscription_period && (
              <Typography fontWeight="thin" variant="caption" sx={{ color: 'text.secondary' }}>
                Recorrência: {row.subscription_period}
              </Typography>
            )}
            {row?.paymentMethod === 'credit_card' ? (
              <Typography
                paddingRight={2}
                fontWeight="thin"
                variant="caption"
                sx={{ color: 'text.secondary' }}
              >
                {OrderPaymentMethod[row?.paymentMethod]?.label || '-'}: {`${row?.installments}x`}
              </Typography>
            ) : (
              <Typography fontWeight="thin" variant="caption" sx={{ color: 'text.secondary' }}>
                {OrderPaymentMethod[row?.paymentMethod]?.label || '-'}
              </Typography>
            )}
          </Stack>
        </Stack>
      </TableCell>

      <TableCell>{fCurrency(parseFloat(userCommission))}</TableCell>

      <TableCell width={40}>
        <IconButton
          onClick={(event) => {
            event.stopPropagation();
            onClick(event);
          }}
        >
          <Iconify icon="mi:options-vertical" />
        </IconButton>
      </TableCell>
    </>
  );
}
