# Integração com Área de Membros - Criação/Edição de Cursos

## Visão Geral

Este documento explica o processo de integração entre o sistema principal da Cakto e a área de membros (`aluno.cakto.com.br`) para gerenciamento de cursos.

## Problema Atual

Atualmente, quando um usuário clica no botão "Área de membros" ou na aba "members", o sistema sempre redireciona para a página de **criação** de curso, mesmo que o curso já exista. Isso pode causar:

- Duplicação de cursos
- Perda de dados existentes
- Experiência do usuário confusa

## Solução Proposta

### 1. Verificação de Curso Existente

Antes de redirecionar para a área de membros, o sistema deve:

1. **Verificar se já existe um curso** para o produto atual
2. **Se existir**: Redirecionar para a página de **edição** do curso
3. **Se não existir**: Redirecionar para a página de **criação** do curso

### 2. Estrutura da URL

#### Para Criação (curso não existe):
```
https://aluno.cakto.com.br/app/admin/courses/create?caktoProductId={productId}&caktoProductName={productName}
```

#### Para Edição (curso já existe):
```
https://aluno.cakto.com.br/app/admin/courses/{courseId}/edit?caktoProductId={productId}&caktoProductName={productName}
```

## Implementação Técnica

### 1. Serviço de Verificação

Criar um novo serviço para verificar se o curso existe:

```javascript
// src/services/members/course.js
export const checkCourseExists = async (productId) => {
  try {
    const response = await AuthenticatedHttp.get(`/members/courses/check/${productId}`);
    return {
      exists: response.data.exists,
      courseId: response.data.courseId || null
    };
  } catch (error) {
    console.error('Erro ao verificar curso:', error);
    return { exists: false, courseId: null };
  }
};
```

### 2. Hook Personalizado

Criar um hook para gerenciar a lógica de redirecionamento:

```javascript
// src/hooks/useMembersAreaRedirect.js
import { useMutation } from '@tanstack/react-query';
import { checkCourseExists } from '@/services/members/course';

export const useMembersAreaRedirect = () => {
  const { mutateAsync: checkCourse, isLoading } = useMutation({
    mutationFn: checkCourseExists,
  });

  const redirectToMembersArea = async (productId, productName) => {
    try {
      const { exists, courseId } = await checkCourse(productId);
      
      const base = process.env.REACT_APP_MEMBERS_V2_BASE_URL || 'https://aluno.cakto.com.br';
      const encodedProductName = encodeURIComponent(productName || '');
      
      let target;
      if (exists && courseId) {
        // Curso existe - redirecionar para edição
        target = `${base}/app/admin/courses/${courseId}/edit?caktoProductId=${productId}&caktoProductName=${encodedProductName}`;
      } else {
        // Curso não existe - redirecionar para criação
        target = `${base}/app/admin/courses/create?caktoProductId=${productId}&caktoProductName=${encodedProductName}`;
      }
      
      window.open(target, '_blank');
    } catch (error) {
      console.error('Erro ao redirecionar para área de membros:', error);
      // Fallback para criação em caso de erro
      const base = process.env.REACT_APP_MEMBERS_V2_BASE_URL || 'https://aluno.cakto.com.br';
      const encodedProductName = encodeURIComponent(productName || '');
      const target = `${base}/app/admin/courses/create?caktoProductId=${productId}&caktoProductName=${encodedProductName}`;
      window.open(target, '_blank');
    }
  };

  return {
    redirectToMembersArea,
    isLoading
  };
};
```

### 3. Atualização dos Componentes

#### EditProduct.js
```javascript
import { useMembersAreaRedirect } from '@/hooks/useMembersAreaRedirect';

export default function EditProduct() {
  const { redirectToMembersArea, isLoading: isRedirecting } = useMembersAreaRedirect();
  
  // No handler da aba members
  if (newTab === 'members') {
    const MEMBERS_V2_TESTERS_IDS = [];
    const MEMBERS_V2_TESTERS_EMAILS = ['<EMAIL>'];

    const isV2Tester =
      (user?.id && MEMBERS_V2_TESTERS_IDS.includes(String(user.id))) ||
      (user?.email && MEMBERS_V2_TESTERS_EMAILS.includes(String(user.email)));

    if (isV2Tester) {
      redirectToMembersArea(id, product?.name);
      return;
    }
    
    navigate(`${PATH_DASHBOARD.general.products.membersArea(id)}?tab=content`);
  }
}
```

#### ProductMembersArea.js
```javascript
import { useMembersAreaRedirect } from '@/hooks/useMembersAreaRedirect';

export default function ProductMembersArea() {
  const { redirectToMembersArea, isLoading: isRedirecting } = useMembersAreaRedirect();
  
  // No botão "Área de membros"
  <Button
    variant="contained"
    color="primary"
    startIcon={<Iconify icon="eva:external-link-fill" />}
    onClick={() => redirectToMembersArea(product?.id, product?.name)}
    disabled={isRedirecting}
  >
    {isRedirecting ? 'Carregando...' : 'Área de membros'}
  </Button>
}
```

## Estrutura da API

### Endpoint de Verificação

```
GET /members/courses/check/{productId}
```

#### Response (200):
```json
{
  "exists": true,
  "courseId": "course-uuid-here",
  "productId": "product-uuid-here"
}
```

#### Response (404):
```json
{
  "exists": false,
  "courseId": null,
  "productId": "product-uuid-here"
}
```

## Fluxo de Funcionamento

```mermaid
graph TD
    A[Usuário clica em 'Área de membros'] --> B[Verificar se curso existe]
    B --> C{Curso existe?}
    C -->|Sim| D[Redirecionar para edição]
    C -->|Não| E[Redirecionar para criação]
    D --> F[URL: /courses/{courseId}/edit]
    E --> G[URL: /courses/create]
    F --> H[Usuário edita curso existente]
    G --> I[Usuário cria novo curso]
```

## Benefícios

1. **Evita duplicação**: Não cria cursos duplicados
2. **Preserva dados**: Mantém configurações existentes
3. **UX melhorada**: Usuário vai direto para o curso correto
4. **Fallback seguro**: Em caso de erro, ainda funciona

## Considerações de Segurança

1. **Autenticação**: Verificar se o usuário tem permissão para acessar o curso
2. **Validação**: Confirmar se o `productId` pertence ao usuário
3. **Rate limiting**: Evitar muitas requisições simultâneas

## Testes Necessários

1. **Teste de curso existente**: Verificar redirecionamento para edição
2. **Teste de curso inexistente**: Verificar redirecionamento para criação
3. **Teste de erro de API**: Verificar fallback para criação
4. **Teste de permissões**: Verificar acesso negado quando apropriado

## Próximos Passos

1. [ ] Implementar o serviço `checkCourseExists`
2. [ ] Criar o hook `useMembersAreaRedirect`
3. [ ] Atualizar os componentes `EditProduct` e `ProductMembersArea`
4. [ ] Implementar o endpoint na API
5. [ ] Testar o fluxo completo
6. [ ] Documentar para a equipe
