# Cakto Frontend - Integração com Members Area V2

## Contexto

Este documento orienta o agente da `cakto-frontend` sobre como implementar a integração correta com a `members-area` (aluno.cakto.com.br) para evitar duplicação de cursos e melhorar a experiência do usuário.

## Problema Atual

### URLs que estão sendo enviadas:
```
https://aluno.cakto.com.br/app/admin/courses/create?caktoProductId=1cc0849a-805c-4f7d-84fd-25aea5502535&caktoProductName=Curso%20com%20area%20de%20membros
```

### Problema:
- Usuários sempre vão para `/courses/create`
- Se o curso já existe, isso pode causar duplicação
- Experiência ruim para o usuário

## Solução Implementada na Members Area

### 1. Endpoint de Verificação (Já Implementado)

**URL**: `GET https://aluno.cakto.com.br/api/courses/check/{productId}`

**Parâmetros**:
- `productId` (UUID): ID do produto no sistema principal

**Response (200) - Curso existe**:
```json
{
  "exists": true,
  "courseId": "course-uuid-here",
  "productId": "1cc0849a-805c-4f7d-84fd-25aea5502535",
  "courseName": "Nome do curso existente"
}
```

**Response (404) - Curso não existe**:
```json
{
  "exists": false,
  "courseId": null,
  "productId": "1cc0849a-805c-4f7d-84fd-25aea5502535"
}
```

### 2. Redirecionamento Automático (Já Implementado)

A página `/app/admin/courses/create` agora:
1. Verifica automaticamente se o curso já existe
2. Se existir: redireciona para `/app/admin/courses/{courseId}/edit`
3. Se não existir: continua com a criação

## Implementação Necessária no Cakto Frontend

### 1. Função de Verificação de Curso

```javascript
// services/members/course-check.js
export const checkCourseExists = async (productId) => {
  try {
    const response = await fetch(`https://aluno.cakto.com.br/api/courses/check/${productId}`);

    if (response.ok) {
      const data = await response.json();
      return {
        exists: data.exists,
        courseId: data.courseId,
        courseName: data.courseName,
        productId: data.productId
      };
    } else if (response.status === 404) {
      const data = await response.json();
      return {
        exists: false,
        courseId: null,
        productId: data.productId
      };
    } else {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
  } catch (error) {
    console.error('Erro ao verificar existência do curso:', error);
    // Fallback: assume que não existe para permitir criação
    return {
      exists: false,
      courseId: null,
      productId: productId
    };
  }
};
```

### 2. Função de Redirecionamento Inteligente

```javascript
// services/members/course-redirect.js
export const redirectToMembersArea = async (productId, productName) => {
  try {
    // Verificar se curso já existe
    const courseCheck = await checkCourseExists(productId);

    // Construir URL base
    const baseUrl = 'https://aluno.cakto.com.br';
    const params = new URLSearchParams();
    params.set('caktoProductId', productId);
    params.set('caktoProductName', encodeURIComponent(productName));

    let targetUrl;

    if (courseCheck.exists && courseCheck.courseId) {
      // Curso existe - redirecionar para edição
      targetUrl = `${baseUrl}/app/admin/courses/${courseCheck.courseId}/edit?${params.toString()}`;
      console.log(`Curso "${courseCheck.courseName}" já existe. Redirecionando para edição.`);
    } else {
      // Curso não existe - redirecionar para criação
      targetUrl = `${baseUrl}/app/admin/courses/create?${params.toString()}`;
      console.log('Curso não existe. Redirecionando para criação.');
    }

    // Abrir em nova aba
    window.open(targetUrl, '_blank');

    return {
      success: true,
      url: targetUrl,
      exists: courseCheck.exists,
      courseId: courseCheck.courseId
    };

  } catch (error) {
    console.error('Erro no redirecionamento para Members Area:', error);

    // Fallback: sempre redirecionar para criação
    const baseUrl = 'https://aluno.cakto.com.br';
    const params = new URLSearchParams();
    params.set('caktoProductId', productId);
    params.set('caktoProductName', encodeURIComponent(productName));

    const fallbackUrl = `${baseUrl}/app/admin/courses/create?${params.toString()}`;
    window.open(fallbackUrl, '_blank');

    return {
      success: false,
      url: fallbackUrl,
      error: error.message
    };
  }
};
```

### 3. Integração nos Componentes Existentes

#### Atualizar ProductMembersArea.js

```javascript
// pages/dashboard/ProductMembersArea.js
import { redirectToMembersArea } from '@/services/members/course-redirect';

// ... código existente ...

const handleOpenMembersArea = async (product) => {
  try {
    setIsLoading(true);

    const result = await redirectToMembersArea(
      product.id,
      product.name
    );

    if (result.success) {
      toast.success(
        result.exists
          ? `Redirecionando para edição do curso "${result.courseName}"`
          : 'Redirecionando para criação do curso'
      );
    } else {
      toast.error('Erro ao abrir Members Area. Tente novamente.');
    }

  } catch (error) {
    console.error('Erro ao abrir Members Area:', error);
    toast.error('Erro ao abrir Members Area. Tente novamente.');
  } finally {
    setIsLoading(false);
  }
};

// ... resto do código ...
```

#### Atualizar EditProduct.js

```javascript
// pages/dashboard/EditProduct.js
import { redirectToMembersArea } from '@/services/members/course-redirect';

// ... código existente ...

const handleMembersAreaAction = async () => {
  if (!product) return;

  try {
    setIsLoadingMembersArea(true);

    const result = await redirectToMembersArea(
      product.id,
      product.name
    );

    if (result.success) {
      toast.success(
        result.exists
          ? `Redirecionando para edição do curso`
          : 'Redirecionando para criação do curso'
      );
    } else {
      toast.error('Erro ao abrir Members Area');
    }

  } catch (error) {
    console.error('Erro ao abrir Members Area:', error);
    toast.error('Erro ao abrir Members Area');
  } finally {
    setIsLoadingMembersArea(false);
  }
};

// ... resto do código ...
```

### 4. Hook Personalizado (Opcional)

```javascript
// hooks/useMembersArea.js
import { useState } from 'react';
import { redirectToMembersArea } from '@/services/members/course-redirect';

export const useMembersArea = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  const openMembersArea = async (productId, productName) => {
    setIsLoading(true);
    setError(null);

    try {
      const result = await redirectToMembersArea(productId, productName);
      return result;
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  return {
    openMembersArea,
    isLoading,
    error
  };
};
```

## Estrutura de URLs Finais

### Para Criação (curso não existe):
```
https://aluno.cakto.com.br/app/admin/courses/create?caktoProductId={productId}&caktoProductName={productName}
```

### Para Edição (curso já existe):
```
https://aluno.cakto.com.br/app/admin/courses/{courseId}/edit?caktoProductId={productId}&caktoProductName={productName}
```

## Testes Necessários

### Cenários de Teste:

1. **Curso não existe**:
   - Chamar `redirectToMembersArea(productId, productName)`
   - Resultado: abre `/app/admin/courses/create` com parâmetros

2. **Curso existe**:
   - Chamar `redirectToMembersArea(productId, productName)`
   - Resultado: abre `/app/admin/courses/{courseId}/edit` com parâmetros

3. **Erro de API**:
   - Simular indisponibilidade do endpoint
   - Resultado: fallback para criação

4. **Parâmetros inválidos**:
   - Chamar com `productId` inválido
   - Resultado: fallback para criação

## Configuração de Ambiente

### Variáveis de Ambiente Necessárias:

```env
# URL da Members Area
REACT_APP_MEMBERS_AREA_URL=https://aluno.cakto.com.br

# Timeout para requisições (opcional)
REACT_APP_API_TIMEOUT=5000
```

### Configuração de CORS:

A Members Area já está configurada para aceitar requisições do domínio da Cakto Frontend.

## Tratamento de Erros

### Estratégia de Fallback:

1. **Erro de rede**: Redirecionar para criação
2. **Timeout**: Redirecionar para criação
3. **Erro 500**: Redirecionar para criação
4. **Erro 404**: Continuar com criação (curso não existe)

### Logs e Monitoramento:

```javascript
// Adicionar logs para monitoramento
console.log('Members Area - Iniciando verificação:', { productId, productName });
console.log('Members Area - Resultado:', result);
console.log('Members Area - Erro:', error);
```

## Benefícios da Implementação

1. **Evita Duplicação**: Usuários não criam cursos duplicados
2. **Melhora UX**: Redirecionamento automático para a ação correta
3. **Mantém Contexto**: Parâmetros são preservados na URL
4. **Fallback Seguro**: Sempre funciona, mesmo com erros
5. **Logs Úteis**: Facilita debugging e monitoramento

## Próximos Passos

1. [ ] Implementar `checkCourseExists` e `redirectToMembersArea`
2. [ ] Atualizar componentes que abrem Members Area
3. [ ] Adicionar tratamento de erros e loading states
4. [ ] Testar todos os cenários
5. [ ] Deploy em produção
6. [ ] Monitorar logs e métricas

## Contato

Para dúvidas sobre a implementação ou problemas de integração, entre em contato com a equipe de desenvolvimento da Members Area.

---

**Importante**: Esta implementação é crítica para evitar duplicação de cursos e melhorar a experiência do usuário. Priorize a implementação das funções de verificação e redirecionamento.
