import type { Config } from "./types";

export const config = {
	appName: "Cakto Members",
	i18n: {
		enabled: true,
		locales: {
			pt: {
				currency: "BRL",
				label: "Português",
			}
		},
		defaultLocale: "pt",
		defaultCurrency: "BRL",
		localeCookieName: "NEXT_LOCALE",
	},
	organizations: {
		enable: true,
		hideOrganization: false,
		enableUsersToCreateOrganizations: true,
		requireOrganization: false,
		forbiddenOrganizationSlugs: [
			"new-organization",
			"admin",
			"settings",
			"organization-invitation",
		],
	},
	users: {
		enableOnboarding: true,
	},
	auth: {
		enableSignup: true,
		enableMagicLink: true,
		enableSocialLogin: true,
		enablePasskeys: false,
		enablePasswordLogin: true,
		enableTwoFactor: false,
		redirectAfterSignIn: "/app",
		redirectAfterLogout: "/",
		sessionCookieMaxAge: 60 * 60 * 24 * 30,
	},
	mails: {
		from: "<EMAIL>",
	},
	ui: {
		enabledThemes: ["dark"],
		defaultTheme: "dark",
		saas: {
			enabled: true,
			useSidebarLayout: false,
		},
	},
	storage: {
		bucketNames: {
			avatars: process.env.NEXT_PUBLIC_AVATARS_BUCKET_NAME ?? "avatars",
		},
	},
	contactForm: {
		enabled: true,
		to: "<EMAIL>",
		subject: "Contato do formulário de contato",
	},
	domains: {
		baseDomain: "aluno.cakto.com.br",
		allowCustomDomains: true,
		allowSubdomains: true,
		primaryDomains: [
			"caktomembers.cloud.cakto.app",
			"members-base.vercel.app",
			"aluno.cakto.com.br",
			"localhost:3000",
		],
	},
} as const satisfies Config;

export type { Config };
