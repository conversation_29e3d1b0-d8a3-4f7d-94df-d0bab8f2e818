{"dependencies": {"@repo/database": "workspace:*", "@repo/logs": "workspace:*", "@repo/auth": "workspace:*", "@repo/utils": "workspace:*"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@repo/tsconfig": "workspace:*", "@types/node": "^22.15.30", "nanoid": "^5.1.5", "tsx": "^4.19.4"}, "name": "@repo/scripts", "private": true, "scripts": {"create:user": "dotenv -c -e ../../.env -- tsx ./src/create-user.ts", "create:users:batch": "dotenv -c -e ../../.env -- tsx ./src/create-users-batch.ts", "debug:user:access": "dotenv -c -e ../../.env -- tsx ./src/debug-user-access.ts", "debug:course": "dotenv -c -e ../../.env -- tsx ./src/debug-course.ts", "type-check": "tsc --noEmit"}, "version": "0.0.0"}