@theme {
	--color-background: var(--background);
	--color-foreground: var(--foreground);
	--color-card: var(--card);
	--color-card-foreground: var(--card-foreground);
	--color-popover: var(--popover);
	--color-popover-foreground: var(--popover-foreground);
	--color-primary: var(--primary);
	--color-primary-foreground: var(--primary-foreground);
	--color-secondary: var(--secondary);
	--color-secondary-foreground: var(--secondary-foreground);
	--color-muted: var(--muted);
	--color-muted-foreground: var(--muted-foreground);
	--color-accent: var(--accent);
	--color-accent-foreground: var(--accent-foreground);
	--color-success: var(--success);
	--color-success-foreground: var(--success-foreground);
	--color-destructive: var(--destructive);
	--color-destructive-foreground: var(--destructive-foreground);
	--color-border: var(--border);
	--color-input: var(--input);
	--color-ring: var(--ring);
	--color-highlight: var(--highlight);
	--color-highlight-foreground: var(--highlight-foreground);
	--radius-lg: var(--radius);
	--radius-md: calc(var(--radius) - 2px);
	--radius-sm: calc(var(--radius) - 4px);

	--font-sans: var(--font-geist-sans);

	--animation-accordion-down: accordion-down 0.2s ease-out;
	--animation-accordion-up: accordion-up 0.2s ease-out;

	@keyframes accordion-down {
		from {
			height: 0;
		}

		to {
			height: var(--radix-accordion-content-height);
		}
	}

	@keyframes accordion-up {
		from {
			height: var(--radix-accordion-content-height);
		}

		to {
			height: 0;
		}
	}
}

@layer base {
	:root {
		--border: #e3ebf6;
		--input: #c7ced8;
		--ring: #0F7864;
		--background: #fafafe;
		--foreground: #292b35;
		--primary: #0F7864;
		--primary-foreground: #000000;
		--secondary: #292b35;
		--secondary-foreground: #ffffff;
		--destructive: #ef4444;
		--destructive-foreground: #ffffff;
		--success: #39a561;
		--success-foreground: #ffffff;
		--muted: #f8fafc;
		--muted-foreground: #64748b;
		--accent: #ddddea;
		--accent-foreground: #292b35;
		--popover: #ffffff;
		--popover-foreground: #292b35;
		--card: linear-gradient(135deg, #0f7864 0%, #151b22 100%);
		--card-foreground: #e9eef3;
		--card-border: #0f7864;
		--highlight: #e5a158;
		--highlight-foreground: #ffffff;
		--radius: 0.75rem;

		/* Container variables for responsive design */
		--container-sm: 640px;
		--container-md: 768px;
		--container-lg: 1024px;
		--container-xl: 1280px;
		--container-2xl: 1536px;
		--container-3xl: 1792px;
		--container-4xl: 2048px;
		--container-5xl: 2304px;
		--container-6xl: 2560px;
		--container-7xl: 1400px;
	}

	.dark {
		--border: #2b303d;
		--input: #4c5362;
		--ring: #0F7864;
		--background: #0E1318;
		--foreground: #e9eef3;
		--primary: #0F7864;
		--primary-foreground: #000000;
		--secondary: #e9eef3;
		--secondary-foreground: #091521;
		--destructive: #ef4444;
		--destructive-foreground: #ffffff;
		--success: #39a561;
		--success-foreground: #ffffff;
		--muted: #020817;
		--muted-foreground: #94a3b8;
		--accent: #1e293b;
		--accent-foreground: #f8fafc;
		--popover: #0d1116;
		--popover-foreground: #e9eef3;
		--card: linear-gradient(135deg, #0f7864 0%, #151b22 100%);
		--card-foreground: #e9eef3;
		--card-border: #0f7864;
		--highlight: #e5a158;
		--highlight-foreground: #ffffff;

		/* Container variables for responsive design (same as light theme) */
		--container-sm: 640px;
		--container-md: 768px;
		--container-lg: 1024px;
		--container-xl: 1280px;
		--container-2xl: 1536px;
		--container-3xl: 1792px;
		--container-4xl: 2048px;
		--container-5xl: 2304px;
		--container-6xl: 2560px;
		--container-7xl: 1400px;
	}

	*,
	::after,
	::before,
	::backdrop,
	::file-selector-button {
		@apply border-border;
	}

	/* Custom scrollbar styles */
	.course-scrollbar::-webkit-scrollbar {
		width: 8px;
	}

	.course-scrollbar::-webkit-scrollbar-track {
		background: transparent;
		border-radius: 4px;
	}

	.course-scrollbar::-webkit-scrollbar-thumb {
		background: rgba(0, 0, 0, 0.5);
		border-radius: 4px;
	}

	.course-scrollbar::-webkit-scrollbar-thumb:hover {
		background: rgba(0, 0, 0, 0.7);
	}

	.sidebar-scrollbar::-webkit-scrollbar {
		width: 6px;
	}

	.sidebar-scrollbar::-webkit-scrollbar-track {
		background: transparent;
	}

	.sidebar-scrollbar::-webkit-scrollbar-thumb {
		background: rgba(0, 0, 0, 0.5);
		border-radius: 3px;
	}

	.sidebar-scrollbar::-webkit-scrollbar-thumb:hover {
		background: rgba(0, 0, 0, 0.7);
	}

	/* Course sidebar specific styles for consistent layout */
	.course-sidebar {
		width: 384px;
		/* 24rem - fixed width for consistency */
		flex-shrink: 0;
	}

	.course-sidebar .lesson-item {
		width: 100%;
	}

	.course-sidebar .lesson-thumbnail {
		width: 48px;
		/* 3rem - fixed width */
		height: 32px;
		/* 2rem - fixed height */
		flex-shrink: 0;
	}

	.course-sidebar .lesson-title {
		flex: 1;
		min-width: 0;
		width: calc(100% - 48px - 12px);
		/* 100% - thumbnail width - gap */
	}

	/* Mobile-specific course sidebar styles */
	@media (max-width: 640px) {
		.course-sidebar {
			width: 100% !important;
			max-width: 100% !important;
		}

		.course-sidebar .lesson-thumbnail {
			width: 40px;
			/* Smaller on mobile */
			height: 24px;
		}

		.course-sidebar .lesson-title {
			width: calc(100% - 40px - 8px);
			/* Adjust for smaller thumbnail */
		}
	}

	/* Tablet-specific course sidebar styles */
	@media (min-width: 641px) and (max-width: 1023px) {
		.course-sidebar {
			width: 400px !important;
			max-width: 400px !important;
		}
	}

	.minimal-scrollbar::-webkit-scrollbar {
		width: 4px;
	}

	.minimal-scrollbar::-webkit-scrollbar-track {
		background: transparent;
	}

	.minimal-scrollbar::-webkit-scrollbar-thumb {
		background: rgba(0, 0, 0, 0.5);
		border-radius: 2px;
	}

	.minimal-scrollbar::-webkit-scrollbar-thumb:hover {
		background: rgba(0, 0, 0, 0.7);
	}
}

@utility container {
	margin-inline: auto;
	padding-inline: 1rem;
	width: 100%;
	max-width: var(--container-7xl);
}

@utility no-scrollbar {
	&::-webkit-scrollbar {
		display: none;
	}

	-ms-overflow-style: none;
	scrollbar-width: none;
}
