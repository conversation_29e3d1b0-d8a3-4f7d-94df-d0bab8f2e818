datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

generator client {
  provider = "prisma-client-js"
}

generator zod {
  provider         = "zod-prisma-types"
  output           = "./zod"
  createInputTypes = true
  addIncludeType   = false
  addSelectType    = false
}

generator json {
  provider = "prisma-json-types-generator"
}

model User {
  id                 String       @id @default(cuid())
  mainAppUserId      String?      @unique
  name               String
  email              String
  emailVerified      Boolean
  image              String?
  createdAt          DateTime
  updatedAt          DateTime
  username           String?
  role               String?
  banned             Boolean?
  banReason          String?
  banExpires         DateTime?
  onboardingComplete Boolean      @default(false)
  paymentsCustomerId String?
  locale             String?
  twoFactorEnabled   Boolean?
  password           String?
  sessions           Session[]
  accounts           Account[]
  passkeys           Passkey[]
  invitations        Invitation[]
  purchases          Purchase[]
  members            Member[]
  twofactors         TwoFactor[]

  createdCourses       Courses[]              @relation("CourseCreator")
  createdVitrines      Vitrine[]              @relation("VitrineCreator")
  courseBannerButton   CourseBannerButton[]
  lessonCommentReplies LessonCommentReplies[]
  lessonComments       LessonComments[]
  userCourses          UserCourses[]
  userPurchases        UserPurchase[]
  userSystemColors     UserSystemColors?
  userWatchedLessons   UserWatchedLessons[]
  apikeys              Apikey[]

  displayUsername String?

  @@unique([email])
  @@unique([username])
  @@map("user")
}

model Session {
  id        String   @id @default(cuid())
  expiresAt DateTime
  ipAddress String?
  userAgent String?
  userId    String
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  impersonatedBy        String?
  activeOrganizationId  String?
  defaultOrganizationId String?
  mainAppUserId         String?

  token     String
  createdAt DateTime
  updatedAt DateTime

  @@unique([token])
  @@map("session")
}

model Account {
  id           String    @id @default(cuid())
  accountId    String
  providerId   String
  userId       String
  user         User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  accessToken  String?   @db.Text
  refreshToken String?   @db.Text
  idToken      String?   @db.Text
  expiresAt    DateTime?
  password     String?

  accessTokenExpiresAt  DateTime?
  refreshTokenExpiresAt DateTime?
  scope                 String?
  createdAt             DateTime
  updatedAt             DateTime

  @@unique([userId, providerId])
  @@map("account")
}

model Verification {
  id         String   @id @default(cuid())
  identifier String
  value      String   @db.Text
  expiresAt  DateTime

  createdAt DateTime?
  updatedAt DateTime?

  @@map("verification")
}

model Passkey {
  id           String    @id @default(cuid())
  name         String?
  publicKey    String
  userId       String
  user         User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  credentialID String
  counter      Int
  deviceType   String
  backedUp     Boolean
  transports   String?
  createdAt    DateTime?

  @@map("passkey")
}

model TwoFactor {
  id          String @id @default(cuid())
  secret      String
  backupCodes String
  userId      String
  user        User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("twoFactor")
}

model Organization {
  id                 String       @id @default(cuid())
  name               String
  slug               String?
  logo               String?
  createdAt          DateTime
  metadata           String?
  paymentsCustomerId String?
  members            Member[]
  invitations        Invitation[]
  purchases          Purchase[]

  courses            Courses[]
  vitrines           Vitrine[]
  memberAreaSettings MemberAreaSettings?

  subdomain    String? @unique
  customDomain String? @unique

  @@unique([slug])
  @@map("organization")
}

model Member {
  id             String           @id @default(cuid())
  organizationId String
  organization   Organization     @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  userId         String
  user           User             @relation(fields: [userId], references: [id], onDelete: Cascade)
  role           roleOrganization @default(member)
  createdAt      DateTime

  @@unique([organizationId, userId])
  @@map("member")
}

model Invitation {
  id             String            @id @default(cuid())
  organizationId String
  organization   Organization      @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  email          String
  role           roleOrganization?
  status         String
  expiresAt      DateTime
  inviterId      String
  user           User              @relation(fields: [inviterId], references: [id], onDelete: Cascade)

  @@map("invitation")
}

enum PurchaseType {
  SUBSCRIPTION
  ONE_TIME
}

model Purchase {
  id             String        @id @default(cuid())
  organization   Organization? @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  organizationId String?
  user           User?         @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId         String?
  type           PurchaseType
  customerId     String
  subscriptionId String?       @unique
  productId      String
  status         String?
  createdAt      DateTime      @default(now())
  updatedAt      DateTime      @updatedAt

  @@index([subscriptionId])
  @@map("purchase")
}

// =============================================================================
// MODELOS LMS - CURSOS
// =============================================================================

model Courses {
  id              String                 @id @default(cuid())
  name            String
  description     String?
  logo            String?
  community       String?
  link            String?
  createdAt       DateTime               @default(now())
  updatedAt       DateTime               @updatedAt
  organizationId  String
  createdBy       String?
  courseBanner    CourseBanner[]
  courseModules   CourseModules[]
  organization    Organization           @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  creator         User?                  @relation("CourseCreator", fields: [createdBy], references: [id], onDelete: SetNull)
  userCourses     UserCourses[]
  vitrineSections VitrineSectionCourse[]
  caktoProducts   CourseProduct[]

  @@map("courses")
}

model CourseProduct {
  id               String   @id @default(cuid())
  courseId         String
  caktoProductId   String
  caktoProductName String?
  createdAt        DateTime @default(now())
  updatedAt        DateTime @updatedAt
  course           Courses  @relation(fields: [courseId], references: [id], onDelete: Cascade)

  @@unique([courseId, caktoProductId])
  @@unique([courseId])
  @@unique([caktoProductId])
  @@map("courseProducts")
}

model CourseBanner {
  id                  Int                  @id @default(autoincrement())
  courseId            String
  title               String
  description         String?
  image               String?
  position            Int
  createdAt           DateTime             @default(now())
  updatedAt           DateTime             @updatedAt
  course              Courses              @relation(fields: [courseId], references: [id])
  courseBannerButtons CourseBannerButton[]

  @@map("courseBanner")
}

model CourseBannerButton {
  id           Int          @id @default(autoincrement())
  bannerId     Int
  title        String
  link         String
  color        String
  userId       String
  createdAt    DateTime     @default(now())
  updatedAt    DateTime     @updatedAt
  courseBanner CourseBanner @relation(fields: [bannerId], references: [id])
  user         User         @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("courseBannerButton")
}

model CourseModules {
  id        Int      @id @default(autoincrement())
  courseId  String
  moduleId  String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  course    Courses  @relation(fields: [courseId], references: [id])
  module    Modules  @relation(fields: [moduleId], references: [id])

  @@map("courseModules")
}

model Modules {
  id            String          @id @default(cuid())
  name          String
  position      Int
  cover         String?
  createdAt     DateTime        @default(now())
  updatedAt     DateTime        @updatedAt
  courseModules CourseModules[]
  lessons       Lessons[]

  @@map("modules")
}

model UserCourses {
  id        Int       @id @default(autoincrement())
  userId    String
  courseId  String
  finalTime DateTime?
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  course    Courses   @relation(fields: [courseId], references: [id])
  user      User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, courseId])
  @@map("userCourses")
}

// =============================================================================
// MODELOS LMS - AULAS
// =============================================================================

model Lessons {
  id                 String               @id @default(cuid())
  name               String
  description        String?
  videoUrl           String?
  position           Int
  moduleId           String
  thumbnail          String?
  duration           String?
  createdAt          DateTime             @default(now())
  updatedAt          DateTime             @updatedAt
  externalLink       String?
  lessonComments     LessonComments[]
  lessonFiles        LessonFiles[]
  module             Modules              @relation(fields: [moduleId], references: [id])
  userWatchedLessons UserWatchedLessons[]

  @@map("lessons")
}

model UserWatchedLessons {
  id          Int      @id @default(autoincrement())
  userId      String
  lessonId    String
  rating      Int?
  isCompleted Boolean  @default(false)
  currentTime String?
  duration    String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  lesson      Lessons  @relation(fields: [lessonId], references: [id])
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, lessonId])
  @@map("userWatchedLessons")
}

model LessonFiles {
  id        Int      @id @default(autoincrement())
  lessonId  String
  name      String
  url       String
  type      String
  size      Int
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  lesson    Lessons  @relation(fields: [lessonId], references: [id], onDelete: Cascade)

  @@map("lessonFiles")
}

model LessonComments {
  id                   Int                    @id @default(autoincrement())
  lessonId             String
  userId               String
  comment              String
  createdAt            DateTime               @default(now())
  updatedAt            DateTime               @updatedAt
  lessonCommentReplies LessonCommentReplies[]
  lesson               Lessons                @relation(fields: [lessonId], references: [id])
  user                 User                   @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("lessonComments")
}

model LessonCommentReplies {
  id            Int            @id @default(autoincrement())
  commentId     Int
  userId        String
  replyComment  String
  createdAt     DateTime       @default(now())
  updatedAt     DateTime       @updatedAt
  lessonComment LessonComments @relation(fields: [commentId], references: [id])
  user          User           @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("lessonCommentReplies")
}

// =============================================================================
// MODELOS LMS - VITRINE
// =============================================================================

model Vitrine {
  id             String            @id @default(cuid())
  title          String
  description    String?
  status         VitrineStatus     @default(DRAFT)
  visibility     VitrineVisibility @default(PUBLIC)
  bannerImage    String?
  isDefault      Boolean           @default(false)
  createdAt      DateTime          @default(now())
  updatedAt      DateTime          @updatedAt
  organizationId String
  createdBy      String?
  sections       VitrineSection[]
  views          VitrineView[]
  settings       VitrineSettings?
  organization   Organization      @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  creator        User?             @relation("VitrineCreator", fields: [createdBy], references: [id], onDelete: SetNull)

  @@map("vitrines")
}

model VitrineSection {
  id               String                 @id @default(cuid())
  vitrineId        String
  title            String
  subtitle         String?
  description      String?
  position         Int
  isLocked         Boolean                @default(false)
  requiresPurchase Boolean                @default(false)
  checkoutUrl      String?
  webhookUrl       String?
  price            Decimal?               @db.Decimal(10, 2)
  originalPrice    Decimal?               @db.Decimal(10, 2)
  accessType       AccessType             @default(FREE)
  visibility       VitrineVisibility      @default(PUBLIC)
  createdAt        DateTime               @default(now())
  updatedAt        DateTime               @updatedAt
  purchases        UserPurchase[]
  courses          VitrineSectionCourse[]
  vitrine          Vitrine                @relation(fields: [vitrineId], references: [id], onDelete: Cascade)

  @@map("vitrineSections")
}

model VitrineSectionCourse {
  id        String         @id @default(cuid())
  sectionId String
  courseId  String
  position  Int
  createdAt DateTime       @default(now())
  course    Courses        @relation(fields: [courseId], references: [id], onDelete: Cascade)
  section   VitrineSection @relation(fields: [sectionId], references: [id], onDelete: Cascade)

  @@unique([sectionId, courseId])
  @@map("vitrineSectionCourses")
}

model UserPurchase {
  id             String         @id @default(cuid())
  userId         String
  sectionId      String
  status         PurchaseStatus @default(PENDING)
  paymentId      String?
  checkoutUrl    String?
  amount         Decimal        @db.Decimal(10, 2)
  paymentMethod  String?
  paymentDate    DateTime?
  expirationDate DateTime?
  webhookData    Json?
  createdAt      DateTime       @default(now())
  updatedAt      DateTime       @updatedAt
  section        VitrineSection @relation(fields: [sectionId], references: [id], onDelete: Cascade)
  user           User           @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, sectionId])
  @@map("userPurchases")
}

model VitrineView {
  id        String   @id @default(cuid())
  vitrineId String
  userId    String?
  ipAddress String?
  userAgent String?
  createdAt DateTime @default(now())
  vitrine   Vitrine  @relation(fields: [vitrineId], references: [id], onDelete: Cascade)

  @@map("vitrineViews")
}

// =============================================================================
// MODELOS LMS - CONFIGURAÇÃO
// =============================================================================

model MemberAreaSettings {
  id              String       @id @default(cuid())
  memberAreaName  String?
  primaryColor    String?
  logoUrl         String?
  commentsEnabled Boolean      @default(false)
  supportEmail    String?
  menuItems       Json
  footer          Json
  organizationId  String       @unique
  organization    Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  createdAt       DateTime     @default(now())
  updatedAt       DateTime     @updatedAt

  @@map("memberAreaSettings")
}

model VitrineSettings {
  id        String   @id @default(cuid())
  vitrineId String   @unique
  vitrine   Vitrine  @relation(fields: [vitrineId], references: [id], onDelete: Cascade)
  menuItems Json
  theme     Json?
  footer    Json?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("vitrineSettings")
}

model UserSystemColors {
  id                       Int      @id @default(autoincrement())
  userId                   String   @unique
  primaryColor             String
  secondaryColor           String
  primaryBackgroundColor   String
  secondaryBackgroundColor String
  createdAt                DateTime @default(now())
  updatedAt                DateTime @updatedAt
  user                     User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("userSystemColors")
}

model Apikey {
  id                  String    @id
  name                String?
  start               String?
  prefix              String?
  key                 String
  userId              String
  refillInterval      Int?
  refillAmount        Int?
  lastRefillAt        DateTime?
  enabled             Boolean?
  rateLimitEnabled    Boolean?
  rateLimitTimeWindow Int?
  rateLimitMax        Int?
  requestCount        Int?
  remaining           Int?
  lastRequest         DateTime?
  expiresAt           DateTime?
  createdAt           DateTime  @default(now())
  updatedAt           DateTime  @updatedAt
  permissions         String?
  metadata            String?
  user                User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("apikey")
}

// =============================================================================
// ENUMS
// =============================================================================

enum VitrineStatus {
  DRAFT
  PUBLISHED
  ARCHIVED
}

enum VitrineVisibility {
  PUBLIC
  PRIVATE
}

enum AccessType {
  FREE
  PAID
  MEMBER_ONLY
}

enum PurchaseStatus {
  PENDING
  COMPLETED
  FAILED
  REFUNDED
}

enum roleOrganization {
  owner
  admin
  member
  producer
}
