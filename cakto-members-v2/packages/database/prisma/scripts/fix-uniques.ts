import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

async function dedupeMemberAreaSettings() {
  const all = await prisma.memberAreaSettings.findMany({
    orderBy: { createdAt: "asc" },
  });
  const seen = new Set<string>();
  const toDelete: string[] = [];
  for (const row of all) {
    if (!row.organizationId) continue;
    if (seen.has(row.organizationId)) {
      toDelete.push(row.id);
    } else {
      seen.add(row.organizationId);
    }
  }
  if (toDelete.length > 0) {
    await prisma.memberAreaSettings.deleteMany({ where: { id: { in: toDelete } } });
  }
}

async function dedupeCourseProducts() {
  const all = await prisma.courseProduct.findMany({ orderBy: { createdAt: "asc" } });
  const seenProduct = new Set<string>();
  const seenCourse = new Set<string>();
  const toDelete: string[] = [];
  for (const row of all) {
    let duplicate = false;
    if (row.caktoProductId && seenProduct.has(row.caktoProductId)) duplicate = true;
    if (row.courseId && seenCourse.has(row.courseId)) duplicate = true;
    if (duplicate) {
      toDelete.push(row.id);
      continue;
    }
    if (row.caktoProductId) seenProduct.add(row.caktoProductId);
    if (row.courseId) seenCourse.add(row.courseId);
  }
  if (toDelete.length > 0) {
    await prisma.courseProduct.deleteMany({ where: { id: { in: toDelete } } });
  }
}

async function dedupeUserCourses() {
  const all = await prisma.userCourses.findMany({ orderBy: { createdAt: "asc" } });
  const seen = new Set<string>();
  const toDelete: string[] = [];
  for (const row of all) {
    const key = `${row.userId}-${row.courseId}`;
    if (seen.has(key)) {
      toDelete.push(row.id);
    } else {
      seen.add(key);
    }
  }
  if (toDelete.length > 0) {
    await prisma.userCourses.deleteMany({ where: { id: { in: toDelete } } });
  }
}

async function main() {
  await dedupeMemberAreaSettings();
  await dedupeCourseProducts();
  await dedupeUserCourses();
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });


