import { config } from "@repo/config";
import {
	db,
	getInvitationById,
	getPurchasesByOrganizationId,
	getPurchasesByUserId,
} from "@repo/database";
import { getUserByEmail } from "@repo/database";
import type { Locale } from "@repo/i18n";
import { logger } from "@repo/logs";
import { sendEmail } from "@repo/mail";
import { getBaseUrl } from "@repo/utils";
import { betterAuth } from "better-auth";
import { prismaAdapter } from "better-auth/adapters/prisma";
import {
	admin,
	createAuthMiddleware,
	genericOAuth,
	magicLink,
	openAPI,
	organization,
	twoFactor,
	username,
} from "better-auth/plugins";
import { passkey } from "better-auth/plugins/passkey";
import { parse as parseCookies } from "cookie";
import { updateSeatsInOrganizationSubscription } from "./lib/organization";
import { invitationOnlyPlugin } from "./plugins/invitation-only";

const getLocaleFromRequest = (request?: Request) => {
	const cookies = parseCookies(request?.headers.get("cookie") ?? "");
	return (
		(cookies[config.i18n.localeCookieName] as Locale) ??
		config.i18n.defaultLocale
	);
};

const appUrl = getBaseUrl();

logger.info("SSO Configuration Debug", {
	caktoClientId: process.env.CAKTO_CLIENT_ID ? "present" : "missing",
	caktoClientSecret: process.env.CAKTO_CLIENT_SECRET ? "present" : "missing",
	appUrl,
	redirectURI: `${appUrl}/api/auth/oauth2/callback/django-sso`,
	authorizationUrl: "https://sso.cakto.com.br/oauth/authorize/",
	tokenUrl: "https://sso.cakto.com.br/oauth/token/",
	userInfoUrl: "https://sso.cakto.com.br/oauth/userinfo/",
	scopes: ["openid", "user", "offers", "products"],
	authentication: "post",
	pkce: true,
});

export const auth = betterAuth({
	baseURL: appUrl,
	trustedOrigins: [appUrl, "http://localhost:3000"],
	appName: config.appName,
	database: prismaAdapter(db, {
		provider: "postgresql",
	}),
	advanced: {
		database: {
			generateId: false,
		},
	},
	session: {
		expiresIn: config.auth.sessionCookieMaxAge,
		freshAge: 0,
	},
	user: {
		additionalFields: {
			onboardingComplete: {
				type: "boolean",
				required: false,
			},
			locale: {
				type: "string",
				required: false,
			},
		},
		deleteUser: {
			enabled: true,
		},
		changeEmail: {
			enabled: true,
			sendChangeEmailVerification: async (
				{ user: { email, name }, url },
				request
			) => {
				const locale = getLocaleFromRequest(request);
				await sendEmail({
					to: email,
					templateId: "emailVerification",
					context: {
						url,
						name,
					},
					locale,
				});
			},
		},
	},
	account: {
		accountLinking: {
			enabled: true,
			trustedProviders: ["google", "github"],
		},
	},
	emailAndPassword: {
		enabled: true,
		requireEmailVerification: false,
		autoSignIn: true,
	},
	email: {
		from: config.mails.from,
		server: {
			host: process.env.SMTP_HOST!,
			port: Number(process.env.SMTP_PORT!),
			auth: {
				user: process.env.SMTP_USER!,
				pass: process.env.SMTP_PASS!,
			},
		},
		verification: {
			enabled: true,
			sendVerificationEmail: async (
				{
					user: { email, name },
					url,
				}: { user: { email: string; name: string }; url: string },
				request: Request
			) => {
				const locale = getLocaleFromRequest(request);
				await sendEmail({
					to: email,
					templateId: "emailVerification",
					context: {
						url,
						name,
					},
					locale,
				});
			},
		},
	},
	hooks: {
		before: createAuthMiddleware(async (ctx) => {
			// Log todas as requisições OAuth2 para debug
			if (ctx.path?.includes("/oauth2")) {
				logger.info("OAuth2 Request Debug", {
					path: ctx.path,
					method: ctx.request?.method,
					url: ctx.request?.url,
					body: ctx.body,
					headers: ctx.request?.headers ? Object.fromEntries(ctx.request.headers.entries()) : {},
					searchParams: ctx.request?.url ? Object.fromEntries(new URL(ctx.request.url).searchParams.entries()) : {},
				});
			}

			if (
				ctx.path.startsWith("/delete-user") ||
				ctx.path.startsWith("/organization/delete")
			) {
				const userId = ctx.context.session?.session.userId;
				const { organizationId } = ctx.body;

				if (userId || organizationId) {
					const purchases = organizationId
						? await getPurchasesByOrganizationId(organizationId)
						: await getPurchasesByUserId(userId!);

					if (purchases.length > 0) {
						throw new Error(
							"Cannot delete user/organization with active purchases"
						);
					}
				}
			}
		}),
		after: createAuthMiddleware(async (ctx) => {
			if (ctx.path.startsWith("/organization/accept-invitation")) {
				const { invitationId } = ctx.body;

				if (!invitationId) {
					return;
				}

				const invitation = await getInvitationById(invitationId);

				if (!invitation) {
					return;
				}

				await updateSeatsInOrganizationSubscription(
					invitation.organizationId
				);
			} else if (ctx.path.startsWith("/organization/remove-member")) {
				const { organizationId } = ctx.body;

				if (!organizationId) {
					return;
				}

				await updateSeatsInOrganizationSubscription(organizationId);
			}
		}),

	},
	socialProviders: {
		...(process.env.GOOGLE_CLIENT_ID && process.env.GOOGLE_CLIENT_SECRET
			? {
					google: {
						clientId: process.env.GOOGLE_CLIENT_ID,
						clientSecret: process.env.GOOGLE_CLIENT_SECRET,
						scope: ["email", "profile"],
					},
			  }
			: {}),
	},
	plugins: [
		username(),
		admin(),
		passkey(),
		magicLink({
			disableSignUp: false,
			sendMagicLink: async ({ email, url }, request) => {
				const locale = getLocaleFromRequest(request);
				await sendEmail({
					to: email,
					templateId: "magicLink",
					context: {
						url,
					},
					locale,
				});
			},
		}),
		organization({
			sendInvitationEmail: async (
				{ email, id, organization },
				request
			) => {
				const locale = getLocaleFromRequest(request);
				const existingUser = await getUserByEmail(email);

				const url = new URL(
					existingUser ? "/auth/login" : "/auth/signup",
					getBaseUrl()
				);

				url.searchParams.set("invitationId", id);
				url.searchParams.set("email", email);

				await sendEmail({
					to: email,
					templateId: "organizationInvitation",
					locale,
					context: {
						organizationName: organization.name,
						url: url.toString(),
					},
				});
			},
		}),
		genericOAuth({
			config: [
				{
					providerId: "django-sso",
					clientId: process.env.CAKTO_CLIENT_ID || "",
					clientSecret: process.env.CAKTO_CLIENT_SECRET || "",
					authorizationUrl: "https://sso.cakto.com.br/oauth/authorize/",
					tokenUrl: "https://sso.cakto.com.br/oauth/token/",
					userInfoUrl: "https://sso.cakto.com.br/oauth/userinfo/",
					scopes: ["openid", "user", "offers", "products"],
					pkce: true,
					responseType: "code",
					authentication: "post",
					redirectURI: `${getBaseUrl()}/api/auth/oauth2/callback/django-sso`,
					mapProfileToUser: (profile: any) => ({
						email: profile.email,
						name:
							profile.first_name && profile.last_name
								? `${profile.first_name} ${profile.last_name}`
								: profile.email,
						emailVerified: true,
						mainAppUserId: profile.sub || profile.id,
						status: profile.status,
					}),
				},
			],
		}),
		openAPI(),
		invitationOnlyPlugin(),
		twoFactor(),
	],
	onAPIError: {
		onError(error: any, ctx: any) {
			const isOAuth2Request = ctx?.request?.url?.includes("/oauth2");

			logger.error("Better Auth API Error", {
				error: error?.message || error,
				path: ctx?.request?.url,
				method: ctx?.request?.method,
				headers: ctx?.request?.headers
					? Object.fromEntries(ctx.request.headers.entries())
					: {},
				body: ctx?.body,
				stack: error?.stack,
				status: error?.status,
				statusText: error?.statusText,
				code: error?.code,
				isOAuth2Request,
				oauthError: isOAuth2Request ? {
					url: ctx?.request?.url,
					searchParams: ctx?.request?.url ? Object.fromEntries(new URL(ctx.request.url).searchParams.entries()) : {},
					requestBody: ctx?.body,
					requestMethod: ctx?.request?.method,
					errorStatus: error?.status,
					errorStatusText: error?.statusText,
					errorMessage: error?.message,
					errorCode: error?.code,
					errorResponse: error?.response,
				} : undefined,
				errorDetails: {
					status: error?.status,
					statusText: error?.statusText,
					message: error?.message,
					code: error?.code,
				},
			});

			// Log específico para erro 405 em OAuth2
			if (isOAuth2Request && error?.status === 405) {
				logger.error("OAuth2 405 Method Not Allowed Error", {
					url: ctx?.request?.url,
					method: ctx?.request?.method,
					body: ctx?.body,
					headers: ctx?.request?.headers ? Object.fromEntries(ctx.request.headers.entries()) : {},
					error: error?.message,
					stack: error?.stack,
				});
			}
		},
	},
	onSuccess: async (ctx: any) => {
		// Handle magic link verification
		if (ctx.request?.url?.includes("/magic-link/verify")) {
			const url = new URL(ctx.request.url);
			const callbackURL = url.searchParams.get("callbackURL");
			const error = url.searchParams.get("error");

			logger.info("Magic link verification attempt", {
				url: ctx.request.url,
				callbackURL,
				error,
				hasToken: !!url.searchParams.get("token"),
				baseUrl: getBaseUrl(),
			});

			if (error) {
				logger.error("Magic link verification failed", { error });
				return ctx.redirect(
					`${getBaseUrl()}/auth/login?error=${encodeURIComponent(
						error
					)}`
				);
			}

			if (callbackURL) {
				const absoluteCallbackURL = callbackURL.startsWith("http")
					? callbackURL
					: `${getBaseUrl()}${callbackURL}`;
				return ctx.redirect(absoluteCallbackURL);
			}

			return ctx.redirect(
				`${getBaseUrl()}${config.auth.redirectAfterSignIn}`
			);
		}

		// Handle OAuth2 callback
		if (ctx.request?.url?.includes("/oauth2/callback")) {
			const url = new URL(ctx.request.url);
			const allParams = Object.fromEntries(url.searchParams.entries());

			logger.info("OAuth2 callback received", {
				url: ctx.request.url,
				method: ctx.request.method,
				baseUrl: getBaseUrl(),
				params: allParams,
				headers: Object.fromEntries(
					ctx.request.headers?.entries() || []
				),
				session: ctx.context?.session,
			});

			// Extract callback URL from query params
			const callbackURL = url.searchParams.get("callbackURL");
			const error = url.searchParams.get("error");
			const code = url.searchParams.get("code");
			const state = url.searchParams.get("state");
			const codeVerifier = url.searchParams.get("code_verifier");

			logger.info("OAuth2 callback details", {
				callbackURL,
				error,
				code: code ? "present" : "missing",
				state: state ? "present" : "missing",
				codeVerifier: codeVerifier ? "present" : "missing",
				errorDescription: url.searchParams.get("error_description"),
				providerId: "django-sso",
				discoveryUrl: "https://sso.cakto.com.br/oauth/.well-known/openid-configuration",
			});

			// Log detalhado para debug do processo OAuth2
			logger.info("OAuth2 Debug Info", {
				hasCode: !!code,
				hasState: !!state,
				hasCodeVerifier: !!codeVerifier,
				hasError: !!error,
				codeLength: code?.length || 0,
				stateLength: state?.length || 0,
				codeVerifierLength: codeVerifier?.length || 0,
				callbackURL: callbackURL || "not set",
			});

			if (error) {
				logger.error("OAuth2 callback failed", {
					error,
					errorDescription: url.searchParams.get("error_description"),
					errorUri: url.searchParams.get("error_uri"),
				});
				return ctx.redirect(
					`${getBaseUrl()}/auth/login?error=${encodeURIComponent(
						error
					)}`
				);
			}

			if (callbackURL) {
				const absoluteCallbackURL = callbackURL.startsWith("http")
					? callbackURL
					: `${getBaseUrl()}${callbackURL}`;
				logger.info("Redirecting to callback URL", {
					absoluteCallbackURL,
				});
				return ctx.redirect(absoluteCallbackURL);
			}

			logger.info("Redirecting to default after sign in", {
				redirectTo: config.auth.redirectAfterSignIn,
			});
			return ctx.redirect(
				`${getBaseUrl()}${config.auth.redirectAfterSignIn}`
			);
		}
	},
});

export * from "./lib/organization";

export type Session = typeof auth.$Infer.Session;

export type ActiveOrganization = NonNullable<
	Awaited<ReturnType<typeof auth.api.getFullOrganization>>
>;

export type Organization = typeof auth.$Infer.Organization;

export type OrganizationMemberRole =
	ActiveOrganization["members"][number]["role"];

export type OrganizationInvitationStatus = typeof auth.$Infer.Invitation.status;

export type OrganizationMetadata = Record<string, unknown> | undefined;
