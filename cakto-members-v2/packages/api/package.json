{"dependencies": {"@hono/node-server": "^1.12.0", "@repo/auth": "workspace:*", "@repo/config": "workspace:*", "@repo/database": "workspace:*", "@repo/i18n": "workspace:*", "@repo/logs": "workspace:*", "@repo/mail": "workspace:*", "@repo/storage": "workspace:*", "@repo/utils": "workspace:*", "@scalar/hono-api-reference": "^0.9.3", "@sindresorhus/slugify": "^2.2.1", "hono": "^4.7.11", "hono-openapi": "^0.4.8", "nanoid": "^5.1.5", "openai": "^5.1.1", "openapi-merge": "^1.3.3", "use-intl": "^4.1.0", "zod": "^3.25.55"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@repo/tsconfig": "workspace:*", "@types/react": "19.1.6", "encoding": "^0.1.13", "prisma": "^6.9.0", "tsx": "^4.19.2", "typescript": "5.8.3"}, "main": "./index.ts", "name": "@repo/api", "scripts": {"type-check": "tsc --noEmit"}, "version": "0.0.0"}