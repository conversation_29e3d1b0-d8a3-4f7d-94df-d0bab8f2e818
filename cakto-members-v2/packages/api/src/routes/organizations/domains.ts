import { Hono } from "hono";
import { validator } from "hono-openapi/zod";
import { z } from "zod";
import { describeRoute } from "hono-openapi";
import { authMiddleware } from "../../middleware/auth";
import { db } from "@repo/database";
import { config } from "@repo/config";

const updateDomainSchema = z.object({
  organizationId: z.string(),
  subdomain: z.string().optional(),
  customDomain: z.string().optional(),
});

export const domainsRouter = new Hono()
  .use(authMiddleware)
  .get(
    "/domains/check",
    validator(
      "query",
      z.object({
        subdomain: z
          .string()
          .min(3)
          .max(63)
          .regex(/^[a-z0-9-]+$/),
      }),
    ),
    describeRoute({
      summary: "Check subdomain availability",
      tags: ["Organizations"],
    }),
    async (c) => {
      const { subdomain } = c.req.valid("query");

      const reserved = new Set([
        "www",
        "mail",
        "admin",
        "api",
        ...config.organizations.forbiddenOrganizationSlugs,
      ]);

      if (reserved.has(subdomain)) {
        return c.json({ available: false, reason: "reserved" });
      }

      const exists = await db.organization.findFirst({
        where: { subdomain },
        select: { id: true },
      });

      return c.json({ available: !exists });
    },
  )
  .patch("/domains", validator("json", updateDomainSchema),
		describeRoute({
			summary: "Update organization domains",
			tags: ["Organizations"],
		}),
		async (c) => {
    const { organizationId, subdomain, customDomain } = c.req.valid("json");

    const organization = await db.organization.update({
      where: { id: organizationId },
      data: {
        subdomain,
        customDomain,
      },
    });

    return c.json(organization);
  });
