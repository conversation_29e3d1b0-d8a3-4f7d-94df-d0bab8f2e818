import { db } from "@repo/database";
import { Hono } from "hono";
import { z } from "zod";
import { validator } from "hono-openapi/zod";
import { describeRoute } from "hono-openapi";
import { authMiddleware } from "../../middleware/auth";
import { adminMiddleware } from "../../middleware/admin";
import { createUser, createUserAccount, getUserByEmail } from "@repo/database";

const grantTesterAccessSchema = z.object({
	userIds: z.array(z.string()).min(1, "At least one user ID is required"),
	courseId: z.string().optional(),
	organizationId: z.string().optional(),
});

export const grantTesterAccess = new Hono()
	.use(authMiddleware)
	.use(adminMiddleware)
	.post("/grant-tester-access",
		describeRoute({
			summary: "Grant tester access to users",
			tags: ["Admin"],
			description: "Grant access to users based on Cakto user IDs. Users will be created if they don't exist and added to the specified organization/course.",
		}),
		async (c) => {
		try {
			const user = c.get("user");

			let data;
			try {
				const rawData = await c.req.json();
				data = grantTesterAccessSchema.parse(rawData);
			} catch (error) {
				if (error instanceof z.ZodError) {
					return c.json({
						error: "Validation failed",
						details: error.errors.map(e => ({
							field: e.path.join('.'),
							message: e.message
						}))
					}, 400);
				}
				return c.json({ error: "Invalid request data" }, 400);
			}

			const { userIds, courseId, organizationId } = data;

			// Get tester organization (default organization for testers)
			const testerOrganization = await db.organization.findFirst({
				where: {
					slug: "cakto-members",
				},
			});

			if (!testerOrganization) {
				return c.json({ error: "Tester organization not found" }, 404);
			}

			const results = [];
			const errors = [];

			for (const caktoUserId of userIds) {
				try {
					// Check if user already exists by mainAppUserId
					let user = await db.user.findFirst({
						where: {
							mainAppUserId: caktoUserId,
						},
					});

					if (!user) {
						// Create a placeholder user (will be updated when they first access)
						user = await createUser({
							name: `Tester ${caktoUserId.slice(0, 8)}`,
							email: `tester-${caktoUserId.slice(0, 8)}@cakto.com`,
							mainAppUserId: caktoUserId,
						});

						// Create user account for authentication
						await createUserAccount(user.id);
					}

					// Add user to tester organization if not already a member
					const existingMembership = await db.member.findFirst({
						where: {
							userId: user.id,
							organizationId: testerOrganization.id,
						},
					});

					if (!existingMembership) {
						await db.member.create({
							data: {
								userId: user.id,
								organizationId: testerOrganization.id,
								role: "member",
							},
						});
					}

					// If courseId is provided, grant course access
					if (courseId) {
						const course = await db.courses.findFirst({
							where: { id: courseId },
						});

						if (course) {
							// Check if user already has access to this course
							const existingCourseAccess = await db.userCourses.findFirst({
								where: {
									userId: user.id,
									courseId: courseId,
								},
							});

							if (!existingCourseAccess) {
								await db.userCourses.create({
									data: {
										userId: user.id,
										courseId: courseId,
									},
								});
							}
						}
					}

					// If organizationId is provided, add user to that organization too
					if (organizationId && organizationId !== testerOrganization.id) {
						const targetOrganization = await db.organization.findFirst({
							where: { id: organizationId },
						});

						if (targetOrganization) {
							const existingTargetMembership = await db.member.findFirst({
								where: {
									userId: user.id,
									organizationId: organizationId,
								},
							});

							if (!existingTargetMembership) {
								await db.member.create({
									data: {
										userId: user.id,
										organizationId: organizationId,
										role: "member",
									},
								});
							}
						}
					}

					results.push({
						caktoUserId,
						userId: user.id,
						email: user.email,
						status: "success",
						message: "Access granted successfully",
					});

				} catch (error) {
					console.error(`Error granting access to user ${caktoUserId}:`, error);
					errors.push({
						caktoUserId,
						status: "error",
						message: error instanceof Error ? error.message : "Unknown error",
					});
				}
			}

			return c.json({
				success: true,
				message: `Processed ${userIds.length} users`,
				data: {
					results,
					errors,
					summary: {
						total: userIds.length,
						successful: results.length,
						failed: errors.length,
					},
				},
			});

		} catch (error) {
			console.error("Error granting tester access:", error);
			return c.json({ error: "Internal server error" }, 500);
		}
	});
