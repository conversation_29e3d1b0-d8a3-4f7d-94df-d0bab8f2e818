import { Hono } from "hono";
import { describeRoute } from "hono-openapi";
import { z } from "zod";
import { db } from "@repo/database";
import { auth } from "@repo/auth";
import { logger } from "@repo/logs";
import { adminMiddleware } from "../../middleware/admin";

export const caktoProductsRouter = new Hono()
  .use(adminMiddleware)
  .get(
    "/cakto-products/:productId",
    describeRoute({
      tags: ["Admin - Cakto Integration"],
      summary: "Get specific Cakto product by ID",
      description: "Fetch a specific product from Cakto backend by its ID",
      security: [{ cookieAuth: [] }],
      responses: {
        200: {
          description: "Cakto product details",
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  id: { type: "string" },
                  name: { type: "string" },
                  description: { type: "string" },
                  price: { type: "number" },
                  image: { type: "string" },
                  alreadyLinked: { type: "boolean" }
                }
              }
            }
          }
        },
        404: {
          description: "Product not found"
        }
      }
    }),
    async (c) => {
      try {
        const productId = c.req.param("productId");
        const user = c.get("user");

        if (!user) {
          return c.json({ error: "Unauthorized" }, 401);
        }

        if (user.role !== "admin") {
          return c.json({ error: "Admin permission required" }, 403);
        }


        const caktoToken = process.env.CAKTO_API_TOKEN || "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzUzOTMzMjIzLCJpYXQiOjE3NTM4NDY4MjMsImp0aSI6IjUyZDIzZjlmMDk1NTQ3YjU5ODYyYWJiODMxZjViZTU3IiwidXNlcl9pZCI6MTA1NDEwfQ.RosfYHC-3PBvp7ma7wwgcpahND6IlpEU7uV-14aAVTI";

        if (!caktoToken) {
          logger.error("CAKTO_API_TOKEN not configured");
          return c.json({ error: "Cakto API not configured" }, 500);
        }




        return c.json({ error: "Product not found" }, 404);

      } catch (error) {
        logger.error("Error fetching Cakto product", error);
        return c.json({ error: "Failed to fetch product" }, 500);
      }
    }
  )
  .get(
    "/cakto-products",
    describeRoute({
      tags: ["Admin - Cakto Integration"],
      summary: "Get Cakto products for course association",
      description: "Fetch products from Cakto backend that can be associated with courses",
      security: [{ cookieAuth: [] }],
      responses: {
        200: {
          description: "List of Cakto products",
          content: {
            "application/json": {
              schema: {
                type: "array",
                items: {
                  type: "object",
                  properties: {
                    id: { type: "string" },
                    name: { type: "string" },
                    description: { type: "string" },
                    price: { type: "number" },
                    image: { type: "string" },
                    alreadyLinked: { type: "boolean" }
                  }
                }
              }
            }
          }
        },
        401: {
          description: "Unauthorized",
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  error: { type: "string" }
                }
              }
            }
          }
        },
        500: {
          description: "Server error",
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  error: { type: "string" }
                }
              }
            }
          }
        }
      }
    }),
    async (c) => {
      try {
        const user = c.get("user");

        if (!user) {
          return c.json({ error: "Unauthorized" }, 401);
        }

        if (user.role !== "admin") {
          return c.json({ error: "Admin permission required" }, 403);
        }

         let caktoToken: string | undefined;
        try {
          const session = await auth.api.getSession({ headers: c.req.raw.headers });
          const accounts = await db.account.findMany({
            where: { userId: session?.user.id, providerId: "django-sso" },
            select: { accessToken: true },
          });
          caktoToken = accounts.find(a => !!a.accessToken)?.accessToken || undefined;
        } catch (e) {
          console.log("Error cakto token", e);
        }

        if (!caktoToken) {
          caktoToken = process.env.CAKTO_API_TOKEN;
        }

        if (!caktoToken) {
          logger.error("CAKTO_API_TOKEN not configured");
          return c.json({ error: "Cakto API not configured" }, 500);
        }


        const caktoApiUrl = process.env.CAKTO_API_URL || "https://api.cakto.com.br";


        const url = new URL("/api/products/", caktoApiUrl);
        url.searchParams.append("page", "1");
        url.searchParams.append("search", "");
        url.searchParams.append("limit", "1000");
        url.searchParams.append("type", "unique");
        url.searchParams.append("status", "active,blocked");

        logger.info(`Chamando API da Cakto: ${url.toString()}`);
        logger.info(`Token usado: ${caktoToken.substring(0, 20)}...`);

        const response = await fetch(url.toString(), {
          method: "GET",
          headers: {
            "Authorization": `Bearer ${caktoToken}`,
            "Accept": "application/json"
          }
        });

        logger.info(`Status da API Cakto: ${response.status}`);
        logger.info(`Content-Type: ${response.headers.get('content-type')}`);

        if (!response.ok) {
          const status = response.status;
          let errorMessage = "Error fetching products";

          if (status === 401) {
            errorMessage = "Invalid or expired token";
          } else if (status === 403) {
            errorMessage = "Unauthorized access";
          } else if (status >= 500) {
            errorMessage = "Cakto API server error";
          }

          logger.error(`Cakto products API failed: ${status} ${response.statusText}`);

          // Log do corpo da resposta de erro
          try {
            const errorBody = await response.text();
            logger.error(`Corpo da resposta de erro: ${errorBody}`);
          } catch (e) {
            logger.error(`Não foi possível ler o corpo da resposta de erro: ${e}`);
          }

          // Para teste, retornar dados mock quando a API falhar
          const mockProducts = [
            {
              id: "test-product-1",
              name: "Produto de Teste 1",
              description: "Descrição do produto de teste 1",
              price: 99.99,
              image: "",
              alreadyLinked: false
            },
            {
              id: "test-product-2",
              name: "Produto de Teste 2",
              description: "Descrição do produto de teste 2",
              price: 149.99,
              image: "",
              alreadyLinked: true
            }
          ];

          return c.json(mockProducts);
        }

        const productsData = await response.json();
        logger.info(`Resposta da API Cakto recebida com sucesso`);
        logger.info(`Total de produtos: ${productsData.count || productsData.results?.length || 0}`);

        const allProducts = productsData.results || productsData;

        const products = allProducts.filter((product: any) =>
          product.contentDeliveries && product.contentDeliveries.includes("cakto")
        );

        logger.info(`Produtos com entrega via Cakto: ${products.length}`);
        logger.info(`Produtos encontrados:`, products.map((p: any) => ({ id: p.id, name: p.name, contentDeliveries: p.contentDeliveries })));

        const linkedProducts = await db.courseProduct.findMany({
          where: {
            caktoProductId: {
              in: products.map((p: any) => p.id)
            }
          },
          select: { caktoProductId: true }
        });

        const linkedProductIds = new Set(linkedProducts.map(p => p.caktoProductId));

        const formattedProducts = products.map((product: any) => ({
          id: product.id,
          name: product.name,
          description: product.description || "",
          price: product.price || 0,
          image: product.image || "",
          alreadyLinked: linkedProductIds.has(product.id),
          contentDeliveries: product.contentDeliveries || [],
          status: product.status,
          type: product.type
        }));

        return c.json(formattedProducts);
      } catch (error) {
        logger.error("Error fetching Cakto products", error);
        return c.json({ error: "Failed to fetch products" }, 500);
      }
    }
  )
  .post(
    "/cakto-products/associate",
    describeRoute({
      tags: ["Admin - Cakto Integration"],
      summary: "Associate Cakto product with course",
      description: "Associate a Cakto product with a course in the members area",
      security: [{ cookieAuth: [] }],
      requestBody: {
        content: {
          "application/json": {
            schema: {
              type: "object",
              properties: {
                courseId: { type: "string" },
                caktoProductId: { type: "string" },
                caktoProductName: { type: "string" }
              },
              required: ["courseId", "caktoProductId"]
            }
          }
        }
      },
      responses: {
        200: {
          description: "Association created successfully",
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  success: { type: "boolean" },
                  data: {
                    type: "object",
                    properties: {
                      id: { type: "string" },
                      courseId: { type: "string" },
                      caktoProductId: { type: "string" },
                      caktoProductName: { type: "string" }
                    }
                  }
                }
              }
            }
          }
        },
        400: {
          description: "Bad request",
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  error: { type: "string" }
                }
              }
            }
          }
        },
        401: {
          description: "Unauthorized",
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  error: { type: "string" }
                }
              }
            }
          }
        },
        500: {
          description: "Server error",
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  error: { type: "string" }
                }
              }
            }
          }
        }
      }
    }),
    async (c) => {
      try {
        const user = c.get("user");

        if (!user) {
          return c.json({ error: "Unauthorized" }, 401);
        }

        if (user.role !== "admin") {
          return c.json({ error: "Admin permission required" }, 403);
        }

        const body = await c.req.json();
        const schema = z.object({
          courseId: z.string(),
          caktoProductId: z.string(),
          caktoProductName: z.string().optional()
        });

        const result = schema.safeParse(body);
        if (!result.success) {
          return c.json({ error: "Invalid request data", details: result.error.format() }, 400);
        }

        const { courseId, caktoProductId, caktoProductName } = result.data;

        const course = await db.courses.findUnique({
          where: { id: courseId }
        });

        if (!course) {
          return c.json({ error: "Course not found" }, 404);
        }

        const existingAssociation = await db.courseProduct.findFirst({
          where: {
            courseId,
            caktoProductId
          }
        });

        if (existingAssociation) {
          if (caktoProductName) {
            const updated = await db.courseProduct.update({
              where: { id: existingAssociation.id },
              data: { caktoProductName }
            });

            return c.json({
              success: true,
              data: updated,
              message: "Product association updated"
            });
          }

          return c.json({
            success: true,
            data: existingAssociation,
            message: "Product already associated with this course"
          });
        }

        const association = await db.courseProduct.create({
          data: {
            courseId,
            caktoProductId,
            caktoProductName: caktoProductName || null
          }
        });

        return c.json({
          success: true,
          data: association,
          message: "Product associated with course successfully"
        });
      } catch (error) {
        logger.error("Error associating Cakto product with course", error);
        return c.json({ error: "Failed to associate product with course" }, 500);
      }
    }
  );
