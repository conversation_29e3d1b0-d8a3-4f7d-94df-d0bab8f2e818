import { Hono } from "hono";
import { describeRoute } from "hono-openapi";
import { validator } from "hono-openapi/zod";
import { z } from "zod";
import { db } from "@repo/database";
import { logger } from "@repo/logs";
import { adminMiddleware } from "../../middleware/admin";

const associateProductSchema = z.object({
  courseId: z.string().min(1, "Course ID é obrigatório"),
  caktoProductId: z.string().min(1, "Cakto Product ID é obrigatório"),
  caktoProductName: z.string().optional(),
});

const updateAssociationSchema = z.object({
  caktoProductName: z.string().min(1, "Nome do produto é obrigatório"),
});

export const courseProductAssociationRouter = new Hono()
  .use(adminMiddleware)
  .get(
    "/",
    describeRoute({
      tags: ["Admin - Course Management"],
      summary: "List all course-product associations",
      description: "Get all associations between courses and Cakto products",
      security: [{ cookieAuth: [] }],
      responses: {
        200: {
          description: "List of associations",
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  success: { type: "boolean" },
                  data: {
                    type: "array",
                    items: {
                      type: "object",
                      properties: {
                        id: { type: "string" },
                        courseId: { type: "string" },
                        caktoProductId: { type: "string" },
                        caktoProductName: { type: "string" },
                        courseName: { type: "string" },
                        createdAt: { type: "string" },
                        updatedAt: { type: "string" }
                      }
                    }
                  },
                  total: { type: "number" }
                }
              }
            }
          }
        }
      }
    }),
    async (c) => {
      try {
        const associations = await db.courseProduct.findMany({
          include: {
            course: {
              select: {
                name: true
              }
            }
          },
          orderBy: {
            createdAt: 'desc'
          }
        });

        const formattedAssociations = associations.map(assoc => ({
          id: assoc.id,
          courseId: assoc.courseId,
          caktoProductId: assoc.caktoProductId,
          caktoProductName: assoc.caktoProductName,
          courseName: assoc.course.name,
          createdAt: assoc.createdAt.toISOString(),
          updatedAt: assoc.updatedAt.toISOString()
        }));

        logger.info(`Retrieved ${associations.length} course-product associations`);

        return c.json({
          success: true,
          data: formattedAssociations,
          total: associations.length
        });

      } catch (error) {
        logger.error("Error retrieving course-product associations:", error);
        return c.json({
          success: false,
          error: "Failed to retrieve associations"
        }, 500);
      }
    }
  )
  .post(
    "/associate",
    validator("json", associateProductSchema),
    describeRoute({
      tags: ["Admin - Course Management"],
      summary: "Associate Cakto product with course",
      description: "Create or update association between a Cakto product and a course",
      security: [{ cookieAuth: [] }],
      requestBody: {
        content: {
          "application/json": {
            schema: {
              type: "object",
              required: ["courseId", "caktoProductId"],
              properties: {
                courseId: { type: "string", description: "ID of the course in Members V2" },
                caktoProductId: { type: "string", description: "ID of the product in Cakto backend" },
                caktoProductName: { type: "string", description: "Optional name of the Cakto product" }
              }
            }
          }
        }
      },
      responses: {
        200: {
          description: "Product successfully associated with course",
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  success: { type: "boolean" },
                  data: {
                    type: "object",
                    properties: {
                      id: { type: "string" },
                      courseId: { type: "string" },
                      caktoProductId: { type: "string" },
                      caktoProductName: { type: "string" },
                      createdAt: { type: "string" },
                      updatedAt: { type: "string" }
                    }
                  },
                  message: { type: "string" }
                }
              }
            }
          }
        },
        400: {
          description: "Invalid request or product already associated"
        },
        404: {
          description: "Course not found"
        },
        500: {
          description: "Internal server error"
        }
      }
    }),
    async (c) => {
      try {
        const { courseId, caktoProductId, caktoProductName } = c.req.valid("json");

        // Verify course exists
        const course = await db.courses.findUnique({
          where: { id: courseId }
        });

        if (!course) {
          logger.error(`Course not found: ${courseId}`, { courseId });
          return c.json({
            success: false,
            error: "COURSE_NOT_FOUND",
            message: "Curso não encontrado"
          }, 404);
        }

        // Check if this product is already associated with ANY course
        const existingProductAssociation = await db.courseProduct.findFirst({
          where: { caktoProductId },
          include: {
            course: {
              select: { name: true }
            }
          }
        });

        if (existingProductAssociation && existingProductAssociation.courseId !== courseId) {
          logger.warn(`Product ${caktoProductId} is already associated with another course`, {
            productId: caktoProductId,
            existingCourseId: existingProductAssociation.courseId,
            existingCourseName: existingProductAssociation.course.name,
            requestedCourseId: courseId,
            requestedCourseName: course.name
          });
          return c.json({
            success: false,
            error: "PRODUCT_ALREADY_ASSOCIATED",
            message: `Produto já está associado ao curso "${existingProductAssociation.course.name}"`
          }, 400);
        }

        // Check if association already exists for this specific course-product pair
        const existingAssociation = await db.courseProduct.findFirst({
          where: {
            courseId,
            caktoProductId,
          }
        });

        if (existingAssociation) {
          // Update existing association if caktoProductName is provided
          if (caktoProductName && caktoProductName !== existingAssociation.caktoProductName) {
            const updated = await db.courseProduct.update({
              where: { id: existingAssociation.id },
              data: { caktoProductName }
            });

            logger.info(`Updated product name for association`, {
              associationId: existingAssociation.id,
              courseId,
              productId: caktoProductId,
              oldName: existingAssociation.caktoProductName,
              newName: caktoProductName
            });

            return c.json({
              success: true,
              data: updated,
              message: "Associação atualizada com sucesso"
            });
          }

          logger.info(`Association already exists`, {
            associationId: existingAssociation.id,
            courseId,
            productId: caktoProductId,
            courseName: course.name
          });

          return c.json({
            success: true,
            data: existingAssociation,
            message: "Produto já está associado a este curso"
          });
        }

        // Create new association
        const association = await db.courseProduct.create({
          data: {
            courseId,
            caktoProductId,
            caktoProductName: caktoProductName || null
          }
        });

        logger.info(`Product ${caktoProductId} associated with course ${courseId}`, {
          associationId: association.id,
          courseId,
          courseName: course.name,
          productId: caktoProductId,
          productName: caktoProductName
        });

        return c.json({
          success: true,
          data: association,
          message: "Produto associado ao curso com sucesso"
        });

      } catch (error) {
        logger.error("Error associating Cakto product with course", error);
        return c.json({ error: "Erro interno do servidor" }, 500);
      }
    }
  )
  .get(
    "/course/:courseId",
    describeRoute({
      tags: ["Admin - Course Management"],
      summary: "Get all Cakto products associated with a course",
      description: "Retrieve all Cakto product associations for a specific course",
      security: [{ cookieAuth: [] }],
      responses: {
        200: {
          description: "List of associated products",
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  success: { type: "boolean" },
                  data: {
                    type: "array",
                    items: {
                      type: "object",
                      properties: {
                        id: { type: "string" },
                        courseId: { type: "string" },
                        caktoProductId: { type: "string" },
                        caktoProductName: { type: "string" },
                        createdAt: { type: "string" },
                        updatedAt: { type: "string" }
                      }
                    }
                  }
                }
              }
            }
          }
        },
        404: {
          description: "Course not found"
        }
      }
    }),
    async (c) => {
      try {
        const courseId = c.req.param("courseId");

        // Verify course exists
        const course = await db.courses.findUnique({
          where: { id: courseId }
        });

        if (!course) {
          return c.json({ error: "Curso não encontrado" }, 404);
        }

        // Get all associations for this course
        const associations = await db.courseProduct.findMany({
          where: { courseId },
          orderBy: { createdAt: "desc" }
        });

        return c.json({
          success: true,
          data: associations
        });

      } catch (error) {
        logger.error("Error getting course product associations", error);
        return c.json({ error: "Erro interno do servidor" }, 500);
      }
    }
  )
  .delete(
    "/:associationId",
    describeRoute({
      tags: ["Admin - Course Management"],
      summary: "Remove Cakto product association",
      description: "Remove association between a Cakto product and a course",
      security: [{ cookieAuth: [] }],
      responses: {
        200: {
          description: "Association removed successfully"
        },
        404: {
          description: "Association not found"
        }
      }
    }),
    async (c) => {
      try {
        const associationId = c.req.param("associationId");

        const association = await db.courseProduct.findUnique({
          where: { id: associationId }
        });

        if (!association) {
          return c.json({ error: "Associação não encontrada" }, 404);
        }

        await db.courseProduct.delete({
          where: { id: associationId }
        });

        logger.info(`Product association ${associationId} removed`);

        return c.json({
          success: true,
          message: "Associação removida com sucesso"
        });

      } catch (error) {
        logger.error("Error removing product association", error);
        return c.json({ error: "Erro interno do servidor" }, 500);
      }
    }
  );
