import { Hono } from "hono";
import { organizationRouter } from "./organizations";
import { userRouter } from "./users";
import { vitrineRouter } from "./vitrines";
import { caktoProductsRouter } from "./cakto-products";
import { coursesRouter } from "./courses";
import { courseProductAssociationRouter } from "./course-product-association";
import { grantTesterAccess } from "./grant-tester-access";

export const adminRouter = new Hono()
	.basePath("/admin")
	.route("/", organizationRouter)
	.route("/", userRouter)
	.route("/", vitrineRouter)
	.route("/", caktoProductsRouter)
	.route("/", coursesRouter)
	.route("/", courseProductAssociationRouter)
	.route("/", grantTesterAccess);
