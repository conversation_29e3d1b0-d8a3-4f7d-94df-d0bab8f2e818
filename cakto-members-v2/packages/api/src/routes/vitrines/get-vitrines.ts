import { db } from "@repo/database";
import { Hono } from "hono";
import { z } from "zod";
import { validator } from "hono-openapi/zod";
import { describeRoute } from "hono-openapi";
import { authMiddleware } from "../../middleware/auth";

const querySchema = z.object({
	organizationId: z.string(),
	status: z.enum(["DRAFT", "PUBLISHED", "ARCHIVED"]).optional(),
	visibility: z.enum(["PUBLIC", "PRIVATE"]).optional(),
	page: z.coerce.number().int().min(1).default(1),
	limit: z.coerce.number().int().min(1).max(100).default(10),
});

export const getVitrines = new Hono()
	.use(authMiddleware)
	.get("/", validator("query", querySchema),
		describeRoute({
			summary: "Get all vitrines for an organization",
			tags: ["Vitrines"],
		}),
		async (c) => {
		try {
			const user = c.get("user");
			const { organizationId, status, visibility, page, limit } = c.req.valid("query");

			// Verificar se o usuário tem permissão na organização (admins têm acesso a todas as organizações)
			if (user.role !== 'admin') {
				const userMembership = await db.member.findFirst({
					where: {
						userId: user.id,
						organizationId,
					},
				});

				if (!userMembership) {
					return c.json({ error: "User is not a member of this organization" }, 403);
				}
			}

			const skip = (page - 1) * limit;

			const where = {
				organizationId,
				...(status && { status }),
				...(visibility && { visibility }),
			};

			const [vitrines, total] = await Promise.all([
				db.vitrine.findMany({
					where,
					include: {
						sections: {
							orderBy: { position: "asc" },
							include: {
								courses: {
									select: { id: true },
								},
								purchases: {
									select: {
										id: true,
										status: true,
										amount: true,
									},
								},
							},
						},
						views: {
							select: { id: true },
						},
						creator: {
							select: {
								id: true,
								name: true,
								email: true,
							},
						},
					},
					orderBy: { createdAt: "desc" },
					skip,
					take: limit,
				}),
				db.vitrine.count({ where }),
			]);

			const formattedVitrines = vitrines.map((vitrine) => {
				const totalPurchases = vitrine.sections.reduce(
					(acc, section) => acc + section.purchases.length,
					0,
				);
				const totalRevenue = vitrine.sections.reduce(
					(acc, section) =>
						acc +
						section.purchases
							.filter((p) => p.status === "COMPLETED")
							.reduce((sum, p) => sum + Number(p.amount), 0),
					0,
				);

				return {
					id: vitrine.id,
					title: vitrine.title,
					description: vitrine.description,
					status: vitrine.status,
					visibility: vitrine.visibility,
					bannerImage: vitrine.bannerImage,
					createdAt: vitrine.createdAt.toISOString(),
					updatedAt: vitrine.updatedAt.toISOString(),
					organizationId: vitrine.organizationId,
					createdBy: vitrine.createdBy,
					creator: vitrine.creator,
					stats: {
						views: vitrine.views.length,
						enrollments: totalPurchases,
						revenue: totalRevenue,
						sections: vitrine.sections.length,
						courses: vitrine.sections.reduce((acc, s) => acc + s.courses.length, 0),
					},
					sections: vitrine.sections.map((section) => ({
						id: section.id,
						title: section.title,
						subtitle: section.subtitle,
						description: section.description,
						position: section.position,
						isLocked: section.isLocked,
						requiresPurchase: section.requiresPurchase,
						checkoutUrl: section.checkoutUrl,
						webhookUrl: section.webhookUrl,
						price: section.price ? Number(section.price) : null,
						originalPrice: section.originalPrice ? Number(section.originalPrice) : null,
						accessType: section.accessType,
						visibility: section.visibility,
						stats: {
							courses: section.courses.length,
							purchases: section.purchases.length,
							revenue: section.purchases
								.filter((p) => p.status === "COMPLETED")
								.reduce((sum, p) => sum + Number(p.amount), 0),
						},
					})),
				};
			});

			const totalPages = Math.ceil(total / limit);

			return c.json({
				data: formattedVitrines,
				meta: {
					page,
					limit,
					total,
					totalPages,
					hasNextPage: page < totalPages,
					hasPreviousPage: page > 1,
				},
			});
		} catch (error) {
			console.error("Error fetching vitrines:", error);
			return c.json({ error: "Internal server error" }, 500);
		}
	});
