import { Hono } from "hono";
import { z } from "zod";
import { validator } from "hono-openapi/zod";
import { describeRoute } from "hono-openapi";
import { authMiddleware } from "../../middleware/auth";
import { db } from "@repo/database";

const vitrineSettingsSchema = z.object({
	menuItems: z.any().default([]),
	theme: z.any().optional(),
	footer: z.any().optional(),
});

const paramsSchema = z.object({
	vitrineId: z.string(),
});

export const vitrineSettingsRouter = new Hono()
	.basePath("/settings")
	.use(authMiddleware)
	.get(
		"/:vitrineId",
		validator("param", paramsSchema),
		describeRoute({
			summary: "Get vitrine settings",
			tags: ["Vitrine Settings"],
		}),
		async (c) => {
			try {
				const { vitrineId } = c.req.valid("param");
				const user = c.get("user");

				const vitrine = await db.vitrine.findFirst({
					where: {
						id: vitrineId,
						organization: {
							members: {
								some: {
									userId: user.id,
								},
							},
						},
					},
				});

				if (!vitrine) {
					return c.json({ error: "Vitrine not found or access denied" }, 404);
				}

				const settings = await db.vitrineSettings.findFirst({
					where: { vitrineId },
				});

				return c.json(settings);
			} catch (error) {
				console.error("Error fetching vitrine settings:", error);
				return c.json({ error: "Internal server error" }, 500);
			}
		}
	)
	.post(
		"/:vitrineId",
		validator("param", paramsSchema),
		validator("json", vitrineSettingsSchema),
		describeRoute({
			summary: "Create vitrine settings",
			tags: ["Vitrine Settings"],
		}),
		async (c) => {
			try {
				const { vitrineId } = c.req.valid("param");
				const data = c.req.valid("json");
				const user = c.get("user");

				const vitrine = await db.vitrine.findFirst({
					where: {
						id: vitrineId,
						organization: {
							members: {
								some: {
									userId: user.id,
                                    role: { in: ["owner", "admin", "producer"] },
								},
							},
						},
					},
				});

				if (!vitrine) {
					return c.json({ error: "Vitrine not found or access denied" }, 404);
				}

				const existingSettings = await db.vitrineSettings.findFirst({
					where: { vitrineId },
				});

				if (existingSettings) {
					return c.json({ error: "Settings already exist for this vitrine" }, 409);
				}

				const settings = await db.vitrineSettings.create({
					data: {
						vitrineId,
						...data,
					},
				});

				return c.json(settings, 201);
			} catch (error) {
				console.error("Error creating vitrine settings:", error);
				return c.json({ error: "Internal server error" }, 500);
			}
		}
	)
	.patch(
		"/:vitrineId",
		validator("param", paramsSchema),
		validator("json", vitrineSettingsSchema.partial()),
		describeRoute({
			summary: "Update vitrine settings",
			tags: ["Vitrine Settings"],
		}),
		async (c) => {
			try {
				const { vitrineId } = c.req.valid("param");
				const data = c.req.valid("json");
				const user = c.get("user");

				const vitrine = await db.vitrine.findFirst({
					where: {
						id: vitrineId,
						organization: {
							members: {
								some: {
									userId: user.id,
                                    role: { in: ["owner", "admin", "producer"] },
								},
							},
						},
					},
				});

				if (!vitrine) {
					return c.json({ error: "Vitrine not found or access denied" }, 404);
				}

				const existingSettings = await db.vitrineSettings.findFirst({
					where: { vitrineId },
				});

				let settings;
				if (existingSettings) {
					settings = await db.vitrineSettings.update({
						where: { id: existingSettings.id },
						data,
					});
				} else {
					settings = await db.vitrineSettings.create({
						data: {
							vitrineId,
							...data,
						},
					});
				}

				return c.json(settings);
			} catch (error) {
				console.error("Error updating vitrine settings:", error);
				return c.json({ error: "Internal server error" }, 500);
			}
		}
	)
	.delete(
		"/:vitrineId",
		validator("param", paramsSchema),
		describeRoute({
			summary: "Delete vitrine settings",
			tags: ["Vitrine Settings"],
		}),
		async (c) => {
			try {
				const { vitrineId } = c.req.valid("param");
				const user = c.get("user");

				const vitrine = await db.vitrine.findFirst({
					where: {
						id: vitrineId,
						organization: {
							members: {
								some: {
									userId: user.id,
                                    role: { in: ["owner", "admin", "producer"] },
								},
							},
						},
					},
				});

				if (!vitrine) {
					return c.json({ error: "Vitrine not found or access denied" }, 404);
				}

				const settings = await db.vitrineSettings.findFirst({
					where: { vitrineId },
				});

				if (!settings) {
					return c.json({ error: "Settings not found" }, 404);
				}

				await db.vitrineSettings.delete({
					where: { id: settings.id },
				});

				return c.json({ message: "Settings deleted successfully" });
			} catch (error) {
				console.error("Error deleting vitrine settings:", error);
				return c.json({ error: "Internal server error" }, 500);
			}
		}
	);
