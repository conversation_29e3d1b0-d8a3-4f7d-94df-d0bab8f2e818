import { db } from "@repo/database";
import { Hono } from 'hono'
import { validator } from 'hono-openapi/zod'
import { z } from 'zod'
import { describeRoute } from "hono-openapi";
import { authMiddleware } from "../../middleware/auth";

const paramsSchema = z.object({
  lessonId: z.string(),
})

export const getLessonFiles = new Hono()
	.use(authMiddleware)
	.get('/', validator('param', paramsSchema),
		describeRoute({
			summary: "Get lesson files",
			tags: ["Courses"],
		}),
		async (c) => {
    try {
      const { lessonId } = c.req.valid('param')

      // Replace with Prisma query
      const files = await db.lessonFiles.findMany({
        where: { lessonId },
        orderBy: { id: 'asc' }
      });

      const formattedFiles = files.map(file => ({
        id: file.id.toString(),
        name: file.name,
        url: file.url,
        size: file.size,
        type: file.type,
        lessonId,
      }));

      return c.json({ files })
    } catch (error) {
      return c.json({ error: 'Failed to fetch lesson files' }, 500)
    }
  })
