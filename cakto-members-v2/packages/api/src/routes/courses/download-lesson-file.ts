import { db } from "@repo/database";
import { Hono } from 'hono'
import { validator } from 'hono-openapi/zod'
import { z } from 'zod'
import { describeRoute } from "hono-openapi";
import { authMiddleware } from "../../middleware/auth";

const paramsSchema = z.object({
  lessonId: z.string(),
  fileId: z.string(),
})

export const downloadLessonFile = new Hono()
	.use(authMiddleware)
	.get('/', validator('param', paramsSchema),
		describeRoute({
			summary: "Download lesson file",
			tags: ["Courses"],
		}),
		async (c) => {
    try {
      const { lessonId, fileId } = c.req.valid('param')

      const file = {
        id: fileId,
        name: 'Material de Apoio.pdf',
        url: 'https://example.com/files/material.pdf',
        lessonId,
      }

      return c.redirect(file.url)
    } catch (error) {
      return c.json({ error: 'Failed to download lesson file' }, 500)
    }
  })
