import { db } from "@repo/database";
import { Hono } from 'hono'
import { validator } from 'hono-openapi/zod'
import { z } from 'zod'
import { describeRoute } from "hono-openapi";
import { authMiddleware } from "../../middleware/auth";

const paramsSchema = z.object({
  lessonId: z.string(),
})

const bodySchema = z.object({
  content: z.string().min(1).max(1000),
})

export const addLessonComment = new Hono()
	.use(authMiddleware)
	.post('/', validator('param', paramsSchema), validator('json', bodySchema),
		describeRoute({
			summary: "Add comment to lesson",
			tags: ["Courses"],
		}),
		async (c) => {
    try {
      const user = c.get('user')
      const { lessonId } = c.req.valid('param')
      const { content } = c.req.valid('json')

      console.log('🔍 Comments API - Add comment:', { lessonId, content, userId: user.id })

      // Check if lesson exists
      const lesson = await db.lessons.findUnique({
        where: { id: lessonId },
        include: {
          module: {
            include: {
              courseModules: {
                include: {
                  course: {
                    include: {
                      organization: true
                    }
                  }
                }
              }
            }
          }
        }
      })

      if (!lesson) {
        console.log('❌ Comments API - Lesson not found:', lessonId)
        return c.json({ error: 'Lesson not found' }, 404)
      }

      // Create the comment in database
      const newComment = await db.lessonComments.create({
        data: {
          lessonId,
          userId: user.id,
          comment: content,
        },
        include: {
          user: {
            select: { id: true, name: true, email: true }
          },
          lessonCommentReplies: {
            include: {
              user: { select: { id: true, name: true, email: true } }
            }
          }
        }
      })

      const formattedComment = {
        id: newComment.id.toString(),
        content: newComment.comment,
        createdAt: newComment.createdAt.toISOString(),
        lessonId: newComment.lessonId,
        user: newComment.user,
        replies: newComment.lessonCommentReplies.map(reply => ({
          id: reply.id.toString(),
          content: reply.replyComment,
          createdAt: reply.createdAt.toISOString(),
          commentId: newComment.id.toString(),
          user: reply.user
        }))
      }

      console.log('✅ Comments API - Comment added successfully')
      return c.json({ comment: formattedComment })
    } catch (error) {
      console.error('❌ Comments API - Error adding comment:', error)
      return c.json({ error: 'Failed to add lesson comment' }, 500)
    }
  })
