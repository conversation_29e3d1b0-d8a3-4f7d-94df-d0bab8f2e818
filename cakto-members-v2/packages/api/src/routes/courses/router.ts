import { Hono } from "hono";
import { describeRoute } from "hono-openapi";
import { authMiddleware } from "../../middleware/auth";
import { getCoursePreview } from "./get-course-preview";
import { getCourse } from "./get-course";
import { createCourse } from "./create-course";
import { updateCourse } from "./update-course";
import { deleteCourse } from "./delete-course";
import { uploadImage } from "./upload-image";
import { uploadFiles } from "./upload-files";
import { createLesson } from "./create-lesson";
import { createModule } from "./create-module";
import { getCourseModules } from "./get-course-modules";
import { getCourseLessons } from "./get-course-lessons";
import { getModuleLessons } from "./get-module-lessons";
import { getOrganizationCourses } from "./get-organization-courses";
import { checkCourseByProduct } from "./check-course-by-product";
import { getAdminCourse } from "./get-admin-course";
import { getAdminCourses } from "./get-admin-courses";
import { deleteAdminCourse } from "./delete-admin-course";
import { createCourseProductMapping } from "./create-course-product-mapping";


export const coursesRouter = new Hono()
	.basePath("/courses")
	.route("/upload-url", uploadImage)
	.route("/", uploadFiles)

	// Course CRUD operations
    .route("/", createCourse)
	.route("/", updateCourse)
	.route("/", deleteCourse)
	.route("/", getCourse)
	.route("/", getCoursePreview)

	// Module operations
	.route("/", createModule)
	.route("/", getCourseModules)
	.route("/", getModuleLessons)

	// Lesson operations
	.route("/", createLesson)
	.route("/", getCourseLessons)

	// Organization courses
	.route("/", getOrganizationCourses)

	// Admin operations
	.route("/", getAdminCourse)
	.route("/", getAdminCourses)
	.route("/", deleteAdminCourse)

	// Course-product mapping
	.route("/course", createCourseProductMapping)

	// Course existence check by productId (for integration)
	.route("/", checkCourseByProduct)

	.get("/test",
		describeRoute({
			summary: "Test course API",
			tags: ["Courses"],
		}),
		(c) => {
			return c.json({ message: "Course API is working!" });
		});
