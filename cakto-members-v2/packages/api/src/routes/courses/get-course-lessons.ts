import { db } from "@repo/database";
import { Hono } from 'hono'
import { validator } from 'hono-openapi/zod'
import { z } from 'zod'
import { describeRoute } from "hono-openapi";
import { authMiddleware } from "../../middleware/auth";

const paramsSchema = z.object({
  courseId: z.string(),
})

export const getCourseLessons = new Hono()
	.use(authMiddleware)
	.get('/course/:courseId/lessons', validator('param', paramsSchema),
		describeRoute({
			summary: "Get course lessons",
			tags: ["Courses"],
		}),
		async (c) => {
    try {
      const user = c.get("user");
      const { courseId } = c.req.valid('param')

      // First, check if course exists and user has access
      const course = await db.courses.findUnique({
        where: { id: courseId },
        select: {
          id: true,
          organizationId: true,
          createdBy: true,
        },
      });

      if (!course) {
        return c.json({ error: 'Course not found' }, 404);
      }

      // Check if user has access to the course (unless admin)
      if (user.role !== 'admin') {
        const userCourseAccess = await db.userCourses.findFirst({
          where: {
            userId: user.id,
            courseId: course.id,
          },
        });

        if (!userCourseAccess && course.createdBy !== user.id) {
          return c.json({ error: 'Access denied - You do not have access to this course' }, 403);
        }
      }

      // Get lessons for the course
      const lessons = await db.lessons.findMany({
        where: {
          module: {
            courseModules: {
              some: { courseId }
            }
          }
        },
        include: {
          module: {
            select: { id: true }
          }
        },
        orderBy: { position: 'asc' }
      });

      const formattedLessons = lessons.map(lesson => ({
        id: lesson.id,
        name: lesson.name,
        description: lesson.description,
        duration: lesson.duration,
        videoUrl: lesson.videoUrl,
        courseId,
        moduleId: lesson.module.id,
        position: lesson.position,
        // isCompleted would be user-specific, so omitted or set to false
      }));

      return c.json({ lessons })
    } catch (error) {
      return c.json({ error: 'Failed to fetch course lessons' }, 500)
    }
  })
