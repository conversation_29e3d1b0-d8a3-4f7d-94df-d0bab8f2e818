import { db } from "@repo/database";
import { Hono } from 'hono'
import { validator } from 'hono-openapi/zod'
import { z } from 'zod'
import { describeRoute } from "hono-openapi";
import { authMiddleware } from "../../middleware/auth";

const paramsSchema = z.object({
  moduleId: z.string(),
})

export const getModuleLessons = new Hono()
	.use(authMiddleware)
	.get('/module/:moduleId/lessons', validator('param', paramsSchema),
		describeRoute({
			summary: "Get module lessons",
			tags: ["Courses"],
		}),
		async (c) => {
    try {
      const user = c.get("user");
      const { moduleId } = c.req.valid('param')

      // First, get the module with course information to check access
      const module = await db.modules.findUnique({
        where: { id: moduleId },
        include: {
          courseModules: {
            include: {
              course: {
                select: {
                  id: true,
                  organizationId: true,
                  createdBy: true,
                },
              },
            },
          },
        },
      });

      if (!module || !module.courseModules[0]) {
        return c.json({ error: 'Module not found' }, 404);
      }

      const course = module.courseModules[0].course;

      // Check if user has access to the course (unless admin)
      if (user.role !== 'admin') {
        const userCourseAccess = await db.userCourses.findFirst({
          where: {
            userId: user.id,
            courseId: course.id,
          },
        });

        if (!userCourseAccess && course.createdBy !== user.id) {
          return c.json({ error: 'Access denied - You do not have access to this course' }, 403);
        }
      }

      // Get lessons for the module
      const lessons = await db.lessons.findMany({
        where: { moduleId },
        orderBy: { position: 'asc' }
      });

      const formattedLessons = lessons.map(lesson => ({
        id: lesson.id,
        name: lesson.name,
        description: lesson.description,
        duration: lesson.duration,
        videoUrl: lesson.videoUrl,
        moduleId: lesson.moduleId,
        position: lesson.position,
        // isCompleted is user-specific, set to false or handle separately
      }));

      return c.json({ lessons })
    } catch (error) {
      return c.json({ error: 'Failed to fetch module lessons' }, 500)
    }
  })
