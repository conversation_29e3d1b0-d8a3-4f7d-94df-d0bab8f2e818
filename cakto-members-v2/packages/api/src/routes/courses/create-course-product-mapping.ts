import { db } from "@repo/database";
import { Hono } from "hono";
import { z } from "zod";
import { validator } from "hono-openapi/zod";
import { describeRoute } from "hono-openapi";
import { authMiddleware } from "../../middleware/auth";
import { adminMiddleware } from "../../middleware/admin";

const createCourseProductMappingSchema = z.object({
	caktoProductId: z.string().min(1, "Cakto Product ID is required"),
	caktoProductName: z.string().min(1, "Cakto Product Name is required"),
});

export const createCourseProductMapping = new Hono()
	.use(authMiddleware)
	.post("/:courseId/product-mapping",
		describeRoute({
			summary: "Create course-product mapping",
			tags: ["Courses"],
		}),
		async (c) => {
		try {
			const user = c.get("user");
			const courseId = c.req.param("courseId");

			let data;
			try {
				const rawData = await c.req.json();
				data = createCourseProductMappingSchema.parse(rawData);
			} catch (error) {
				if (error instanceof z.ZodError) {
					return c.json({
						error: "Validation failed",
						details: error.errors.map(e => ({
							field: e.path.join('.'),
							message: e.message
						}))
					}, 400);
				}
				return c.json({ error: "Invalid request data" }, 400);
			}

			// Check if course exists and user has access
			const course = await db.courses.findFirst({
				where: {
					id: courseId,
                    organization: {
                        members: {
                            some: {
                                userId: user.id,
                                role: { in: ["owner", "admin", "producer"] }
                            }
                        }
                    }
				},
				include: {
					organization: {
						select: {
							id: true,
							name: true,
							slug: true,
						},
					},
				},
			});

			if (!course) {
				return c.json({ error: "Course not found or access denied" }, 404);
			}

            // Enforce 1:1 mapping
            const productAlreadyMapped = await db.courseProduct.findFirst({
                where: { caktoProductId: data.caktoProductId },
                include: { course: { select: { id: true, name: true } } },
            });

            if (productAlreadyMapped && productAlreadyMapped.courseId !== courseId) {
                return c.json({
                    error: "PRODUCT_ALREADY_ASSOCIATED",
                    message: `Produto já está associado ao curso "${productAlreadyMapped.course.name}"`,
                    courseId: productAlreadyMapped.courseId,
                }, 409);
            }

            const mappingForSameCourse = await db.courseProduct.findFirst({
                where: { courseId, caktoProductId: data.caktoProductId },
            });

            if (mappingForSameCourse) {
                return c.json({ success: true, data: mappingForSameCourse }, 200);
            }

			// Create the mapping
			const courseProductMapping = await db.courseProduct.create({
				data: {
					courseId: courseId,
					caktoProductId: data.caktoProductId,
					caktoProductName: data.caktoProductName,
				},
				include: {
					course: {
						select: {
							id: true,
							name: true,
						},
					},
				},
			});

			return c.json({
				success: true,
				message: "Course-product mapping created successfully",
				data: courseProductMapping,
			});
		} catch (error) {
			console.error("Error creating course-product mapping:", error);
			return c.json({ error: "Internal server error" }, 500);
		}
	});
