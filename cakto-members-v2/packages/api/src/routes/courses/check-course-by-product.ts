import { db } from "@repo/database";
import { Hono } from "hono";
import { z } from "zod";
import { validator } from "hono-openapi/zod";
import { describeRoute } from "hono-openapi";

const paramsSchema = z.object({
  productId: z.string().uuid("productId deve ser um UUID válido"),
});

export const checkCourseByProduct = new Hono().get(
  "/check/:productId",
  validator("param", paramsSchema),
  describeRoute({
    tags: ["Courses"],
    summary: "Check if a course exists for a given Cakto productId",
    description: "Returns whether a course exists associated to the provided Cakto productId. If found, returns the courseId and courseName.",
    responses: {
      200: {
        description: "Course exists",
        content: {
          "application/json": {
            schema: {
              type: "object",
              properties: {
                exists: { type: "boolean" },
                courseId: { type: "string" },
                productId: { type: "string" },
                courseName: { type: "string" },
              },
            },
          },
        },
      },
      404: {
        description: "Course not found for productId",
        content: {
          "application/json": {
            schema: {
              type: "object",
              properties: {
                exists: { type: "boolean" },
                courseId: { type: "string", nullable: true },
                productId: { type: "string" },
              },
            },
          },
        },
      },
    },
  }),
  async (c) => {
    try {
      const { productId } = c.req.valid("param");

      const association = await db.courseProduct.findFirst({
        where: { caktoProductId: productId },
        include: { course: { select: { id: true, name: true } } },
      });

      if (!association || !association.course) {
        return c.json(
          { exists: false, courseId: null, productId },
          404,
        );
      }

      return c.json({
        exists: true,
        courseId: association.course.id,
        productId,
        courseName: association.course.name,
      });
    } catch (error) {
      return c.json({ error: "Erro interno" }, 500);
    }
  },
);


