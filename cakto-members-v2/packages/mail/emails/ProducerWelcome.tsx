import React from "react";
import { Preview, Heading, Text, Link, Section } from "@react-email/components";
import { Wrapper } from "./Wrapper";

interface ProducerWelcomeProps {
  name: string;
  email: string;
}

export const ProducerWelcome = ({
  name,
  email,
}: ProducerWelcomeProps) => (
  <Wrapper>
    <Preview>🎉 Bem-vindo ao Cakto Members! Sua conta de produtor foi criada</Preview>
    <Section style={{ textAlign: "center" }}>
      <Heading style={{ fontSize: 24, margin: "24px 0 8px", color: "#0F7864" }}>
        🎉 Bem-vindo ao Cakto Members!
      </Heading>
      <Text style={{ fontSize: 16, margin: "16px 0", lineHeight: "1.6" }}>
        Olá {name},<br />
        Sua conta de produtor no Cakto Members foi criada com sucesso!<br />
        Agora você pode criar e gerenciar seus cursos na nossa plataforma.
      </Text>

      <Section style={{
        background: "linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)",
        padding: "20px",
        borderRadius: "8px",
        margin: "24px 0",
        textAlign: "left",
        border: "1px solid #dee2e6"
      }}>
        <Text style={{ margin: "0 0 12px 0", fontSize: 16, fontWeight: "600", color: "#0F7864" }}>
          🔐 Suas credenciais de acesso:
        </Text>
        <Text style={{ margin: "8px 0", fontSize: 14, color: "#495057" }}>
          <strong>Email:</strong> {email}
        </Text>
        <Text style={{ margin: "8px 0", fontSize: 14, color: "#495057" }}>
          <strong>Perfil:</strong> Produtor
        </Text>
      </Section>

      <Section style={{ margin: "32px 0" }}>
        <Link
          href={`${process.env.NEXT_PUBLIC_APP_URL || 'https://members.cakto.com.br'}/auth/login`}
          style={{
            display: "inline-block",
            background: "linear-gradient(135deg, #0F7864 0%, #0d6b5a 100%)",
            color: "#fff",
            padding: "14px 36px",
            borderRadius: "8px",
            fontWeight: "600",
            textDecoration: "none",
            fontSize: 16,
            boxShadow: "0 4px 12px rgba(15, 120, 100, 0.3)",
            transition: "all 0.3s ease"
          }}
        >
          🚀 Acessar Minha Conta
        </Link>
      </Section>

      <Section style={{
        background: "#d1ecf1",
        padding: "16px",
        borderRadius: "8px",
        margin: "24px 0",
        border: "1px solid #bee5eb"
      }}>
        <Text style={{ color: "#0c5460", fontSize: 14, margin: "0", textAlign: "left" }}>
          <strong>💡 Dica:</strong> Use o mesmo email e senha que você usa no Cakto principal para fazer login.
          Se você ainda não tem senha, use a opção "Esqueci minha senha" para criar uma nova.
        </Text>
      </Section>

      <Section style={{ margin: "32px 0", textAlign: "left" }}>
        <Text style={{ fontSize: 16, fontWeight: "600", color: "#0F7864", margin: "0 0 12px 0" }}>
          🎯 O que você pode fazer na plataforma:
        </Text>
        <Text style={{ fontSize: 14, color: "#495057", margin: "4px 0", lineHeight: "1.5" }}>
          • Criar e gerenciar seus cursos
        </Text>
        <Text style={{ fontSize: 14, color: "#495057", margin: "4px 0", lineHeight: "1.5" }}>
          • Adicionar aulas, módulos e conteúdos
        </Text>
        <Text style={{ fontSize: 14, color: "#495057", margin: "4px 0", lineHeight: "1.5" }}>
          • Gerenciar alunos e acessos
        </Text>
        <Text style={{ fontSize: 14, color: "#495057", margin: "4px 0", lineHeight: "1.5" }}>
          • Criar vitrines para seus cursos
        </Text>
        <Text style={{ fontSize: 14, color: "#495057", margin: "4px 0", lineHeight: "1.5" }}>
          • Acompanhar métricas e relatórios
        </Text>
        <Text style={{ fontSize: 14, color: "#495057", margin: "4px 0", lineHeight: "1.5" }}>
          • Interagir com a comunidade de alunos
        </Text>
      </Section>

      <Section style={{
        background: "#fff3cd",
        padding: "16px",
        borderRadius: "8px",
        margin: "24px 0",
        border: "1px solid #ffeaa7"
      }}>
        <Text style={{ color: "#856404", fontSize: 14, margin: "0", textAlign: "left" }}>
          <strong>📋 Próximos passos:</strong>
        </Text>
        <Text style={{ color: "#856404", fontSize: 14, margin: "8px 0 0 0", textAlign: "left" }}>
          1. Faça login na plataforma<br />
          2. Complete seu perfil de produtor<br />
          3. Crie sua primeira organização<br />
          4. Comece a criar seu primeiro curso
        </Text>
      </Section>

      <Text style={{ color: "#6c757d", fontSize: 14, margin: "24px 0 0", fontStyle: "italic" }}>
        Estamos aqui para apoiar seu sucesso como produtor!<br />
        Se tiver dúvidas sobre como usar a plataforma, nossa equipe está pronta para ajudar.
      </Text>
    </Section>
  </Wrapper>
);

export default ProducerWelcome;
