import { getSession } from "@saas/auth/lib/server";
import { StudentDashboard } from "@saas/start/components/StudentDashboard";
import { ViewModeProvider } from "@saas/shared/contexts/ViewModeContext";
import { DashboardWithViewMode } from "@saas/start/components/DashboardWithViewMode";
import { getTranslations } from "next-intl/server";
import { redirect } from "next/navigation";
import { isAdmin, isUser, isProducer } from "@/lib/auth/role-utils";

export default async function AppStartPage() {
	const session = await getSession();

	if (!session) {
		return redirect("/auth/login");
	}

	const t = await getTranslations();

	const userRole = session.user?.role;
  const userIsAdmin = isAdmin(userRole) || isProducer(userRole);
  const userIsStudent = isUser(userRole);

	return (
		<div className="">
			{userIsStudent ? (
				<ViewModeProvider defaultMode="student">
					<StudentDashboard />
				</ViewModeProvider>
			) : userIsAdmin ? (
				<ViewModeProvider defaultMode="admin">
					<DashboardWithViewMode />
				</ViewModeProvider>
			) : (
				<div className="flex items-center justify-center min-h-[400px]">
					<div className="text-center">
						<h2 className="text-xl font-semibold mb-2">Acesso Negado</h2>
						<p className="text-muted-foreground">Você não tem permissão para acessar esta área.</p>
					</div>
				</div>
			)}
		</div>
	);
}
