"use client";

import { useState, useEffect, useCallback } from "react";
import { useR<PERSON>er, useSearchParams } from "next/navigation";
import { toast } from "sonner";
import { Card, CardContent, CardHeader, CardTitle } from "@/modules/ui/components/card";
import { <PERSON><PERSON> } from "@/modules/ui/components/button";
import { Badge } from "@/modules/ui/components/badge";
import { Loader2Icon, PackageIcon, AlertCircleIcon, CheckIcon } from "lucide-react";
import { CourseBasicForm } from "../../../courses/components/CourseBasicForm";
import { CourseStructureForm } from "../../../courses/components/CourseStructureForm";
import { CourseSettingsForm } from "../../../courses/components/CourseSettingsForm";
import { CoursePreview } from "../../../courses/components/CoursePreview";
import { CaktoProductSelector } from "@/modules/saas/admin/component/courses/CaktoProductSelector";
import type { CourseFormData } from "../../../courses/types";
import { apiClient } from "@shared/lib/api-client";

const STEPS = [
	{
		id: "basic",
		title: "Informações Básicas",
		description: "Nome, descrição e organização",
	},
	{
		id: "structure",
		title: "Estrutura e Conteúdo",
		description: "Módulos, aulas e upload de materiais",
	},
	{
		id: "settings",
		title: "Configurações",
		description: "Configurações de acesso e publicação",
	},
	{
		id: "preview",
		title: "Visualizar e Publicar",
		description: "Revise e publique seu curso",
	},
];

interface CaktoProduct {
	id: string;
	name: string;
	description?: string;
	image?: string;
	price: number;
	type: string;
	status: string;
	contentDeliveries: string[];
	alreadyLinked: boolean;
}

interface EnhancedCourseFormProps {
	mode: "create" | "edit";
	courseId?: string;
}

export default function EnhancedCourseForm({ mode, courseId }: EnhancedCourseFormProps) {
	const router = useRouter();
	const searchParams = useSearchParams();
	const [currentStep, setCurrentStep] = useState(0);
	const [isLoading, setIsLoading] = useState(false);
	const [isLoadingProduct, setIsLoadingProduct] = useState(false);
	const [showProductSelector, setShowProductSelector] = useState(false);
	const [selectedCaktoProduct, setSelectedCaktoProduct] = useState<CaktoProduct | null>(null);

	// Get product parameter from URL
  const productParam = searchParams.get('product');
  const caktoProductId = searchParams.get('caktoProductId') || searchParams.get('productId');
  const caktoProductName = searchParams.get('caktoProductName') || searchParams.get('productName');

	const [formData, setFormData] = useState<CourseFormData>({
		name: caktoProductName ? decodeURIComponent(caktoProductName) : "",
		description: "",
		organizationId: "",
		community: "",
		link: "",
		logo: "",
		modules: [],
	});

  useEffect(() => {
    const tryRedirectIfExists = async () => {
      const pid = caktoProductId || productParam;
      if (!pid || selectedCaktoProduct) return;

      try {
        const res = await fetch(`/api/courses/check/${pid}`);
        if (res.ok) {
          const data = await res.json();
          if (data?.exists && data?.courseId) {
            const params = new URLSearchParams();
            params.set('caktoProductId', pid);
            if (caktoProductName) params.set('caktoProductName', caktoProductName);
            // Preserve product/productName if present
            if (productParam) params.set('product', productParam);
            router.replace(`/app/admin/courses/${data.courseId}/edit?${params.toString()}`);
            return;
          }
        }
      } catch (e) {
      }
      if (caktoProductId && caktoProductName) {
        setSelectedCaktoProduct({
          id: caktoProductId,
          name: decodeURIComponent(caktoProductName),
          description: "",
          price: 0,
          type: "course",
          status: "active",
          contentDeliveries: ["cakto"],
          alreadyLinked: false,
        });
        setFormData(prev => ({
          ...prev,
          name: decodeURIComponent(caktoProductName),
        }));
      } else if (productParam) {
        loadCaktoProductData(productParam);
      }
    };

    void tryRedirectIfExists();
  }, [productParam, caktoProductId, caktoProductName, selectedCaktoProduct, router]);

	const loadCaktoProductData = async (productId: string) => {
		try {
			setIsLoadingProduct(true);

			// Fetch product details from Cakto API
			const response = await fetch(`/api/admin/cakto-products`);

			if (!response.ok) {
				throw new Error("Failed to fetch Cakto products");
			}

			const products = await response.json();
			const product = products.find((p: CaktoProduct) => p.id === productId);

			if (!product) {
				throw new Error("Product not found");
			}

			if (product.alreadyLinked) {
				toast.error("Este produto já está associado a outro curso");
				router.push("/app/admin/courses");
				return;
			}

			setSelectedCaktoProduct(product);

			// Auto-populate form with product data
			setFormData(prev => ({
				...prev,
				name: product.name,
				description: product.description || "",
				logo: product.image || "",
			}));

			toast.success(`Produto "${product.name}" carregado com sucesso!`);

		} catch (error) {

			toast.error("Erro ao carregar dados do produto da Cakto");
		} finally {
			setIsLoadingProduct(false);
		}
	};

	const updateFormData = useCallback((updates: Partial<CourseFormData>) => {
		setFormData((prev: CourseFormData) => ({ ...prev, ...updates }));
	}, []);

	const handleNext = useCallback(() => {
		if (currentStep < STEPS.length - 1) {
			setCurrentStep((prev) => prev + 1);
		}
	}, [currentStep]);

	const handlePrevious = useCallback(() => {
		if (currentStep > 0) {
			setCurrentStep((prev) => prev - 1);
		}
	}, [currentStep]);

	const handleProductSelect = (product: CaktoProduct) => {
		setSelectedCaktoProduct(product);
		setFormData(prev => ({
			...prev,
			name: product.name,
			description: product.description || prev.description,
			logo: product.image || prev.logo,
		}));
		setShowProductSelector(false);
		toast.success(`Produto "${product.name}" selecionado!`);
	};

	const handleRemoveProduct = () => {
		setSelectedCaktoProduct(null);
		// Don't clear form data as user might want to keep the populated information
		toast.info("Produto removido. Os dados do formulário foram mantidos.");
	};

	if (isLoadingProduct) {
		return (
			<div className="flex items-center justify-center min-h-[400px]">
				<div className="text-center">
					<Loader2Icon className="h-8 w-8 animate-spin mx-auto mb-4" />
					<p className="text-sm text-gray-600">Carregando dados do produto...</p>
				</div>
			</div>
		);
	}

	const renderStepContent = () => {
		switch (currentStep) {
			case 0:
				return (
					<CourseBasicForm
						data={formData}
						onUpdate={updateFormData}
						onNext={handleNext}
					/>
				);
			case 1:
				return (
					<CourseStructureForm
						data={formData}
						onUpdate={updateFormData}
						onNext={handleNext}
						onPrevious={handlePrevious}
					/>
				);
			case 2:
				return (
					<CourseSettingsForm
						data={formData}
						onUpdate={updateFormData}
						onNext={handleNext}
						onPrevious={handlePrevious}
					/>
				);
			case 3:
				return (
					<CoursePreview
						data={formData}
						onPrevious={handlePrevious}
					/>
				);
			default:
				return null;
		}
	};

	return (
		<div className="container mx-auto py-8 px-4">
			<div className="max-w-4xl mx-auto">
				{/* Header */}
				<div className="mb-8">
					<h1 className="text-3xl font-bold text-gray-900 mb-2">
						{mode === "create" ? "Criar Novo Curso" : "Editar Curso"}
					</h1>
					<p className="text-gray-600">
						{mode === "create"
							? "Configure seu curso seguindo os passos abaixo"
							: "Edite as informações do seu curso"
						}
					</p>
				</div>

				{/* Cakto Product Info */}
				{selectedCaktoProduct && (
					<Card className="mb-6 border-primary/20 bg-primary/5">
						<CardContent className="pt-6">
							<div className="flex items-start gap-4">
								<div className="p-3 bg-primary/10 rounded-lg flex-shrink-0">
									<PackageIcon className="h-6 w-6 text-primary" />
								</div>
								<div className="flex-1 min-w-0">
									<div className="flex items-start justify-between mb-2">
										<div className="flex-1 min-w-0">
											<h3 className="font-semibold text-lg text-foreground mb-1">
												{selectedCaktoProduct.name}
											</h3>
											{selectedCaktoProduct.description && (
												<p className="text-sm text-muted-foreground mb-3 line-clamp-2">
													{selectedCaktoProduct.description}
												</p>
											)}
										</div>
									</div>
									<div className="flex items-center justify-between">
										<div className="flex items-center gap-2">
											<Badge status="success" className="text-xs">
												Produto Vinculado
											</Badge>
											<Badge status="info" className="text-xs">
												{selectedCaktoProduct.type === 'course' ? 'Curso' :
												 selectedCaktoProduct.type === 'ebook' ? 'E-book' :
												 selectedCaktoProduct.type === 'mentorship' ? 'Mentoria' :
												 selectedCaktoProduct.type}
											</Badge>
										</div>
										<Button
											variant="outline"
											size="sm"
											onClick={handleRemoveProduct}
											className="text-destructive hover:text-destructive hover:bg-destructive/10 h-8 px-3"
										>
											Remover
										</Button>
									</div>
								</div>
							</div>
						</CardContent>
					</Card>
				)}

				{/* Product Selection Button */}
				{!selectedCaktoProduct && (
					<Card className="mb-6 border-dashed border-muted-foreground/30 bg-muted/30">
						<CardContent className="py-8">
							<div className="text-center">
								<div className="p-4 bg-muted/50 rounded-full w-fit mx-auto mb-4">
									<PackageIcon className="h-8 w-8 text-muted-foreground" />
								</div>
								<h3 className="text-lg font-semibold mb-2">
									Associar Produto Cakto (Opcional)
								</h3>
								<p className="text-muted-foreground mb-6 max-w-md mx-auto">
									Selecione um produto da Cakto para pré-popular automaticamente os dados do curso
								</p>
								<Button
									onClick={() => setShowProductSelector(true)}
									variant="outline"
									className="border-primary/30 text-primary hover:bg-primary/10"
								>
									<PackageIcon className="h-4 w-4 mr-2" />
									Selecionar Produto
								</Button>
							</div>
						</CardContent>
					</Card>
				)}

				{/* Step Content */}
				{renderStepContent()}

				{/* Product Selector Modal */}
				<CaktoProductSelector
					open={showProductSelector}
					onOpenChange={setShowProductSelector}
					onProductSelect={handleProductSelect}
				/>
			</div>
		</div>
	);
}
