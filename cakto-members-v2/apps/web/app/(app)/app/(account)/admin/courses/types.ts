export interface CourseFormData {
	id?: string;
	name: string;
	description?: string;
	organizationId: string;
	community?: string;
	link?: string;
	logo?: string;
	modules: CourseModule[];
}

export interface CourseModule {
	id?: string;
	name: string;
	description?: string;
	position: number;
	cover?: string;
	lessons?: CourseLesson[];
}

export interface CourseLesson {
	id?: string;
	name: string;
	description?: string;
	duration?: string;
	position: number;
	videoUrl?: string;
	videoId?: string;
	thumbnail?: string;
	files?: CourseLessonFile[];
}

export interface CourseLessonFile {
	id?: string;
	name: string;
	url: string;
	type: 'pdf' | 'document' | 'image' | 'video' | 'audio' | 'other';
	size: number;
	uploadProgress?: number;
	status?: 'pending' | 'uploading' | 'completed' | 'error';
}

export interface FileUploadState {
	[key: string]: {
		progress: number;
		status: 'pending' | 'uploading' | 'completed' | 'error';
		error?: string;
	};
}
