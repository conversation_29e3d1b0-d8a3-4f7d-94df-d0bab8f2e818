'use client'

import { useState } from 'react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/modules/ui/components/card'
import { Button } from '@/modules/ui/components/button'
import { Input } from '@/modules/ui/components/input'
import { Label } from '@/modules/ui/components/label'
import { Textarea } from '@/modules/ui/components/textarea'
import { Badge } from '@/modules/ui/components/badge'
import { Alert, AlertDescription } from '@/modules/ui/components/alert'
import { toast } from 'sonner'
import { Users, CheckCircle, AlertCircle, Loader2 } from 'lucide-react'

interface TesterAccessResult {
  caktoUserId: string
  userId: string
  email: string
  status: 'success' | 'error'
  message: string
}

interface TesterAccessResponse {
  success: boolean
  message: string
  data: {
    results: TesterAccessResult[]
    errors: TesterAccessResult[]
    summary: {
      total: number
      successful: number
      failed: number
    }
  }
}

export default function TesterAccessPage() {
  const [userIds, setUserIds] = useState('')
  const [courseId, setCourseId] = useState('')
  const [organizationId, setOrganizationId] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [results, setResults] = useState<TesterAccessResponse | null>(null)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!userIds.trim()) {
      toast.error('Por favor, insira pelo menos um User ID')
      return
    }

    const userIdsArray = userIds
      .split('\n')
      .map(id => id.trim())
      .filter(id => id.length > 0)

    if (userIdsArray.length === 0) {
      toast.error('Por favor, insira pelo menos um User ID válido')
      return
    }

    setIsLoading(true)
    setResults(null)

    try {
      const response = await fetch('/api/admin/grant-tester-access', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userIds: userIdsArray,
          courseId: courseId.trim() || undefined,
          organizationId: organizationId.trim() || undefined,
        }),
      })

      const data: TesterAccessResponse = await response.json()

      if (response.ok) {
        setResults(data)
        toast.success(`Acesso liberado para ${data.data.summary.successful} usuários`)
      } else {
        toast.error(data.message || 'Erro ao liberar acesso')
      }
    } catch (error) {
      console.error('Error granting tester access:', error)
      toast.error('Erro de conexão. Tente novamente.')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      <div className="text-center">
        <h1 className="text-3xl font-bold mb-2">Liberar Acesso de Testers</h1>
        <p className="text-muted-foreground">
          Libere acesso para usuários testers baseado nos User IDs do Cakto
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5 text-primary" />
            Configuração de Acesso
          </CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="space-y-2">
              <Label htmlFor="userIds">User IDs do Cakto</Label>
              <Textarea
                id="userIds"
                placeholder="Insira os User IDs do Cakto, um por linha&#10;Exemplo:&#10;user-id-1&#10;user-id-2&#10;user-id-3"
                value={userIds}
                onChange={(e) => setUserIds(e.target.value)}
                rows={6}
                required
              />
              <p className="text-sm text-muted-foreground">
                Insira os User IDs dos usuários que devem receber acesso. Um ID por linha.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="courseId">ID do Curso (Opcional)</Label>
                <Input
                  id="courseId"
                  placeholder="ID do curso para acesso específico"
                  value={courseId}
                  onChange={(e) => setCourseId(e.target.value)}
                />
                <p className="text-sm text-muted-foreground">
                  Se fornecido, os usuários receberão acesso específico a este curso.
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="organizationId">ID da Organização (Opcional)</Label>
                <Input
                  id="organizationId"
                  placeholder="ID da organização para acesso específico"
                  value={organizationId}
                  onChange={(e) => setOrganizationId(e.target.value)}
                />
                <p className="text-sm text-muted-foreground">
                  Se fornecido, os usuários serão adicionados a esta organização.
                </p>
              </div>
            </div>

            <Button
              type="submit"
              disabled={isLoading}
              className="w-full"
            >
              {isLoading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Processando...
                </>
              ) : (
                <>
                  <CheckCircle className="h-4 w-4 mr-2" />
                  Liberar Acesso
                </>
              )}
            </Button>
          </form>
        </CardContent>
      </Card>

      {results && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertCircle className="h-5 w-5 text-primary" />
              Resultados
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-3 gap-4">
              <div className="text-center p-4 bg-muted rounded-lg">
                <div className="text-2xl font-bold text-primary">
                  {results.data.summary.total}
                </div>
                <div className="text-sm text-muted-foreground">Total</div>
              </div>
              <div className="text-center p-4 bg-green-50 rounded-lg">
                <div className="text-2xl font-bold text-green-600">
                  {results.data.summary.successful}
                </div>
                <div className="text-sm text-muted-foreground">Sucessos</div>
              </div>
              <div className="text-center p-4 bg-red-50 rounded-lg">
                <div className="text-2xl font-bold text-red-600">
                  {results.data.summary.failed}
                </div>
                <div className="text-sm text-muted-foreground">Falhas</div>
              </div>
            </div>

            {results.data.results.length > 0 && (
              <div>
                <h4 className="font-medium mb-2">Usuários com acesso liberado:</h4>
                <div className="space-y-2">
                  {results.data.results.map((result, index) => (
                    <div key={index} className="flex items-center justify-between p-3 bg-green-50 border border-green-200 rounded-md">
                      <div>
                        <div className="font-medium text-green-800">
                          {result.caktoUserId}
                        </div>
                        <div className="text-sm text-green-600">
                          {result.email}
                        </div>
                      </div>
                      <Badge variant="secondary" className="bg-green-100 text-green-800">
                        Sucesso
                      </Badge>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {results.data.errors.length > 0 && (
              <div>
                <h4 className="font-medium mb-2">Erros:</h4>
                <div className="space-y-2">
                  {results.data.errors.map((error, index) => (
                    <div key={index} className="flex items-center justify-between p-3 bg-red-50 border border-red-200 rounded-md">
                      <div>
                        <div className="font-medium text-red-800">
                          {error.caktoUserId}
                        </div>
                        <div className="text-sm text-red-600">
                          {error.message}
                        </div>
                      </div>
                      <Badge variant="secondary" className="bg-red-100 text-red-800">
                        Erro
                      </Badge>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  )
}
