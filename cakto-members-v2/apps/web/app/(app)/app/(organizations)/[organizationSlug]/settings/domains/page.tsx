import { getActiveOrganization } from "@saas/auth/lib/server";
import { OrganizationDomainsForm } from "@saas/organizations/components/OrganizationDomainsForm";
import { SettingsList } from "@saas/shared/components/SettingsList";
import { notFound } from "next/navigation";

export default async function OrganizationDomainsPage({ params }: { params: Promise<{ organizationSlug: string }> }) {
  const { organizationSlug } = await params;
  const organization = await getActiveOrganization(organizationSlug);
  if (!organization) return notFound();

  return (
    <SettingsList>
      <OrganizationDomainsForm organizationId={organization.id} />
    </SettingsList>
  );
}


