import { CourseCreationWizard } from '@/modules/saas/organizations/course/components/CourseCreator/CourseCreationWizard'

interface CourseCreatePageProps {
  params: Promise<{
    organizationSlug: string
  }>
  searchParams?: Promise<{
    productId?: string
  }>
}

export default async function CourseCreatePage({ params, searchParams }: CourseCreatePageProps) {
  const { organizationSlug } = await params
  const { productId } = (await searchParams) || {}

  return (
    <CourseCreationWizard
      organizationSlug={organizationSlug}
      defaultCaktoProductId={productId}
    />
  )
}
