import { getActiveOrganization, getSession } from "@saas/auth/lib/server";
import { SupportPage } from "@saas/organizations/components/SupportPage";
import { Footer } from "@/modules/saas/shared/components/Footer";
import { UnifiedNavbar } from "@/modules/saas/shared/components/UnifiedNavbar";
import { getTranslations } from "next-intl/server";
import { redirect } from "next/navigation";

export async function generateMetadata() {
    const t = await getTranslations();

    return {
        title: "Suporte",
        description: "Central de suporte e ajuda para membros",
    };
}

export default async function SupportPageRoute({
    params,
}: {
    params: Promise<{ organizationSlug: string }>;
}) {
    const session = await getSession();
    const { organizationSlug } = await params;
    const organization = await getActiveOrganization(organizationSlug);

    if (!organization) {
        redirect("/app");
    }

    return (
        <>
            <UnifiedNavbar />
            <div className="min-h-screen bg-background">
                <SupportPage
                    organizationId={organization.id}
                    organizationName={organization.name}
                />
            </div>
            <Footer />
        </>
    );
}
