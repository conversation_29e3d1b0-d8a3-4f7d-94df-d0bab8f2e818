import { getSession } from "@saas/auth/lib/server";
import { redirect } from "next/navigation";

export type UserRole = "user" | "admin" | "producer";

export interface RoleGuardOptions {
	allowedRoles: UserRole[];
	redirectTo?: string;
}

export async function requireRole(options: RoleGuardOptions) {
	const session = await getSession();

	if (!session) {
		return redirect("/auth/login");
	}

	const userRole = session.user?.role as UserRole;
	const isAllowed = options.allowedRoles.includes(userRole);

	if (!isAllowed) {
		const redirectPath = options.redirectTo || "/app";
		return redirect(redirectPath);
	}

	return session;
}

export async function requireAdmin() {
	return requireRole({ allowedRoles: ["admin"] });
}

export async function requireProducer() {
	return requireRole({ allowedRoles: ["producer"] });
}

export async function requireAdminOrProducer() {
	return requireRole({ allowedRoles: ["admin", "producer"] });
}

export function hasRole(userRole: string | null | undefined, allowedRoles: UserRole[]): boolean {
	if (!userRole) return false;
	return allowedRoles.includes(userRole as UserRole);
}

export function isAdmin(userRole: string | null | undefined): boolean {
	return hasRole(userRole, ["admin"]);
}

export function isProducer(userRole: string | null | undefined): boolean {
	return hasRole(userRole, ["producer"]);
}

export function isUser(userRole: string | null | undefined): boolean {
	return hasRole(userRole, ["user"]) || userRole === null || userRole === undefined;
}