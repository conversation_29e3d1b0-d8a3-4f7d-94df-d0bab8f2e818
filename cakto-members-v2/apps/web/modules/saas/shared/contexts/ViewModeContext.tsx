"use client";

import { createContext, useContext, useState, ReactNode } from "react";
import { useSession } from "@saas/auth/hooks/use-session";
import { useActiveOrganization } from "@saas/organizations/hooks/use-active-organization";
import { isOrganizationAdmin } from "@repo/auth/lib/helper";

type ViewMode = "admin" | "student";

interface ViewModeContextType {
	viewMode: ViewMode;
	setViewMode: (mode: ViewMode) => void;
	isStudentView: boolean;
	toggleViewMode: () => void;
	canToggleViewMode: boolean;
}

const ViewModeContext = createContext<ViewModeContextType | undefined>(undefined);

interface ViewModeProviderProps {
	children: ReactNode;
	defaultMode?: ViewMode;
}

export function ViewModeProvider({ children, defaultMode = "admin" }: ViewModeProviderProps) {
	const [viewMode, setViewMode] = useState<ViewMode>(defaultMode);
	const { user, loaded: sessionLoaded } = useSession();
	const { activeOrganization, loaded: orgLoaded } = useActiveOrganization();

	const isStudentView = viewMode === "student";

	// Add safety checks to prevent errors during hydration
	const canToggleViewMode = Boolean(
		user?.role === "admin" ||
		(activeOrganization && isOrganizationAdmin(activeOrganization, user))
	);

	const toggleViewMode = () => {
		if (!canToggleViewMode) return;
		setViewMode(current => current === "admin" ? "student" : "admin");
	};

	const contextValue = {
		viewMode,
		setViewMode,
		isStudentView,
		toggleViewMode,
		canToggleViewMode
	};

	// Wait for both session and organization to be loaded before rendering
	if (!sessionLoaded || !orgLoaded) {
		return (
			<ViewModeContext.Provider value={{
				viewMode: defaultMode,
				setViewMode: () => {},
				isStudentView: defaultMode === "student",
				toggleViewMode: () => {},
				canToggleViewMode: false
			}}>
				{children}
			</ViewModeContext.Provider>
		);
	}

	return (
		<ViewModeContext.Provider value={contextValue}>
			{children}
		</ViewModeContext.Provider>
	);
}

export function useViewMode() {
	const context = useContext(ViewModeContext);
	if (context === undefined) {
		// Add more descriptive error message for debugging
		throw new Error("useViewMode must be used within a ViewModeProvider. Make sure the component is wrapped with ViewModeProvider.");
	}
	return context;
}
