"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { useFullOrganizationQuery, useUpdateOrganizationDomainsMutation, useCheckSubdomainQuery } from "@saas/organizations/lib/api";
import { SettingsItem } from "@saas/shared/components/SettingsItem";
import { Alert, AlertDescription } from "@ui/components/alert";
import { Button } from "@ui/components/button";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@ui/components/form";
import { Input } from "@ui/components/input";
import { config } from "@repo/config";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { useMemo } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import { CopyIcon, ExternalLinkIcon, CheckCircleIcon, XCircleIcon } from "lucide-react";

const schema = z.object({
  subdomain: z
    .string()
    .min(3, "Min. 3 caracteres")
    .max(63, "Máx. 63 caracteres")
    .regex(/^[a-z0-9-]+$/, "Use apenas letras minúsculas, números e hífens")
    .optional()
    .or(z.literal("")),
  customDomain: z.string().optional().or(z.literal("")),
});

type Values = z.infer<typeof schema>;

export function OrganizationDomainsForm({ organizationId }: { organizationId: string }) {
  const { data: org } = useFullOrganizationQuery(organizationId);
  const updateMutation = useUpdateOrganizationDomainsMutation();

  const form = useForm<Values>({
    resolver: zodResolver(schema),
    defaultValues: {
      subdomain: org?.subdomain || "",
      customDomain: org?.customDomain || "",
    },
    values: {
      subdomain: org?.subdomain || "",
      customDomain: org?.customDomain || "",
    },
  });

  const subdomain = form.watch("subdomain");
  const { data: check } = useCheckSubdomainQuery(subdomain || "");

  const baseDomain = useMemo(() => config.domains.baseDomain, []);

  const onSubmit = form.handleSubmit(async (values) => {
    await updateMutation.mutateAsync({ organizationId, subdomain: values.subdomain || undefined, customDomain: values.customDomain || undefined });
  });

  const copy = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
    } catch {}
  };

  const subdomainEnabled = config.domains.allowSubdomains !== false;
  const customEnabled = config.domains.allowCustomDomains !== false;

  return (
    <Form {...form}>
      <form onSubmit={onSubmit} className="space-y-8">
        <SettingsItem title="Subdomínio" description={`Use um subdomínio sob ${baseDomain}`}>
          <div className="grid gap-4 max-w-xl">
            <FormField
              control={form.control}
              name="subdomain"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Subdomínio</FormLabel>
                  <FormControl>
                    <div className="flex items-center gap-2">
                      <Input {...field} placeholder="meu-workspace" disabled={!subdomainEnabled} />
                      <span className="text-muted-foreground">.{baseDomain}</span>
                    </div>
                  </FormControl>
                  <FormDescription>
                    Ex.: {field.value || "meu-workspace"}.{baseDomain}
                  </FormDescription>
                  {!subdomainEnabled && (
                    <p className="text-sm text-muted-foreground">Subdomínios desabilitados nesta instância.</p>
                  )}
                  {subdomainEnabled && check && (
                    <div className="flex items-center gap-2">
                      {check.available ? (
                        <CheckCircleIcon className="h-4 w-4 text-green-600" />
                      ) : (
                        <XCircleIcon className="h-4 w-4 text-red-600" />
                      )}
                      <p className={`text-sm ${check.available ? "text-green-600" : "text-red-600"}`}>
                        {check.available ? "Disponível" : check.reason === "reserved" ? "Reservado" : "Indisponível"}
                      </p>
                    </div>
                  )}
                  <FormMessage />
                </FormItem>
              )}
            />

            <Alert>
              <AlertDescription>
                Sem configuração DNS necessária para subdomínio em {baseDomain}. Após salvar, o endereço ficará ativo em alguns minutos.
              </AlertDescription>
            </Alert>

            <Card>
              <CardHeader>
                <CardTitle>URL de acesso</CardTitle>
              </CardHeader>
              <CardContent className="flex items-center gap-2">
                <Input readOnly value={`https://${(form.getValues().subdomain || "meu-workspace")}.${baseDomain}`} />
                <Button type="button" variant="outline" onClick={() => copy(`https://${(form.getValues().subdomain || "meu-workspace")}.${baseDomain}`)}>
                  <CopyIcon className="h-4 w-4" />
                </Button>
              </CardContent>
            </Card>
          </div>
        </SettingsItem>

        <SettingsItem
          title="Domínio personalizado"
          description="Aponte seu domínio no provedor de DNS para nossa infraestrutura e depois informe aqui"
        >
          <div className="grid gap-4 max-w-2xl">
            <FormField
              control={form.control}
              name="customDomain"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Domínio (ex.: cursos.minhaempresa.com)</FormLabel>
                  <FormControl>
                    <Input {...field} placeholder="cursos.minhaempresa.com" disabled={!customEnabled} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="p-4 border rounded-lg space-y-3 text-sm">
              <p className="font-medium">Como configurar no seu DNS:</p>
              <ol className="list-decimal ml-5 space-y-2">
                <li>Se for um subdomínio (ex.: cursos.minhaempresa.com), crie um registro CNAME apontando para <code>aluno.cakto.com.br</code>.</li>
                <li>Se for domínio raiz (apex), crie um registro A apontando para <code>***********</code> ou use ALIAS/ANAME para <code>aluno.cakto.com.br</code> se seu DNS suportar.</li>
                <li>Para www.seudominio.com, crie um registro CNAME apontando para <code>aluno.cakto.com.br</code>.</li>
                <li>Após propagar (até 24h), informe o domínio acima e salve.</li>
              </ol>
              <p className="text-muted-foreground">Dica: A propagação do DNS pode levar até 24 horas. Entre em contato com nosso suporte se precisar de ajuda.</p>
              <div className="flex gap-2">
                <Button type="button" variant="outline" onClick={() => copy("aluno.cakto.com.br")}>CNAME alvo <CopyIcon className="h-4 w-4 ml-2" /></Button>
                <Button type="button" variant="outline" onClick={() => copy("***********")}>IP apex <CopyIcon className="h-4 w-4 ml-2" /></Button>
                <Button type="button" variant="ghost" asChild>
                  <a href="mailto:<EMAIL>" target="_blank" rel="noreferrer">
                    Contatar suporte <ExternalLinkIcon className="h-4 w-4 ml-2" />
                  </a>
                </Button>
              </div>
              {!customEnabled && (
                <p className="text-xs text-muted-foreground mt-2">Domínios personalizados desabilitados nesta instância.</p>
              )}
            </div>
          </div>
        </SettingsItem>

        <div className="flex justify-end">
          <Button type="submit" loading={updateMutation.isPending}>Salvar</Button>
        </div>
      </form>
    </Form>
  );
}


