"use client";

import { config } from "@repo/config";
import { useMemberAreaSettingsForSupportQuery } from "@saas/settings/lib/api";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@ui/components/card";
import { Button } from "@ui/components/button";
import { Badge } from "@ui/components/badge";
import { 
    HelpCircleIcon, 
    MailIcon, 
    ExternalLinkIcon, 
    BookOpenIcon,
    MessageSquareIcon,
    PhoneIcon,
    ClockIcon,
    CheckCircleIcon
} from "lucide-react";
import Link from "next/link";
import { useTranslations } from "next-intl";
import { cn } from "@ui/lib";

interface SupportPageProps {
    organizationId: string;
    organizationName?: string;
}

interface SupportSettings {
    memberAreaName: string;
    primaryColor: string;
    logoUrl?: string;
    supportEmail: string;
}

const DEFAULT_SUPPORT_SETTINGS: SupportSettings = {
    memberAreaName: "Cakto Members",
    primaryColor: "#0F7864",
    logoUrl: undefined,
    supportEmail: "<EMAIL>",
};

export function SupportPage({ organizationId, organizationName }: SupportPageProps) {
    const t = useTranslations();
    const { data: memberAreaSettings, isLoading } = useMemberAreaSettingsForSupportQuery(organizationId);

    // Merge settings with fallbacks
    const supportSettings: SupportSettings = {
        memberAreaName: memberAreaSettings?.memberAreaName || organizationName || DEFAULT_SUPPORT_SETTINGS.memberAreaName,
        primaryColor: memberAreaSettings?.primaryColor || DEFAULT_SUPPORT_SETTINGS.primaryColor,
        logoUrl: memberAreaSettings?.logoUrl || DEFAULT_SUPPORT_SETTINGS.logoUrl,
        supportEmail: memberAreaSettings?.supportEmail || DEFAULT_SUPPORT_SETTINGS.supportEmail,
    };

    const supportChannels = [
        {
            id: "email",
            title: "Email de Suporte",
            description: "Entre em contato conosco por email para questões gerais",
            icon: MailIcon,
            action: `mailto:${supportSettings.supportEmail}`,
            actionText: supportSettings.supportEmail,
            isPrimary: true,
        },
        {
            id: "help-docs",
            title: "Central de Ajuda",
            description: "Acesse nossa documentação completa e tutoriais",
            icon: BookOpenIcon,
            action: "https://ajuda.cakto.com.br/pt/",
            actionText: "Acessar Documentação",
            isExternal: true,
        },
        {
            id: "community",
            title: "Comunidade",
            description: "Participe da nossa comunidade para tirar dúvidas",
            icon: MessageSquareIcon,
            action: "#",
            actionText: "Acessar Comunidade",
            isExternal: true,
        },
    ];

    const quickHelp = [
        {
            title: "Como acessar meus cursos?",
            description: "Navegue até a seção 'Cursos' no menu principal",
        },
        {
            title: "Problemas com login?",
            description: "Verifique seu email e senha ou use a recuperação de senha",
        },
        {
            title: "Como atualizar meu perfil?",
            description: "Acesse 'Configurações' no menu do usuário",
        },
        {
            title: "Dúvidas sobre pagamento?",
            description: "Entre em contato com nosso suporte financeiro",
        },
    ];

    if (isLoading) {
        return (
            <div className="container mx-auto py-12 px-4">
                <div className="max-w-4xl mx-auto">
                    <div className="animate-pulse space-y-6">
                        <div className="h-8 bg-muted rounded w-1/3"></div>
                        <div className="h-4 bg-muted rounded w-2/3"></div>
                        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                            {[1, 2, 3].map((i) => (
                                <div key={i} className="h-48 bg-muted rounded-2xl"></div>
                            ))}
                        </div>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className="container mx-auto py-12 px-4">
            <div className="max-w-4xl mx-auto">
                {/* Header */}
                <div className="text-center mb-12">
                    <div className="flex items-center justify-center mb-4">
                        {supportSettings.logoUrl ? (
                            <img 
                                src={supportSettings.logoUrl} 
                                alt={supportSettings.memberAreaName}
                                className="h-12 w-auto"
                            />
                        ) : (
                            <div 
                                className="w-12 h-12 rounded-lg flex items-center justify-center text-white font-bold text-xl"
                                style={{ backgroundColor: supportSettings.primaryColor }}
                            >
                                {supportSettings.memberAreaName.charAt(0)}
                            </div>
                        )}
                    </div>
                    <h1 className="text-3xl font-bold mb-4">
                        Central de Suporte - {supportSettings.memberAreaName}
                    </h1>
                    <p className="text-muted-foreground text-lg max-w-2xl mx-auto">
                        Estamos aqui para ajudar! Encontre respostas para suas dúvidas ou entre em contato conosco.
                    </p>
                </div>

                {/* Support Channels */}
                <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 mb-12">
                    {supportChannels.map((channel) => {
                        const Icon = channel.icon;
                        return (
                            <Card key={channel.id} className={cn(
                                "relative overflow-hidden transition-all duration-200 hover:shadow-lg",
                                channel.isPrimary && "ring-2 ring-primary/20"
                            )}>
                                <CardHeader className="pb-4">
                                    <div className="flex items-center gap-3">
                                        <div 
                                            className="p-2 rounded-lg text-white"
                                            style={{ backgroundColor: supportSettings.primaryColor }}
                                        >
                                            <Icon className="w-5 h-5" />
                                        </div>
                                        <div>
                                            <CardTitle className="text-lg">{channel.title}</CardTitle>
                                            {channel.isPrimary && (
                                                <Badge status="info" className="text-xs">
                                                    Recomendado
                                                </Badge>
                                            )}
                                        </div>
                                    </div>
                                </CardHeader>
                                <CardContent className="pt-0">
                                    <CardDescription className="mb-4">
                                        {channel.description}
                                    </CardDescription>
                                    {channel.isExternal ? (
                                        <Link href={channel.action} target="_blank" rel="noopener noreferrer">
                                            <Button
                                                className="w-full"
                                                variant={channel.isPrimary ? "primary" : "outline"}
                                                style={channel.isPrimary ? { backgroundColor: supportSettings.primaryColor } : undefined}
                                            >
                                                {channel.actionText}
                                                <ExternalLinkIcon className="w-4 h-4 ml-2" />
                                            </Button>
                                        </Link>
                                    ) : (
                                        <Link href={channel.action}>
                                            <Button
                                                className="w-full"
                                                variant={channel.isPrimary ? "primary" : "outline"}
                                                style={channel.isPrimary ? { backgroundColor: supportSettings.primaryColor } : undefined}
                                            >
                                                {channel.actionText}
                                                {channel.id === "email" && <MailIcon className="w-4 h-4 ml-2" />}
                                            </Button>
                                        </Link>
                                    )}
                                </CardContent>
                            </Card>
                        );
                    })}
                </div>

                {/* Quick Help Section */}
                <Card className="mb-8">
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <HelpCircleIcon className="w-5 h-5" />
                            Perguntas Frequentes
                        </CardTitle>
                        <CardDescription>
                            Respostas rápidas para as dúvidas mais comuns
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="grid gap-4 md:grid-cols-2">
                            {quickHelp.map((item, index) => (
                                <div key={index} className="flex gap-3 p-4 rounded-lg bg-muted/50">
                                    <CheckCircleIcon 
                                        className="w-5 h-5 mt-0.5 flex-shrink-0" 
                                        style={{ color: supportSettings.primaryColor }}
                                    />
                                    <div>
                                        <h4 className="font-medium mb-1">{item.title}</h4>
                                        <p className="text-sm text-muted-foreground">{item.description}</p>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </CardContent>
                </Card>

                {/* Support Hours */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <ClockIcon className="w-5 h-5" />
                            Horário de Atendimento
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="grid gap-4 md:grid-cols-2">
                            <div>
                                <h4 className="font-medium mb-2">Suporte por Email</h4>
                                <p className="text-sm text-muted-foreground mb-1">Segunda a Sexta: 9h às 18h</p>
                                <p className="text-sm text-muted-foreground">Resposta em até 24 horas</p>
                            </div>
                            <div>
                                <h4 className="font-medium mb-2">Central de Ajuda</h4>
                                <p className="text-sm text-muted-foreground mb-1">Disponível 24/7</p>
                                <p className="text-sm text-muted-foreground">Acesso imediato à documentação</p>
                            </div>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </div>
    );
}
