"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { useMemberAreaSettingsQuery, useUpdateMemberAreaSettingsMutation } from "@saas/organizations/lib/api";
import { SettingsItem } from "@saas/shared/components/SettingsItem";
import { Button } from "@ui/components/button";
import {
    Form,
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
    FormDescription,
} from "@ui/components/form";
import { Input } from "@ui/components/input";
import { Switch } from "@ui/components/switch";
import { useTranslations } from "next-intl";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { z } from "zod";
import { MenuItemsEditor } from "./MenuItemsEditor";
import { ImageIcon, Loader2Icon } from "lucide-react";
import { useState, useEffect } from "react";
import { cn } from "@ui/lib";

const memberAreaSettingsSchema = z.object({
    memberAreaName: z.string().min(1, "Member area name is required"),
    primaryColor: z.string().regex(/^#[0-9A-F]{6}$/i, "Please enter a valid hex color"),
    logoUrl: z.string().url("Please enter a valid URL").optional().or(z.literal("")),
    commentsEnabled: z.boolean(),
    supportEmail: z.string().email("Please enter a valid email address"),
    menuItems: z.array(z.object({
        title: z.string().min(1, "Title is required"),
        url: z.string().min(1, "URL is required"),
        icon: z.string().optional(),
        position: z.number(),
    })),
    footer: z.object({
        text: z.string().optional(),
        links: z.array(z.object({
            title: z.string(),
            url: z.string(),
        })).optional(),
    }),
});

type MemberAreaSettingsFormValues = z.infer<typeof memberAreaSettingsSchema>;

export function MemberAreaSettingsForm({
    organizationId,
}: {
    organizationId: string;
}) {
    const t = useTranslations();
    const { data: settings, isLoading } = useMemberAreaSettingsQuery(organizationId);
    const updateMutation = useUpdateMemberAreaSettingsMutation();

    const form = useForm<MemberAreaSettingsFormValues>({
        resolver: zodResolver(memberAreaSettingsSchema),
        defaultValues: {
            memberAreaName: "",
            primaryColor: "#6366f1",
            logoUrl: "",
            commentsEnabled: false,
            supportEmail: "",
            menuItems: [],
            footer: { text: "", links: [] },
        },
    });

    // Update form when settings are loaded
    useEffect(() => {
        if (settings) {
            form.reset({
                memberAreaName: settings.memberAreaName || "",
                primaryColor: settings.primaryColor || "#6366f1",
                logoUrl: settings.logoUrl || "",
                commentsEnabled: settings.commentsEnabled || false,
                supportEmail: settings.supportEmail || "",
                menuItems: settings.menuItems || [],
                footer: settings.footer || { text: "", links: [] },
            });
        }
    }, [settings, form]);

    // Domain management moved to dedicated Organization Domains page

    const onSubmit = form.handleSubmit(async (values) => {
        try {
            await updateMutation.mutateAsync({
                organizationId,
                data: values,
            });

            toast.success(t("organizations.settings.memberArea.notifications.success"));
        } catch (error) {
            toast.error(t("organizations.settings.memberArea.notifications.error"));
        }
    });

    if (isLoading) {
        return (
            <div className="flex items-center justify-center min-h-[400px]">
                <div className="flex items-center gap-3">
                    <Loader2Icon className="h-6 w-6 animate-spin" />
                    <span className="text-muted-foreground">Loading member area settings...</span>
                </div>
            </div>
        );
    }

    const currentColor = form.watch("primaryColor");
    const currentLogo = form.watch("logoUrl");

    return (
        <div className="space-y-6">
            <Form {...form}>
                <form onSubmit={onSubmit} className="space-y-8">
                {/* Member Area Settings */}
                <SettingsItem
                    title="Área de Membros"
                    description="Configure a aparência e funcionalidades da sua área de membros"
                >
                    <div className="grid gap-6">
                        <FormField
                            control={form.control}
                            name="memberAreaName"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>{t("organizations.settings.memberArea.memberAreaName")}</FormLabel>
                                    <FormControl>
                                        <Input
                                            {...field}
                                            placeholder="My Member Area"
                                            className="max-w-md"
                                        />
                                    </FormControl>
                                    <FormDescription>
                                        Este nome será exibido no cabeçalho e título do navegador da sua área de membros
                                    </FormDescription>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />

                        {/* Domain fields moved to Organization > Domains page */}

                        <FormField
                            control={form.control}
                            name="supportEmail"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>{t("organizations.settings.memberArea.supportEmail")}</FormLabel>
                                    <FormControl>
                                        <Input
                                            {...field}
                                            type="email"
                                            placeholder="<EMAIL>"
                                            className="max-w-md"
                                        />
                                    </FormControl>
                                    <FormDescription>
                                        Endereço de email que será exibido para suporte aos membros
                                    </FormDescription>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                    </div>
                </SettingsItem>

                {/* Domain Settings now live under Organization Domains settings page */}

                {/* Appearance Settings */}
                <SettingsItem
                    title={t("organizations.settings.memberArea.appearance.title")}
                    description={t("organizations.settings.memberArea.appearance.description")}
                >
                    <div className="grid gap-6">
                        <FormField
                            control={form.control}
                            name="primaryColor"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>{t("organizations.settings.memberArea.primaryColor")}</FormLabel>
                                    <FormControl>
                                        <div className="flex items-center gap-4 max-w-md">
                                            <div className="relative">
                                                <Input
                                                    {...field}
                                                    type="color"
                                                    className="w-16 h-12 cursor-pointer border-0 p-1"
                                                />
                                                <div
                                                    className="absolute inset-0 rounded-md border-2 border-border pointer-events-none"
                                                    style={{ backgroundColor: currentColor }}
                                                />
                                            </div>
                                            <Input
                                                {...field}
                                                placeholder="#6366f1"
                                                className="flex-1"
                                            />
                                        </div>
                                    </FormControl>
                                    <FormDescription>
                                        Cor principal usada para botões, links e elementos destacados
                                    </FormDescription>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />

                        <FormField
                            control={form.control}
                            name="logoUrl"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>{t("organizations.settings.memberArea.logoUrl")}</FormLabel>
                                    <FormControl>
                                        <div className="space-y-4 max-w-md">
                                            <Input
                                                {...field}
                                                type="url"
                                                placeholder="https://example.com/logo.png"
                                            />
                                            {currentLogo && (
                                                <div className="flex items-center gap-3 p-3 border rounded-lg bg-muted/50">
                                                    <ImageIcon className="h-5 w-5 text-muted-foreground" />
                                                    <div className="flex-1 min-w-0">
                                                        <p className="text-sm font-medium truncate">Logo Preview</p>
                                                        <p className="text-xs text-muted-foreground truncate">{currentLogo}</p>
                                                    </div>
                                                    <img
                                                        src={currentLogo}
                                                        alt="Logo preview"
                                                        className="h-8 w-8 object-contain rounded"
                                                        onError={(e) => {
                                                            e.currentTarget.style.display = 'none';
                                                        }}
                                                    />
                                                </div>
                                            )}
                                        </div>
                                    </FormControl>
                                    <FormDescription>
                                        URL do logo da sua organização (recomendado: 200x200px ou maior)
                                    </FormDescription>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                    </div>
                </SettingsItem>

                {/* Features Settings */}
                <SettingsItem
                    title={t("organizations.settings.memberArea.features.title")}
                    description={t("organizations.settings.memberArea.features.description")}
                >
                    <FormField
                        control={form.control}
                        name="commentsEnabled"
                        render={({ field }) => (
                            <FormItem className="flex items-center justify-between p-4 border rounded-lg">
                                <div className="space-y-1">
                                    <FormLabel className="text-base">
                                        {t("organizations.settings.memberArea.commentsEnabled")}
                                    </FormLabel>
                                    <FormDescription>
                                        Permitir que membros comentem em cursos e aulas
                                    </FormDescription>
                                </div>
                                <FormControl>
                                    <Switch
                                        checked={field.value}
                                        onCheckedChange={field.onChange}
                                    />
                                </FormControl>
                            </FormItem>
                        )}
                    />
                </SettingsItem>

                {/* Menu Items Editor */}
                <MenuItemsEditor form={form} />

                <div className="flex justify-end">
                    <Button
                        type="submit"
                        disabled={updateMutation.isPending}
                        className="px-6"
                    >
                        {updateMutation.isPending && <Loader2Icon className="mr-2 h-4 w-4 animate-spin" />}
                        {t("organizations.settings.memberArea.save")}
                    </Button>
                </div>

                </form>
            </Form>
        </div>
    );
}
