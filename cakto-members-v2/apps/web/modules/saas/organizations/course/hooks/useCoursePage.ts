'use client'

import { useState, useEffect, useMemo } from 'react'
import { useQuery } from '@tanstack/react-query'
import { useRouter, useSearchParams } from 'next/navigation'
import { Course, Module, Lesson, CourseProgress, SidebarState } from '../types'
import { courseKeys } from './useCourses'
import { api } from '../lib/api'

interface UseCoursePage {
  course?: Course & { modules: (Module & { lessons: Lesson[] })[] }
  modules: Module[]
  lessons: Lesson[]
  allModuleLessons: Record<string, Lesson[]>
  currentLesson?: Lesson
  currentModule?: Module
  courseProgress: CourseProgress
  sidebarState: SidebarState
  isLoading: boolean
  hasError: boolean
  selectModule: (moduleId: string) => void
  selectLesson: (lessonId: string, moduleId: string) => void
  toggleSidebar: () => void
  goToNextLesson: () => void
  goToPrevLesson: () => void
  getAdjacentLessons: () => { nextLesson?: Lesson; prevLesson?: Lesson }
}

export function useCoursePage(courseId: string, organizationSlug: string, initialLessonId?: string): UseCoursePage {
  const router = useRouter()
  const [currentModuleId, setCurrentModuleId] = useState<string | undefined>()
  const [currentLessonId, setCurrentLessonId] = useState<string | undefined>(initialLessonId)
  const [sidebarState, setSidebarState] = useState<SidebarState>({ isOpen: true })


  const {
    data: course,
    isLoading,
    error
  } = useQuery({
    queryKey: courseKeys.course(courseId, organizationSlug),
    queryFn: () => api.getCourse(courseId, organizationSlug),
    enabled: !!courseId && !!organizationSlug
  })


  const modules = useMemo(() => {
    return course?.modules || []
  }, [course])

  const lessons = useMemo(() => {
    return course?.modules.flatMap(module => module.lessons) || []
  }, [course])


  const allModuleLessons = useMemo(() => {
    const grouped: Record<string, Lesson[]> = {}

    lessons.forEach(lesson => {
      if (!grouped[lesson.moduleId]) {
        grouped[lesson.moduleId] = []
      }
      grouped[lesson.moduleId].push(lesson)
    })

    // Sort lessons by position within each module
    Object.keys(grouped).forEach(moduleId => {
      grouped[moduleId].sort((a, b) => a.position - b.position)
    })

    return grouped
  }, [lessons])

  // Get current lesson and module
  const currentLesson = useMemo(() => {
    return lessons.find(lesson => lesson.id === currentLessonId)
  }, [lessons, currentLessonId])

  const currentModule = useMemo(() => {
    return modules.find(module => module.id === currentModuleId)
  }, [modules, currentModuleId])

  // Calculate course progress
  const courseProgress = useMemo((): CourseProgress => {
    const totalLessons = lessons.length
    const completedLessons = lessons.filter(lesson => lesson.userWatchedLessons?.isCompleted).length
    const progressPercentage = totalLessons > 0 ? Math.round((completedLessons / totalLessons) * 100) : 0

    return {
      totalLessons,
      completedLessons,
      progressPercentage
    }
  }, [lessons])

  // Initialize with initial lesson or first module and lesson if none selected
  useEffect(() => {
    if (modules.length > 0 && allModuleLessons) {
      // If we have an initial lesson ID, find its module and set both
      if (initialLessonId && !currentLessonId) {
        const lessonModule = modules.find(module =>
          allModuleLessons[module.id]?.some(lesson => lesson.id === initialLessonId)
        )
        if (lessonModule) {
          setCurrentModuleId(lessonModule.id)
          setCurrentLessonId(initialLessonId)
          return
        }
      }

      // Otherwise, initialize with first module and lesson if none selected
      if (!currentModuleId) {
        const firstModule = modules.sort((a, b) => a.position - b.position)[0]
        setCurrentModuleId(firstModule.id)

        const firstModuleLessons = allModuleLessons[firstModule.id]
        if (firstModuleLessons && firstModuleLessons.length > 0) {
          const firstLesson = firstModuleLessons[0]
          setCurrentLessonId(firstLesson.id)
          updateLessonUrl(firstLesson.id)
        }
      }
    }
  }, [modules, allModuleLessons, currentModuleId, initialLessonId, currentLessonId])

  // Get adjacent lessons for navigation
  const getAdjacentLessons = () => {
    if (!currentLessonId || !currentModuleId) {
      return { nextLesson: undefined, prevLesson: undefined }
    }

    const currentModuleLessons = allModuleLessons[currentModuleId] || []
    const currentLessonIndex = currentModuleLessons.findIndex(lesson => lesson.id === currentLessonId)

    let nextLesson: Lesson | undefined
    let prevLesson: Lesson | undefined

    // Check within current module
    if (currentLessonIndex > 0) {
      prevLesson = currentModuleLessons[currentLessonIndex - 1]
    }
    if (currentLessonIndex < currentModuleLessons.length - 1) {
      nextLesson = currentModuleLessons[currentLessonIndex + 1]
    }

    // Check adjacent modules if needed
    const sortedModules = modules.sort((a, b) => a.position - b.position)
    const currentModuleIndex = sortedModules.findIndex(module => module.id === currentModuleId)

    if (!prevLesson && currentModuleIndex > 0) {
      const prevModule = sortedModules[currentModuleIndex - 1]
      const prevModuleLessons = allModuleLessons[prevModule.id] || []
      if (prevModuleLessons.length > 0) {
        prevLesson = prevModuleLessons[prevModuleLessons.length - 1]
      }
    }

    if (!nextLesson && currentModuleIndex < sortedModules.length - 1) {
      const nextModule = sortedModules[currentModuleIndex + 1]
      const nextModuleLessons = allModuleLessons[nextModule.id] || []
      if (nextModuleLessons.length > 0) {
        nextLesson = nextModuleLessons[0]
      }
    }

    return { nextLesson, prevLesson }
  }

  // Function to update URL with lesson parameter
  const updateLessonUrl = (lessonId: string) => {
    const url = new URL(window.location.href)
    url.searchParams.set('lesson', lessonId)
    router.replace(url.pathname + url.search, { scroll: false })
  }

  // Navigation functions
  const selectModule = (moduleId: string) => {
    setCurrentModuleId(moduleId)
    const moduleLessons = allModuleLessons[moduleId]
    if (moduleLessons && moduleLessons.length > 0) {
      setCurrentLessonId(moduleLessons[0].id)
      updateLessonUrl(moduleLessons[0].id)
    }
  }

  const selectLesson = (lessonId: string, moduleId: string) => {
    setCurrentLessonId(lessonId)
    setCurrentModuleId(moduleId)
    updateLessonUrl(lessonId)
  }

  const goToNextLesson = () => {
    const { nextLesson } = getAdjacentLessons()
    if (nextLesson) {
      selectLesson(nextLesson.id, nextLesson.moduleId)
    }
  }

  const goToPrevLesson = () => {
    const { prevLesson } = getAdjacentLessons()
    if (prevLesson) {
      selectLesson(prevLesson.id, prevLesson.moduleId)
    }
  }

  const toggleSidebar = () => {
    setSidebarState(prev => ({ isOpen: !prev.isOpen }))
  }

  const hasError = !!error

  return {
    course,
    modules,
    lessons,
    allModuleLessons,
    currentLesson,
    currentModule,
    courseProgress,
    sidebarState,
    isLoading,
    hasError,
    selectModule,
    selectLesson,
    toggleSidebar,
    goToNextLesson,
    goToPrevLesson,
    getAdjacentLessons
  }
}
