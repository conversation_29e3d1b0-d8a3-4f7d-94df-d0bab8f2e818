'use client'

import { useState } from 'react'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { CourseCreationData, Course, Module, Lesson } from '../types'
import { api } from '../lib/api'
import { courseKeys } from './useCourses'

export function useCourseCreation() {
  const [isCreating, setIsCreating] = useState(false)
  const queryClient = useQueryClient()

  const createCourseMutation = useMutation({
    mutationFn: async ({ courseData, organizationSlug }: { courseData: CourseCreationData; organizationSlug: string }) => {

      const course = await api.createCourse({
        name: courseData.basicInfo.name,
        description: courseData.basicInfo.description,
        shortDescription: courseData.basicInfo.shortDescription,
        logo: courseData.basicInfo.logo,
        community: courseData.basicInfo.community,
        link: courseData.basicInfo.link,
      })

      if (courseData.basicInfo.caktoProductId) {
        try {
          await api.createCourseProductMapping(course.id, {
            caktoProductId: courseData.basicInfo.caktoProductId,
            caktoProductName: courseData.basicInfo.name,
          })
        } catch (err: any) {
          const message = String(err?.message || '')
          if (message.includes('PRODUCT_ALREADY_ASSOCIATED')) {
            // If product is already mapped to another course, abort creation flow with error to caller
            throw new Error('Este produto já está associado a outro curso. Abriremos a edição do curso existente.')
          }
          throw err
        }
      }

      if (courseData.structure.modules.length > 0) {
        for (const moduleData of courseData.structure.modules) {
          const module = await api.createModule(course.id, {
            name: moduleData.name,
            description: moduleData.description,
            position: moduleData.position,
            cover: moduleData.cover,
          })


          if (moduleData.lessons.length > 0) {
            for (const lessonData of moduleData.lessons) {
              await api.createLesson(module.id, {
                name: lessonData.name,
                description: lessonData.description,
                videoUrl: lessonData.videoUrl,
                position: lessonData.position,
                thumbnail: lessonData.thumbnail,
                duration: lessonData.duration,
                externalLink: lessonData.externalLink,
              })
            }
          }
        }
      }

      return course
    },
    onSuccess: (course) => {

      queryClient.invalidateQueries({ queryKey: courseKeys.all })
    },
    onError: (error) => {
      console.error('Erro ao criar curso:', error)
    }
  })

  const createCourse = async (courseData: CourseCreationData, organizationSlug: string): Promise<string> => {
    setIsCreating(true)
    try {
      const course = await createCourseMutation.mutateAsync({ courseData, organizationSlug })
      return course.id
    } finally {
      setIsCreating(false)
    }
  }

  return {
    createCourse,
    isCreating: isCreating || createCourseMutation.isPending,
    error: createCourseMutation.error,
  }
}


export function useCourseTemplates() {
  return {
    templates: [
      {
        id: 'marketing-digital',
        name: 'Curso de Marketing Digital',
        description: 'Template para cursos de marketing digital e vendas online',
        category: 'Marketing',
        modules: [
          {
            name: 'Fundamentos do Marketing Digital',
            description: 'Conceitos básicos e estratégias fundamentais',
            lessons: [
              { name: 'Introdução ao Marketing Digital', description: 'Visão geral e conceitos básicos' },
              { name: 'Público-alvo e Personas', description: 'Como identificar e segmentar seu público' },
            ]
          },
          {
            name: 'Estratégias de Conteúdo',
            description: 'Criação e distribuição de conteúdo relevante',
            lessons: [
              { name: 'Planejamento de Conteúdo', description: 'Como criar um calendário editorial' },
              { name: 'Redes Sociais', description: 'Estratégias para cada plataforma' },
            ]
          },
          {
            name: 'Conversão e Vendas',
            description: 'Técnicas para converter visitantes em clientes',
            lessons: [
              { name: 'Funnels de Venda', description: 'Criando jornadas de conversão' },
              { name: 'Copywriting', description: 'Técnicas de persuasão e vendas' },
              { name: 'Análise de Resultados', description: 'Métricas e otimização' },
            ]
          },
        ]
      },
      {
        id: 'design-basic',
        name: 'Curso de Design Básico',
        description: 'Template para cursos introdutórios de design',
        category: 'Design',
        modules: [
          {
            name: 'Fundamentos do Design',
            description: 'Princípios básicos de design',
            lessons: [
              { name: 'Teoria das cores', description: 'Como usar cores efetivamente' },
              { name: 'Tipografia', description: 'Escolhendo e combinando fontes' },
            ]
          },
          {
            name: 'Ferramentas',
            description: 'Dominando as ferramentas de design',
            lessons: [
              { name: 'Introdução ao Figma', description: 'Interface e funcionalidades básicas' },
              { name: 'Criando seu primeiro design', description: 'Projeto prático guiado' },
            ]
          },
        ]
      },
    ],
  }
}
