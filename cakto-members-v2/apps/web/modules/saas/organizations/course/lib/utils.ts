/**
 * Utility functions for the course editor
 */

/**
 * Format duration from seconds to MM:SS or HH:MM:SS format
 */
export function formatDuration(seconds: number): string {
	// biome-ignore lint/style/useBlockStatements: <explanation>
	if (seconds < 0) return "00:00";

	const hours = Math.floor(seconds / 3600);
	const minutes = Math.floor((seconds % 3600) / 60);
	const remainingSeconds = Math.floor(seconds % 60);

	if (hours > 0) {
		return `${hours.toString().padStart(2, "0")}:${minutes.toString().padStart(2, "0")}:${remainingSeconds.toString().padStart(2, "0")}`;
	}

	return `${minutes.toString().padStart(2, "0")}:${remainingSeconds.toString().padStart(2, "0")}`;
}

/**
 * Parse duration string (MM:SS or HH:MM:SS) to seconds
 */
export function parseDuration(duration: string): number {
	// biome-ignore lint/style/useBlockStatements: <explanation>
	if (!duration) return 0;

	const parts = duration.split(":").map(Number);

	if (parts.length === 2) {
		// MM:SS format
		return parts[0] * 60 + parts[1];
		// biome-ignore lint/style/noUselessElse: <explanation>
	} else if (parts.length === 3) {
		// HH:MM:SS format
		return parts[0] * 3600 + parts[1] * 60 + parts[2];
	}

	return 0;
}

/**
 * Validate if a URL is a valid video URL
 */
export function validateVideoUrl(url: string): boolean {
	// biome-ignore lint/style/useBlockStatements: <explanation>
	if (!url) return false;

	try {
		const urlObj = new URL(url);

		// Check for common video hosting domains
		const validDomains = [
			"youtube.com",
			"youtu.be",
			"vimeo.com",
			"bunnycdn.com",
			"video.bunnycdn.com",
			"wistia.com",
			"jwplatform.com",
		];

		return validDomains.some(
			(domain) =>
				urlObj.hostname.includes(domain) ||
				urlObj.hostname.endsWith(domain),
		);
	} catch {
		return false;
	}
}

/**
 * Get file extension from filename
 */
export function getFileExtension(filename: string): string {
	const lastDotIndex = filename.lastIndexOf(".");
	// biome-ignore lint/style/useBlockStatements: <explanation>
	if (lastDotIndex === -1) return "";
	return filename.slice(lastDotIndex + 1).toLowerCase();
}

/**
 * Validate file type for uploads
 */
export function validateFileType(file: File, allowedTypes: string[]): boolean {
	const extension = getFileExtension(file.name);
	return allowedTypes.includes(extension) || allowedTypes.includes(file.type);
}

/**
 * Format file size in human readable format
 */
export function formatFileSize(bytes: number): string {
	// biome-ignore lint/style/useBlockStatements: <explanation>
	if (bytes === 0) return "0 B";

	const k = 1024;
	const sizes = ["B", "KB", "MB", "GB"];
	const i = Math.floor(Math.log(bytes) / Math.log(k));

	return `${
		// biome-ignore lint/style/useExponentiationOperator: <explanation>
		Number.parseFloat((bytes / Math.pow(k, i)).toFixed(1))
	} ${sizes[i]}`;
}

/**
 * Generate a unique ID
 */
export function generateId(): string {
	return Date.now().toString(36) + Math.random().toString(36).substr(2);
}

/**
 * Debounce function
 */
export function debounce<T extends (...args: any[]) => any>(
	func: T,
	wait: number,
): (...args: Parameters<T>) => void {
	let timeout: NodeJS.Timeout;

	return (...args: Parameters<T>) => {
		clearTimeout(timeout);
		timeout = setTimeout(
			() => func(...args),
			wait,
		) as unknown as NodeJS.Timeout;
	};
}
export function throttle<T extends (...args: any[]) => any>(
	func: T,
	limit: number,
): (...args: Parameters<T>) => void {
	let inThrottle: boolean;

	return (...args: Parameters<T>) => {
		if (!inThrottle) {
			func(...args);
			inThrottle = true;
			// biome-ignore lint/suspicious/noAssignInExpressions: <explanation>
			setTimeout(() => (inThrottle = false), limit);
		}
	};
}

/**
 * Validate image dimensions
 */
export function validateImageDimensions(
	file: File,
	maxWidth: number,
	maxHeight: number,
): Promise<boolean> {
	return new Promise((resolve) => {
		const img = new Image();
		const url = URL.createObjectURL(file);

		img.onload = () => {
			URL.revokeObjectURL(url);
			resolve(img.width <= maxWidth && img.height <= maxHeight);
		};

		img.onerror = () => {
			URL.revokeObjectURL(url);
			resolve(false);
		};

		img.src = url;
	});
}

/**
 * Create image thumbnail
 */
export function createImageThumbnail(
	file: File,
	maxWidth: number,
	maxHeight: number,
	quality = 0.8,
): Promise<Blob> {
	return new Promise((resolve, reject) => {
		const canvas = document.createElement("canvas");
		const ctx = canvas.getContext("2d");
		const img = new Image();

		if (!ctx) {
			reject(new Error("Canvas context not available"));
			return;
		}

		img.onload = () => {
			// Calculate new dimensions
			let { width, height } = img;

			if (width > height) {
				if (width > maxWidth) {
					height = (height * maxWidth) / width;
					width = maxWidth;
				}
			} else {
				if (height > maxHeight) {
					width = (width * maxHeight) / height;
					height = maxHeight;
				}
			}

			canvas.width = width;
			canvas.height = height;

			// Draw and compress
			ctx.drawImage(img, 0, 0, width, height);

			canvas.toBlob(
				(blob) => {
					if (blob) {
						resolve(blob);
					} else {
						reject(new Error("Failed to create thumbnail"));
					}
				},
				"image/jpeg",
				quality,
			);
		};

		img.onerror = () => reject(new Error("Failed to load image"));
		img.src = URL.createObjectURL(file);
	});
}

/**
 * Sort array by multiple criteria
 */
export function sortBy<T>(
	array: T[],
	...criteria: Array<(item: T) => any>
): T[] {
	return [...array].sort((a, b) => {
		for (const criterion of criteria) {
			const aVal = criterion(a);
			const bVal = criterion(b);

			// biome-ignore lint/style/useBlockStatements: <explanation>
			if (aVal < bVal) return -1;
			// biome-ignore lint/style/useBlockStatements: <explanation>
			if (aVal > bVal) return 1;
		}
		return 0;
	});
}

/**
 * Deep clone object
 */
export function deepClone<T>(obj: T): T {
	// biome-ignore lint/style/useBlockStatements: <explanation>
	if (obj === null || typeof obj !== "object") return obj;
	// biome-ignore lint/style/useBlockStatements: <explanation>
	if (obj instanceof Date) return new Date(obj.getTime()) as unknown as T;
	// biome-ignore lint/suspicious/useIsArray: <explanation>
	// biome-ignore lint/style/useBlockStatements: <explanation>
	if (obj instanceof Array)
		return obj.map((item) => deepClone(item)) as unknown as T;
	if (typeof obj === "object") {
		const clonedObj = {} as { [key: string]: any };
		for (const key in obj) {
			// biome-ignore lint/suspicious/noPrototypeBuiltins: <explanation>
			if (obj.hasOwnProperty(key)) {
				clonedObj[key] = deepClone(obj[key]);
			}
		}
		return clonedObj as T;
	}
	return obj;
}

/**
 * Check if object is empty
 */
export function isEmpty(obj: any): boolean {
	// biome-ignore lint/style/useBlockStatements: <explanation>
	if (obj == null) return true;
	// biome-ignore lint/style/useBlockStatements: <explanation>
	if (Array.isArray(obj) || typeof obj === "string") return obj.length === 0;
	// biome-ignore lint/style/useBlockStatements: <explanation>
	if (typeof obj === "object") return Object.keys(obj).length === 0;
	return false;
}

/**
 * Capitalize first letter of string
 */
export function capitalize(str: string): string {
	// biome-ignore lint/style/useBlockStatements: <explanation>
	if (!str) return str;
	return str.charAt(0).toUpperCase() + str.slice(1);
}

/**
 * Truncate string with ellipsis
 */
export function truncate(str: string, length: number): string {
	// biome-ignore lint/style/useBlockStatements: <explanation>
	if (!str || str.length <= length) return str;
	// biome-ignore lint/style/useTemplate: <explanation>
	return str.slice(0, length) + "...";
}
