export interface User {
  id: string
  name?: string
  email: string
  role: 'admin' | 'USER'
  emailVerified: boolean
  banned?: boolean
  banReason?: string
  banExpires?: Date
  image?: string
  createdAt: Date | string
  updatedAt: Date | string
}

export interface Course {
  id: string
  name: string
  description?: string
  shortDescription?: string
  community?: string
  links?: string
  link?: string
  logo?: string
  createdAt: string
  updatedAt: string
  courseBanners?: CourseBanner[]
}

export interface Module {
  id: string
  name: string
  description?: string
  position: number
  cover?: string
  coverImage?: string
  courseId: string
  createdAt: Date | string
  updatedAt: Date | string
  totalLessonsDuration?: string
}

export interface Lesson {
  id: string
  name: string
  description?: string
  videoUrl?: string
  position: number
  moduleId: string
  thumbnail?: string
  duration?: string
  lessonDuration?: string
  createdAt: Date | string
  updatedAt: Date | string
  externalLink?: string
  userWatchedLessons?: {
    isCompleted: boolean
    currentTime?: string
    duration?: string
    rating?: number
  }
}

export interface CourseBannerButton {
  id: number
  title: string
  link: string
  color: string
  userId: string
}

export interface CourseBanner {
  id: number
  title: string
  description?: string
  position: number
  image?: string
  createdAt: string
  courseBannerButtons?: CourseBannerButton[]
}

export interface WatchedLesson {
  id: number
  userId: string
  lessonId: string
  rating?: number
  isCompleted: boolean
  currentTime?: string
  duration?: string
  createdAt: Date | string
  updatedAt: Date | string
}

export interface LessonComment {
  id: number
  comment: string
  createdAt: string
  user: {
    id: string
    name?: string
    image?: string
  }
  lessonCommentReplies: Array<{
    id: number
    replyComment: string
    createdAt: string
    user: {
      id: string
      name?: string
      image?: string
    }
  }>
}

export interface LessonFile {
  id: number
  title: string
  file?: string
  fileSize?: string
  createdAt: string
}

export interface ApiResponse<T> {
  data?: T
  message?: string
  error?: string
  success?: boolean
}

export interface ApiError {
  message: string
  code?: string
  field?: string
  details?: Record<string, any>
}

export interface CourseProgress {
  totalLessons: number
  completedLessons: number
  progressPercentage: number
}

export interface SidebarState {
  isOpen: boolean
  expandedModules?: Set<string>
}

export interface WatchLessonRequest {
  lessonId: string
  data: {
    lessonId: string
    currentTime: string
    isCompleted: boolean
  }
}

export interface CourseEditorProps {
  courseId?: string
  organizationSlug: string
  mode: 'create' | 'edit'
}

export interface CourseHeaderProps {
  course: Course
  onEdit: () => void
  onSave: (data: Partial<Course>) => void
}

export interface ModuleListProps {
  modules: Module[]
  onAddModule: () => void
  onEditModule: (module: Module) => void
  onDeleteModule: (moduleId: string) => void
  onReorderModules: (modules: Module[]) => void
}

export interface LessonListProps {
  lessons: Lesson[]
  moduleId: string
  onAddLesson: (moduleId: string) => void
  onEditLesson: (lesson: Lesson) => void
  onDeleteLesson: (lessonId: string) => void
  onReorderLessons: (lessons: Lesson[]) => void
}

export interface VideoLibraryProps {
  isOpen: boolean
  isMinimized: boolean
  onSelectVideo: (video: { url: string; thumbnail: string; duration: string; title: string }) => void
  onClose: () => void
  onMinimize: () => void
}

export interface ModuleFormData {
  name: string
  description?: string
  coverImage?: string
}

export interface LessonFormData {
  name: string
  description?: string
  videoUrl?: string
  thumbnail?: string | File
  status: 'draft' | 'published'
  duration?: string
}


export interface CourseCreationWizardProps {
  organizationSlug: string
  onComplete: (courseId: string) => void
  onCancel: () => void
}

export interface WizardStep {
  id: string
  title: string
  description: string
  component: React.ComponentType<any>
  isCompleted: boolean
  isOptional?: boolean
}

export interface CourseCreationData {
  basicInfo: {
    name: string
    description: string
    shortDescription: string
    category: string
    tags: string[]
    difficulty: 'beginner' | 'intermediate' | 'advanced'
    logo?: string
    community?: string
    link?: string
    caktoProductId?: string
  }
  structure: {
    modules: ModuleCreationData[]
    estimatedDuration: string
  }
  content: {
    videos: VideoUploadData[]
    materials: MaterialUploadData[]
  }
}

export interface ModuleCreationData {
  id?: string
  name: string
  description?: string
  position: number
  cover?: string
  lessons: LessonCreationData[]
}

export interface LessonCreationData {
  id?: string
  name: string
  description?: string
  videoUrl?: string
  position: number
  thumbnail?: string
  duration?: string
  externalLink?: string
}

export interface VideoUploadData {
  id: string
  file: File
  name: string
  duration?: string
  thumbnail?: string
  uploadProgress: number
  status: 'pending' | 'uploading' | 'completed' | 'error'
  moduleId?: string
  lessonId?: string
}

export interface MaterialUploadData {
  id: string
  file: File
  name: string
  type: string
  size: number
  uploadProgress: number
  status: 'pending' | 'uploading' | 'completed' | 'error'
  moduleId?: string
  lessonId?: string
}

export interface CourseTemplate {
  id: string
  name: string
  description: string
  category: string
  modules: {
    name: string
    description: string
    lessons: {
      name: string
      description: string
    }[]
  }[]
}

export interface SmartSuggestion {
  type: 'module' | 'lesson' | 'description' | 'tags'
  content: string
  confidence: number
}

export interface DragItem {
  id: string
  type: 'module' | 'lesson'
  index: number
}
