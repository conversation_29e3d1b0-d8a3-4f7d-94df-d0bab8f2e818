'use client'

import { useState, useCallback } from 'react'
import { CourseCreationData } from '../../../types'
import { Button } from '@/modules/ui/components/button'
import { Input } from '@/modules/ui/components/input'
import { Label } from '@/modules/ui/components/label'
import { Textarea } from '@/modules/ui/components/textarea'
import { Card, CardContent, CardHeader, CardTitle } from '@/modules/ui/components/card'
import { Badge } from '@/modules/ui/components/badge'
import { Switch } from '@/modules/ui/components/switch'
import {
  Settings,
  Info,
  Link,
  ExternalLink
} from 'lucide-react'
import { toast } from 'sonner'

interface SettingsStepProps {
  data: CourseCreationData
  onUpdate: (data: Partial<CourseCreationData>) => void
  onNext: () => void
  onPrevious: () => void
  organizationSlug: string
}

export function SettingsStep({ data, onUpdate, onNext, onPrevious }: SettingsStepProps) {
  const [community, setCommunity] = useState(data.basicInfo.community || '')
  const [link, setLink] = useState(data.basicInfo.link || '')
  const [caktoProductId, setCaktoProductId] = useState(data.basicInfo.caktoProductId || '')
  const [enableCaktoIntegration, setEnableCaktoIntegration] = useState(!!data.basicInfo.caktoProductId)

  const handleNext = useCallback(() => {
    onUpdate({
      basicInfo: {
        ...data.basicInfo,
        community,
        link,
        caktoProductId: enableCaktoIntegration ? caktoProductId : undefined
      }
    })
    onNext()
  }, [onUpdate, onNext, data.basicInfo, community, link, caktoProductId, enableCaktoIntegration])

  const handleCaktoIntegrationToggle = useCallback((enabled: boolean) => {
    setEnableCaktoIntegration(enabled)
    if (!enabled) {
      setCaktoProductId('')
    }
  }, [])

  const handleGoToCakto = useCallback(() => {
    if (caktoProductId) {
      window.open(`https://app.cakto.com.br/dashboard/products/${caktoProductId}/edit?tab=general`, '_blank')
    } else {
      window.open('https://app.cakto.com.br/dashboard/products/create', '_blank')
    }
  }, [caktoProductId])

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5 text-primary" />
            Configurações do Curso
          </CardTitle>
          <p className="text-muted-foreground">
            Configure as informações adicionais do seu curso
          </p>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="community">Comunidade</Label>
              <Input
                id="community"
                placeholder="Nome da comunidade relacionada"
                value={community}
                onChange={(e) => setCommunity(e.target.value)}
              />
              <p className="text-sm text-muted-foreground">
                Nome da comunidade ou grupo relacionado ao curso (opcional)
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="link">Link do Curso</Label>
              <Input
                id="link"
                placeholder="https://exemplo.com/curso"
                value={link}
                onChange={(e) => setLink(e.target.value)}
              />
              <p className="text-sm text-muted-foreground">
                Link externo relacionado ao curso (opcional)
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Link className="h-5 w-5 text-primary" />
            Integração com Cakto
          </CardTitle>
          <p className="text-muted-foreground">
            Associe este curso a um produto do Cakto para liberar acesso automático aos compradores
          </p>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label htmlFor="cakto-integration">Integração com Cakto</Label>
              <p className="text-sm text-muted-foreground">
                Ative para associar este curso a um produto do Cakto
              </p>
            </div>
            <Switch
              id="cakto-integration"
              checked={enableCaktoIntegration}
              onCheckedChange={handleCaktoIntegrationToggle}
            />
          </div>

          {enableCaktoIntegration && (
            <div className="space-y-4 p-4 border rounded-lg bg-muted/50">
              <div className="space-y-2">
                <Label htmlFor="cakto-product-id">ID do Produto Cakto</Label>
                <div className="flex gap-2">
                  <Input
                    id="cakto-product-id"
                    placeholder="ID do produto (ex: ff3fdf61-e88f-43b5-982a-32d50f112414)"
                    value={caktoProductId}
                    onChange={(e) => setCaktoProductId(e.target.value)}
                  />
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={handleGoToCakto}
                    className="whitespace-nowrap"
                  >
                    <ExternalLink className="h-4 w-4 mr-2" />
                    Ir para Cakto
                  </Button>
                </div>
                <p className="text-sm text-muted-foreground">
                  ID do produto no Cakto. Quando um cliente comprar este produto,
                  ele receberá acesso automático a este curso.
                </p>
              </div>

              {caktoProductId && (
                <div className="flex items-center gap-2 p-3 bg-green-50 border border-green-200 rounded-md">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span className="text-sm text-green-700">
                    Produto associado: {caktoProductId}
                  </span>
                </div>
              )}

              <div className="bg-blue-50 border border-blue-200 rounded-md p-3">
                <div className="flex items-start gap-2">
                  <Info className="h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0" />
                  <div className="text-sm text-blue-700">
                    <p className="font-medium mb-1">Como funciona:</p>
                    <ul className="space-y-1 text-xs">
                      <li>• Crie um produto no Cakto com "Content Delivery: Área de membros cakto"</li>
                      <li>• Cole o ID do produto no campo acima</li>
                      <li>• Quando alguém comprar o produto, receberá acesso automático ao curso</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      <div className="flex justify-between">
        <Button variant="outline" onClick={onPrevious}>
          Anterior
        </Button>
        <Button onClick={handleNext}>
          Continuar
        </Button>
      </div>
    </div>
  )
}
