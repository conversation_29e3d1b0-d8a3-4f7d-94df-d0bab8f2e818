"use client";

import { <PERSON><PERSON> } from "@/modules/ui/components/button";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/modules/ui/components/card";
import { Skeleton } from "@/modules/ui/components/skeleton";
import {
	Ta<PERSON>,
	<PERSON><PERSON><PERSON>ontent,
	<PERSON><PERSON><PERSON><PERSON>,
	TabsTrigger,
} from "@/modules/ui/components/tabs";
import { Badge } from "@/modules/ui/components/badge";
import { Input } from "@/modules/ui/components/input";
import { Textarea } from "@/modules/ui/components/textarea";
import { StarRating } from "@/modules/ui/components/star-rating";
import { VideoPlayer } from "./VideoPlayer";
import { CourseSidebar } from "./CourseSidebar";
import { CourseHeader } from "./CourseHeader";
import { LessonContent } from "./LessonContent";
import { CoursePageSkeleton, CourseErrorState } from "./skeletons";
import { useCoursePage } from "../hooks/useCoursePage";
import {
	useLessonComments,
	useLessonFiles,
	useAddLessonComment,
	useReplyLessonComment,
	useToggleLessonCompletion,
	useRateLesson,
	useDownloadLessonFile
} from "../hooks/useCourses";
import {
	ArrowLeft,
	BookOpen,
	Clock,
	MessageCircle,
	Download,
	ChevronLeft,
	ChevronRight,
	Menu,
	Play,
	Check,
	CheckCircle,
	Circle,
	Reply,
	FileText,
	File,
	Star,
	Loader2,
} from "lucide-react";
import { motion } from "framer-motion";
import { useSession } from "@/modules/saas/auth/hooks/use-session";
import { redirect } from "next/navigation";
import { cn } from "@/lib/utils";
import type { LessonFile } from "../types";
import { useState } from "react";

interface CoursePageProps {
	courseId: string;
	organizationSlug: string;
	initialLessonId?: string;
}

export function CoursePage({ courseId, organizationSlug, initialLessonId }: CoursePageProps) {
	const { user } = useSession();
	const [commentText, setCommentText] = useState("");
	const [replyTexts, setReplyTexts] = useState<Record<number, string>>({});
	const [showReplyForm, setShowReplyForm] = useState<Record<number, boolean>>({});
	const [downloadingFiles, setDownloadingFiles] = useState<Set<number>>(new Set());

	const {
		course,
		modules,
		lessons,
		allModuleLessons,
		currentLesson,
		currentModule,
		courseProgress,
		sidebarState,
		isLoading,
		hasError,
		selectModule,
		selectLesson,
		toggleSidebar,
		goToNextLesson,
		goToPrevLesson,
		getAdjacentLessons,
	} = useCoursePage(courseId, organizationSlug, initialLessonId);

	// Fetch lesson comments and files for current lesson
	const { data: comments = [], isLoading: commentsLoading } =
		useLessonComments(currentLesson?.id || "");

	const { data: files = [], isLoading: filesLoading } = useLessonFiles(
		currentLesson?.id || "",
	);

	// Mutations
	const { mutate: addComment, isPending: isAddingComment } = useAddLessonComment();
	const { mutate: replyToComment, isPending: isReplying } = useReplyLessonComment();
	const { mutate: toggleCompletion, isPending: isTogglingCompletion } = useToggleLessonCompletion();
	const { mutate: rateLesson, isPending: isRating } = useRateLesson();
	const { mutate: downloadFile } = useDownloadLessonFile();

	// Redirect if user is not authenticated
	if (!user) {
		redirect("/auth/login");
	}

	// Error state
	if (hasError) {
		return <CourseErrorState onRetry={() => window.location.reload()} />;
	}

	// Loading state
	if (isLoading) {
		return <CoursePageSkeleton />;
	}

	const { nextLesson, prevLesson } = getAdjacentLessons();

	const handleAddComment = () => {
		if (currentLesson?.id && commentText.trim()) {
			addComment({
				lessonId: currentLesson.id,
				comment: commentText.trim()
			}, {
				onSuccess: () => {
					setCommentText("");
				}
			});
		}
	};

	const handleReplyToComment = (commentId: number) => {
		const replyText = replyTexts[commentId];
		if (currentLesson?.id && replyText?.trim()) {
			replyToComment({
				lessonId: currentLesson.id,
				commentId,
				reply: replyText.trim()
			}, {
				onSuccess: () => {
					setReplyTexts(prev => ({ ...prev, [commentId]: "" }));
					setShowReplyForm(prev => ({ ...prev, [commentId]: false }));
				}
			});
		}
	};

	const handleToggleCompletion = () => {
		if (currentLesson?.id) {
			const isCurrentlyCompleted = currentLesson.userWatchedLessons?.isCompleted || false;
			toggleCompletion({
				lessonId: currentLesson.id,
				isCompleted: !isCurrentlyCompleted
			});
		}
	};

	const handleRateLesson = (rating: number) => {
		if (currentLesson?.id) {
			rateLesson({
				lessonId: currentLesson.id,
				rating
			});
		}
	};

	const handleDownloadFile = async (file: LessonFile) => {
		if (currentLesson?.id) {
			setDownloadingFiles(prev => new Set(prev).add(file.id));

			try {
				downloadFile({
					lessonId: currentLesson.id,
					fileId: file.id
				}, {
					onSuccess: (blob) => {
						// Create download link
						const url = window.URL.createObjectURL(blob);
						const link = document.createElement('a');
						link.href = url;
						link.download = file.title;
						document.body.appendChild(link);
						link.click();
						document.body.removeChild(link);
						window.URL.revokeObjectURL(url);
					},
					onSettled: () => {
						setDownloadingFiles(prev => {
							const newSet = new Set(prev);
							newSet.delete(file.id);
							return newSet;
						});
					}
				});
			} catch (error) {
				console.error('Download failed:', error);
				setDownloadingFiles(prev => {
					const newSet = new Set(prev);
					newSet.delete(file.id);
					return newSet;
				});
			}
		}
	};

	const getFileIcon = (fileName: string) => {
		const extension = fileName.split('.').pop()?.toLowerCase();

		switch (extension) {
			case 'pdf':
				return <FileText className="h-5 w-5 text-red-500" />;
			case 'doc':
			case 'docx':
				return <FileText className="h-5 w-5 text-blue-500" />;
			case 'xls':
			case 'xlsx':
				return <FileText className="h-5 w-5 text-green-500" />;
			case 'ppt':
			case 'pptx':
				return <FileText className="h-5 w-5 text-orange-500" />;
			default:
				return <File className="h-5 w-5 text-muted-foreground" />;
		}
	};

	return (
		<div className="h-screen border-t bg-background overflow-hidden flex flex-col w-full max-w-screen overflow-x-hidden">
			{/* Course Content - Takes remaining height */}
			<div className="flex flex-1 max-w-full relative overflow-hidden">
				{/* Main Content - Left Side with Independent Scroll */}
				<div className="flex-1 flex flex-col min-w-0 overflow-hidden lg:mr-0">
					{/* Header - Fixed */}
					<header className="flex-shrink-0 border-b border-border/50 bg-background/95 backdrop-blur-sm p-4">
						<div className="flex items-center justify-between">
							<div className="flex items-center gap-2 md:gap-4 min-w-0 flex-1">
								<Button
									variant="ghost"
									size="sm"
									onClick={() => window.history.back()}
									className="flex-shrink-0"
								>
									<ArrowLeft className="h-4 w-4 md:mr-2" />
									<span className="hidden md:inline">Voltar</span>
								</Button>

								{currentModule && (
									<div className="flex items-center gap-2 min-w-0">
										<BookOpen className="h-4 w-4 md:h-5 md:w-5 text-muted-foreground flex-shrink-0" />
										<span className="font-medium truncate text-sm md:text-base">{currentModule.name}</span>
										{currentLesson && (
											<>
												<span className="text-muted-foreground hidden sm:inline">/</span>
												<span className="text-muted-foreground truncate text-sm hidden sm:inline">{currentLesson.name}</span>
											</>
										)}
									</div>
								)}
							</div>

							<div className="flex items-center gap-1 md:gap-2 flex-shrink-0">
								{/* Navigation Buttons */}
								<Button
									variant="outline"
									size="sm"
									onClick={goToPrevLesson}
									disabled={!prevLesson && modules.findIndex(m => m.id === currentModule?.id) === 0}
									className="px-2 md:px-3"
								>
									<ChevronLeft className="h-4 w-4 md:mr-1" />
									<span className="hidden md:inline">Anterior</span>
								</Button>

								<Button
									variant="outline"
									size="sm"
									onClick={goToNextLesson}
									disabled={!nextLesson && modules.findIndex(m => m.id === currentModule?.id) === modules.length - 1}
									className="px-2 md:px-3"
								>
									<span className="hidden md:inline">Próxima</span>
									<ChevronRight className="h-4 w-4 md:ml-1" />
								</Button>

								<Button
									variant="outline"
									size="sm"
									onClick={toggleSidebar}
									className="px-2 md:px-3 lg:hidden"
								>
									<Menu className="h-4 w-4" />
								</Button>
							</div>
						</div>
					</header>

					{/* Scrollable Content Area - Video + Materials */}
					<div className="flex-1 overflow-y-auto overflow-x-hidden course-scrollbar">
						{/* Video Player Container - 16:9 Aspect Ratio */}
						<div className="p-4 md:p-6 pb-0">
							<div className="w-full aspect-video max-w-full rounded-lg overflow-hidden shadow-lg border border-border/20">
								<VideoPlayer
									videoUrl={currentLesson?.videoUrl || undefined}
									lessonId={currentLesson?.id}
									lessonTitle={currentLesson?.name}
									onNext={nextLesson ? goToNextLesson : undefined}
									onPrevious={prevLesson ? goToPrevLesson : undefined}
									className="w-full h-full"
								/>
							</div>
						</div>

						{/* Content Area - Scrolls with Video */}
						<div className="p-4 md:p-6 pt-4">
							<div className="space-y-6 max-w-full">
								{/* Lesson Info - Redesigned inspired by Rocketseat */}
								{currentLesson && (
									<motion.div
										initial={{ opacity: 0, y: 20 }}
										animate={{ opacity: 1, y: 0 }}
										transition={{ duration: 0.5 }}
									>
										<div className="space-y-6">

											<div className="flex flex-col lg:flex-row lg:items-start gap-6 mt-2">

												<div className="flex-1 min-w-0">
													<div>
{currentLesson.duration && (
															<span className=" flex items-center gap-1 text-xs   pb-2  text-muted-foreground  ">
																<Clock className="h-4 w-4 mr-1" />
																{currentLesson.duration}
															</span>
														)}
													</div>
													<div className="flex items-center gap-3 mb-1">
														<h1 className="text-2xl md:text-3xl font-bold text-foreground leading-tight truncate">
															{currentLesson.name}
														</h1>

													</div>
													{currentLesson.description && (
														<p className="text-base text-muted-foreground leading-relaxed mb-0">
															{currentLesson.description}
														</p>
													)}
												</div>

 												<div className="w-full max-w-xs lg:max-w-[260px] mt-4 lg:mt-0 lg:ml-6">
													<div className="rounded-xl border border-border bg-card/80 shadow-sm px-5 py-4 flex flex-col items-center gap-4">
 														<Button
															variant={currentLesson.userWatchedLessons?.isCompleted ? "primary" : "outline"}
															size="sm"
															onClick={handleToggleCompletion}
															disabled={isTogglingCompletion}
															className={cn(
																"w-full justify-center font-medium transition-all duration-200 h-9 text-sm",
																currentLesson.userWatchedLessons?.isCompleted
																	? "bg-success text-success-foreground border-success hover:bg-success/90"
																	: "border-border bg-background hover:bg-muted"
															)}
														>
															{isTogglingCompletion ? (
																<Loader2 className="h-4 w-4 animate-spin mr-2" />
															) : currentLesson.userWatchedLessons?.isCompleted ? (
																<CheckCircle className="h-4 w-4 mr-2" />
															) : (
																<Circle className="h-4 w-4 mr-2" />
															)}
															{currentLesson.userWatchedLessons?.isCompleted ? "Aula Concluída" : "Marcar como assistida"}
														</Button>
														{/* Estrelas */}
														<div className="w-full flex flex-col items-center gap-1 mt-1">
															<span className="text-xs text-muted-foreground mb-1">O que você achou desta aula?</span>
															<StarRating
																rating={currentLesson.userWatchedLessons?.rating || 0}
																onRatingChange={handleRateLesson}
																readonly={isRating}
																size="md"
																className="gap-1"
															/>
															{(currentLesson.userWatchedLessons?.rating || 0) > 0 && (
																<span className="text-xs text-muted-foreground mt-1">Obrigado pela sua avaliação!</span>
															)}
														</div>
													</div>
												</div>
											</div>


										</div>
									</motion.div>
								)}

								{/* Tabs for Comments and Materials */}
								<Tabs defaultValue="comments" className="w-full minimal-scrollbar">
									<TabsList className="grid w-full grid-cols-2">
										<TabsTrigger value="comments" className="flex cursor-pointer items-center gap-2">
											<MessageCircle className="h-4 w-4" />
											Comentários ({comments.length})
										</TabsTrigger>
										<TabsTrigger value="materials" className="flex cursor-pointer items-center gap-2">
											<Download className="h-4 w-4" />
											Materiais ({files.length})
										</TabsTrigger>
									</TabsList>

									<TabsContent value="comments" className="space-y-4 minimal-scrollbar">
										{/* Comment Input Form */}
										<Card>
											<CardContent className="p-4">
												<div className="space-y-3">
													<Textarea
														placeholder="Adicione um comentário sobre esta aula..."
														value={commentText}
														onChange={(e) => setCommentText(e.target.value)}
														className="min-h-[100px]"
													/>
													<div className="flex justify-end">
														<Button
															onClick={handleAddComment}
															disabled={!commentText.trim() || isAddingComment}
															size="sm"
														>
															{isAddingComment ? (
																<Loader2 className="h-4 w-4 animate-spin mr-2" />
															) : (
																<MessageCircle className="h-4 w-4 mr-2" />
															)}
															Comentar
														</Button>
													</div>
												</div>
											</CardContent>
										</Card>

										{/* Comments List */}
										{commentsLoading ? (
											<div className="space-y-4">
												{[...Array(3)].map((_, i) => (
													<div key={i} className="space-y-2">
														<Skeleton className="h-4 w-1/4 max-w-full" />
														<Skeleton className="h-16 w-full max-w-full" />
													</div>
												))}
											</div>
										) : comments.length > 0 ? (
											<div className="space-y-4">
												{comments.map((comment) => (
													<Card key={comment.id}>
														<CardContent className="p-4">
															<div className="space-y-3">
																{/* Main Comment */}
																<div className="flex items-start gap-3">
																	<div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center flex-shrink-0">
																		<span className="text-sm font-medium">
																			{comment.user.name?.[0] || 'U'}
																		</span>
																	</div>
																	<div className="flex-1 min-w-0">
																		<div className="flex items-center gap-2 mb-1">
																			<span className="font-medium text-sm truncate">
																				{comment.user.name || 'Usuário'}
																			</span>
																			<span className="text-xs text-muted-foreground flex-shrink-0">
																				{new Date(comment.createdAt).toLocaleDateString()}
																			</span>
																		</div>
																		<p className="text-sm break-words">{comment.comment}</p>
																	</div>
																</div>

																{/* Reply Button */}
																<div className="flex items-center gap-2 ml-11">
																	<Button
																		variant="ghost"
																		size="sm"
																		onClick={() => setShowReplyForm(prev => ({
																			...prev,
																			[comment.id]: !prev[comment.id]
																		}))}
																		className="text-xs h-7 px-2"
																	>
																		<Reply className="h-3 w-3 mr-1" />
																		Responder
																	</Button>
																</div>

																{/* Reply Form */}
																{showReplyForm[comment.id] && (
																	<div className="ml-11 space-y-2">
																		<Textarea
																			placeholder="Escreva sua resposta..."
																			value={replyTexts[comment.id] || ""}
																			onChange={(e) => setReplyTexts(prev => ({
																				...prev,
																				[comment.id]: e.target.value
																			}))}
																			className="min-h-[80px]"
																		/>
																		<div className="flex justify-end gap-2">
																			<Button
																				variant="ghost"
																				size="sm"
																				onClick={() => setShowReplyForm(prev => ({
																					...prev,
																					[comment.id]: false
																				}))}
																			>
																				Cancelar
																			</Button>
																			<Button
																				size="sm"
																				onClick={() => handleReplyToComment(comment.id)}
																				disabled={!replyTexts[comment.id]?.trim() || isReplying}
																			>
																				{isReplying ? (
																					<Loader2 className="h-4 w-4 animate-spin mr-2" />
																				) : (
																					<Reply className="h-4 w-4 mr-2" />
																				)}
																				Responder
																			</Button>
																		</div>
																	</div>
																)}

																{/* Replies */}
																{comment.lessonCommentReplies && comment.lessonCommentReplies.length > 0 && (
																	<div className="ml-11 space-y-3 pt-2 border-l-2 border-muted pl-4">
																		{comment.lessonCommentReplies.map((reply) => (
																			<div key={reply.id} className="flex items-start gap-3">
																				<div className="w-6 h-6 rounded-full bg-primary/10 flex items-center justify-center flex-shrink-0">
																					<span className="text-xs font-medium">
																						{reply.user.name?.[0] || 'U'}
																					</span>
																				</div>
																				<div className="flex-1 min-w-0">
																					<div className="flex items-center gap-2 mb-1">
																						<span className="font-medium text-xs truncate">
																							{reply.user.name || 'Usuário'}
																						</span>
																						<span className="text-xs text-muted-foreground flex-shrink-0">
																							{new Date(reply.createdAt).toLocaleDateString()}
																						</span>
																					</div>
																					<p className="text-xs break-words">{reply.replyComment}</p>
																				</div>
																			</div>
																		))}
																	</div>
																)}
															</div>
														</CardContent>
													</Card>
												))}
											</div>
										) : (
											<Card>
												<CardContent className="p-8 text-center">
													<MessageCircle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
													<p className="text-muted-foreground">
														Ainda não há comentários nesta aula.
													</p>
												</CardContent>
											</Card>
										)}
									</TabsContent>

									<TabsContent value="materials" className="space-y-4 minimal-scrollbar">
										{filesLoading ? (
											<div className="space-y-4">
												{[...Array(2)].map((_, i) => (
													<Skeleton key={i} className="h-16 w-full max-w-full" />
												))}
											</div>
										) : files.length > 0 ? (
											<div className="space-y-4">
												{files.map((file) => (
													<Card key={file.id}>
														<CardContent className="p-4">
															<div className="flex items-center justify-between">
																<div className="flex items-center gap-3 min-w-0 flex-1">
																	<div className="w-10 h-10 rounded bg-primary/10 flex items-center justify-center flex-shrink-0">
																		{getFileIcon(file.title)}
																	</div>
																	<div className="min-w-0 flex-1">
																		<p className="font-medium truncate">{file.title}</p>
																		{file.fileSize && (
																			<p className="text-sm text-muted-foreground">
																				{file.fileSize}
																			</p>
																		)}
																	</div>
																</div>
																<Button
																	size="sm"
																	variant="outline"
																	className="flex-shrink-0 ml-4"
																	onClick={() => handleDownloadFile(file)}
																	disabled={downloadingFiles.has(file.id)}
																>
																	{downloadingFiles.has(file.id) ? (
																		<Loader2 className="h-4 w-4 animate-spin mr-2" />
																	) : (
																		<Download className="h-4 w-4 mr-2" />
																	)}
																	{downloadingFiles.has(file.id) ? "Baixando..." : "Baixar"}
																</Button>
															</div>
														</CardContent>
													</Card>
												))}
											</div>
										) : (
											<Card>
												<CardContent className="p-8 text-center">
													<Download className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
													<p className="text-muted-foreground">
														Não há materiais disponíveis para esta aula.
													</p>
												</CardContent>
											</Card>
										)}
									</TabsContent>
								</Tabs>
							</div>
						</div>
					</div>
				</div>

				{/* Right Sidebar - Independent Scroll */}
				<CourseSidebar
					modules={modules}
					lessons={lessons}
					allModuleLessons={allModuleLessons}
					currentModuleId={currentModule?.id}
					currentLessonId={currentLesson?.id}
					isOpen={sidebarState.isOpen}
					onToggle={toggleSidebar}
					onModuleSelect={selectModule}
					onLessonSelect={selectLesson}
					courseProgress={courseProgress}
					className="order-last hidden lg:flex lg:w-96 course-sidebar"
				/>
			</div>
		</div>
	);
}
