import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

export function useMemberAreaSettingsQuery() {
    return useQuery({
        queryKey: ["memberAreaSettings"],
        queryFn: async () => {
            const response = await fetch("/api/member-area-settings");
            if (!response.ok) {
                throw new Error("Failed to fetch member area settings");
            }
            return response.json();
        },
    });
}

export function useMemberAreaSettingsForSupportQuery(organizationId?: string) {
    return useQuery({
        queryKey: ["memberAreaSettings", "support", organizationId],
        queryFn: async () => {
            const response = await fetch("/api/member-area-settings");
            if (!response.ok) {
                // Return null instead of throwing to allow fallback handling
                return null;
            }
            return response.json();
        },
        enabled: !!organizationId,
        staleTime: 5 * 60 * 1000, // 5 minutes
        retry: 1, // Only retry once for support page
    });
}

export function useUpdateMemberAreaSettingsMutation() {
    const queryClient = useQueryClient();
    
    return useMutation({
        mutationFn: async ({ data }: { data: any }) => {
            const response = await fetch("/api/member-area-settings", {
                method: "PATCH",
                headers: {
                    "Content-Type": "application/json",
                },
                body: JSON.stringify(data),
            });
            
            if (!response.ok) {
                throw new Error("Failed to update member area settings");
            }
            
            return response.json();
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ["memberAreaSettings"] });
        },
    });
}