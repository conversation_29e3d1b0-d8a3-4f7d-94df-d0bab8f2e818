"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { useMemberAreaSettingsQuery, useUpdateMemberAreaSettingsMutation } from "@saas/organizations/lib/api";
import { useActiveOrganization } from "@saas/organizations/hooks/use-active-organization";
import { SettingsItem } from "@saas/shared/components/SettingsItem";
import { Button } from "@ui/components/button";
import { Card, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import {
    Form,
    FormControl,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
    FormDescription,
} from "@ui/components/form";
import { Input } from "@ui/components/input";
import { Switch } from "@ui/components/switch";
import { Textarea } from "@ui/components/textarea";
import { useTranslations } from "next-intl";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { z } from "zod";
import { MenuItemsEditor } from "./MenuItemsEditor";
import { FooterEditor } from "./FooterEditor";
import { MemberAreaImageUpload } from "./MemberAreaImageUpload";
import { OrganizationSelector } from "./OrganizationSelector";
import { SettingsIcon, Building2Icon } from "lucide-react";

const memberAreaCustomizationSchema = z.object({
    memberAreaName: z.string().min(1, "Nome da área de membros é obrigatório"),
    primaryColor: z.string().regex(/^#[0-9A-F]{6}$/i, "Cor deve ser um código hexadecimal válido"),
    logoUrl: z.string().url("URL da logo deve ser válida").optional().or(z.literal("")),
    commentsEnabled: z.boolean(),
    supportEmail: z.string().email("Email de suporte deve ser válido"),
    menuItems: z.array(z.object({
        title: z.string(),
        url: z.string(),
        icon: z.string().optional(),
        position: z.number(),
    })),
    footer: z.object({
        text: z.string().optional(),
        links: z.array(z.object({
            title: z.string(),
            url: z.string(),
        })).optional(),
    }),
    // Domains now managed via Organization Domains page
});

type MemberAreaCustomizationFormValues = z.infer<typeof memberAreaCustomizationSchema>;

export function MemberAreaCustomizationForm() {
    const t = useTranslations();
    const { activeOrganization } = useActiveOrganization();
    const { data: settings, isLoading } = useMemberAreaSettingsQuery(activeOrganization?.id || "");
    const updateMutation = useUpdateMemberAreaSettingsMutation();

    const form = useForm<MemberAreaCustomizationFormValues>({
        resolver: zodResolver(memberAreaCustomizationSchema),
        defaultValues: {
            memberAreaName: settings?.memberAreaName || "Cakto Members",
            primaryColor: settings?.primaryColor || "#6366f1",
            logoUrl: settings?.logoUrl || "",
            commentsEnabled: settings?.commentsEnabled || false,
            supportEmail: settings?.supportEmail || "",
            menuItems: settings?.menuItems || [
                { title: "Dashboard", url: "/dashboard", icon: "dashboard", position: 1 },
                { title: "Cursos", url: "/courses", icon: "book", position: 2 },
                { title: "Suporte", url: "/support", icon: "help", position: 3 },
            ],
            footer: settings?.footer || { text: "", links: [] },
        },
    });

    const onSubmit = form.handleSubmit(async (values) => {
        if (!activeOrganization) return;

        try {
            await updateMutation.mutateAsync({
                organizationId: activeOrganization.id,
                data: values,
            });

            toast.success(t("settings.customize.notifications.success"));
        } catch (error) {
            toast.error(t("settings.customize.notifications.error"));
        }
    });

    if (isLoading) {
        return <div>Carregando...</div>;
    }

    if (!activeOrganization) {
        return (
            <div className="space-y-6">
                <div className="bg-blue-50 dark:bg-blue-950/30 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                    <div className="flex items-start gap-3">
                        <div className="flex-shrink-0">
                            <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900/50 rounded-full flex items-center justify-center">
                                <SettingsIcon className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                            </div>
                        </div>
                        <div className="flex-1">
                            <h3 className="text-sm font-medium text-blue-900 dark:text-blue-100">
                                Configuração de Área de Membros
                            </h3>
                            <p className="text-sm text-blue-700 dark:text-blue-300 mt-1">
                                Para configurar uma área de membros, você precisa selecionar uma organização.
                                As áreas de membros são específicas de cada organização e permitem personalizar
                                a experiência dos seus alunos.
                            </p>
                        </div>
                    </div>
                </div>
                <OrganizationSelector />
            </div>
        );
    }

    return (
        <div className="space-y-6">
            <div className="bg-green-50 dark:bg-green-950/30 border border-green-200 dark:border-green-800 rounded-lg p-4">
                <div className="flex items-start gap-3">
                    <div className="flex-shrink-0">
                        <div className="w-8 h-8 bg-green-100 dark:bg-green-900/50 rounded-full flex items-center justify-center">
                            <Building2Icon className="h-4 w-4 text-green-600 dark:text-green-400" />
                        </div>
                    </div>
                                        <div className="flex-1">
                        <h3 className="text-sm font-medium text-green-900 dark:text-green-100">
                            Configurando: {activeOrganization?.name}
                        </h3>
                        <p className="text-sm text-green-700 dark:text-green-300 mt-1">
                            Você está configurando a área de membros da organização <strong>{activeOrganization?.name}</strong>.
                            As alterações aqui afetarão todos os membros desta organização.
                        </p>
                    </div>
                </div>
            </div>

            <Form {...form}>
                <form onSubmit={onSubmit} className="space-y-6">
                    <SettingsItem
                        title="Configurações Gerais"
                        description="Personalize o nome e aparência da sua área de membros"
                    >
                        <div className="grid gap-4">
                            <FormField
                                control={form.control}
                                name="memberAreaName"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>Nome da Área de Membros</FormLabel>
                                        <FormControl>
                                            <Input {...field} placeholder="Nome da sua plataforma" />
                                        </FormControl>
                                        <FormDescription>
                                            Este nome será exibido no cabeçalho e título da sua área de membros
                                        </FormDescription>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />

                            <FormField
                                control={form.control}
                                name="logoUrl"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>Logo da Plataforma</FormLabel>
                                        <FormControl>
                                            <MemberAreaImageUpload
                                                value={field.value}
                                                onChange={field.onChange}
                                                organizationId={activeOrganization.id}
                                                fileType="logo"
                                                label="Logo da Plataforma"
                                                description="Selecione uma imagem para o logo da sua plataforma"
                                            />
                                        </FormControl>
                                        <FormDescription>
                                            Recomendamos uma imagem quadrada com 200x200 pixels ou maior
                                        </FormDescription>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />

                            <FormField
                                control={form.control}
                                name="supportEmail"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>Email de Suporte</FormLabel>
                                        <FormControl>
                                            <Input {...field} type="email" placeholder="<EMAIL>" />
                                        </FormControl>
                                        <FormDescription>
                                            Email que será exibido para contato de suporte
                                        </FormDescription>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                        </div>
                    </SettingsItem>

                    {/* Domains moved to Organization > Domains settings */}

                    <SettingsItem
                        title="Aparência"
                        description="Personalize as cores e estilo da sua área de membros"
                    >
                        <FormField
                            control={form.control}
                            name="primaryColor"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>Cor Principal</FormLabel>
                                    <FormControl>
                                        <div className="flex gap-2">
                                            <Input {...field} type="color" className="w-16 h-10" />
                                            <Input {...field} placeholder="#6366f1" />
                                        </div>
                                    </FormControl>
                                    <FormDescription>
                                        Cor principal que será usada em botões, links e elementos de destaque
                                    </FormDescription>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                    </SettingsItem>

                    <SettingsItem
                        title="Funcionalidades"
                        description="Ative ou desative funcionalidades da sua área de membros"
                    >
                        <FormField
                            control={form.control}
                            name="commentsEnabled"
                            render={({ field }) => (
                                <FormItem className="flex items-center justify-between">
                                    <div>
                                        <FormLabel>Comentários</FormLabel>
                                        <FormDescription>
                                            Permite que os membros comentem nos cursos e lições
                                        </FormDescription>
                                    </div>
                                    <FormControl>
                                        <Switch
                                            checked={field.value}
                                            onCheckedChange={field.onChange}
                                        />
                                    </FormControl>
                                </FormItem>
                            )}
                        />
                    </SettingsItem>

                    <MenuItemsEditor form={form} />

                    <FooterEditor form={form} />

                    <div className="flex justify-end">
                        <Button
                            type="submit"
                            loading={updateMutation.isPending}
                        >
                            Salvar Configurações
                        </Button>
                    </div>
                </form>
            </Form>
        </div>
    );
}
