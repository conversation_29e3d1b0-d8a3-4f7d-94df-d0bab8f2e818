export async function getOrganizationByDomain(host: string, origin?: string) {
  try {
    // Use API route instead of direct Prisma import
    const baseUrl = origin || 'http://localhost:3000';
    const response = await fetch(`${baseUrl}/api/organization/by-domain?host=${encodeURIComponent(host)}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      console.error("Error fetching organization by domain:", response.statusText);
      return null;
    }

    const organization = await response.json();
    return organization;
  } catch (error) {
    console.error("Error fetching organization by domain:", error);
    return null;
  }
}

export function extractSubdomain(host: string): string | null {
  const parts = host.split('.');

  // For aluno.cakto.com.br subdomains
  if (parts.length >= 4 && parts.slice(-3).join('.') === 'aluno.cakto.com.br') {
    return parts[0];
  }

  return null;
}

export function isDomainBasedRequest(host: string): boolean {
  return extractSubdomain(host) !== null || isCustomDomain(host);
}

export function isCustomDomain(host: string): boolean {
  return !host.includes('aluno.cakto.com.br') && !host.includes('localhost');
}
