import { routing } from "@i18n/routing";
import { config as appConfig } from "@repo/config";
import {
	getOrganizationsForSession,
	getSessionFromRequest,
} from "@shared/lib/middleware-helpers";
import { getOrganizationByDomain, isDomainBasedRequest } from "@shared/lib/domain-helpers";
import createMiddleware from "next-intl/middleware";
import { type NextRequest, NextResponse } from "next/server";
import { withQuery } from "ufo";

const intlMiddleware = createMiddleware(routing);

export default async function middleware(req: NextRequest) {
	const { pathname, origin } = req.nextUrl;
	const host = req.headers.get('host') || '';

	// Domain-based organization routing
	if (isDomainBasedRequest(host)) {
		const organization = await getOrganizationByDomain(host, origin);

		if (!organization) {
			return NextResponse.redirect(new URL("/", origin));
		}

		const rewriteUrl = new URL(`/app/${organization.slug}${pathname}`, origin);
		return NextResponse.rewrite(rewriteUrl);
	}

  if (pathname.startsWith("/app")) {
		const response = NextResponse.next();

		if (!appConfig.ui.saas.enabled) {
			return NextResponse.redirect(new URL("/", origin));
		}

		const session = await getSessionFromRequest(req);
		let locale = req.cookies.get(appConfig.i18n.localeCookieName)?.value;

		if (!session) {
			return NextResponse.redirect(
				new URL(
					withQuery("/auth/login", {
						redirectTo: pathname,
					}),
					origin,
				),
			);
		}

		if (
			appConfig.users.enableOnboarding &&
			!session.user.onboardingComplete &&
			pathname !== "/app/onboarding"
		) {
			return NextResponse.redirect(
				new URL(
					withQuery("/app/onboarding", {
						redirectTo: pathname,
					}),
					origin,
				),
			);
		}

    if (pathname.startsWith("/app/admin")) {
      const userRole = session.user?.role;
      if (userRole !== "admin" && userRole !== "producer") {
        return NextResponse.redirect(new URL("/app", origin));
      }
    }

		if (
			!locale ||
			(session.user.locale && locale !== session.user.locale)
		) {
			locale = session.user.locale ?? appConfig.i18n.defaultLocale;
			if (locale) {
				response.cookies.set(appConfig.i18n.localeCookieName, locale);
			}
		}

    if (
			appConfig.organizations.enable &&
			appConfig.organizations.requireOrganization &&
			pathname === "/app"
		) {
      const organizations = await getOrganizationsForSession(req);
      const organization =
        organizations.find(
          (org: any) => org.id === session?.session.activeOrganizationId,
        ) || organizations[0];

      // Preserve query params like productId when redirecting to org homepage
      const search = req.nextUrl.search;
      const redirectBase = organization ? `/app/${organization.slug}` : "/app/new-organization";
      return NextResponse.redirect(new URL(`${redirectBase}${search || ''}`, origin));
		}

		return response;
	}

	if (pathname.startsWith("/auth")) {
		if (!appConfig.ui.saas.enabled) {
			return NextResponse.redirect(new URL("/", origin));
		}

		const session = await getSessionFromRequest(req);

		if (session && pathname !== "/auth/reset-password") {
			return NextResponse.redirect(new URL("/app", origin));
		}

		return NextResponse.next();
	}

	if (!pathname.startsWith("/app") && !pathname.startsWith("/auth")) {
		return NextResponse.redirect(new URL("/app", origin));
	}

	return intlMiddleware(req);
}

export const config = {
	matcher: [
		"/((?!api|image-proxy|images|fonts|_next/static|_next/image|favicon.ico|sitemap.xml|robots.txt).*)",
	],
};
