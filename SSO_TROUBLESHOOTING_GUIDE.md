# Guia de Troubleshooting - SSO Cakto

## Problemas Identificados e Soluções

### 1. **Erro 405 Method Not Allowed**

**Sintoma:** Erro 405 durante o callback OAuth

**Causa:** Conflito entre múltiplos handlers de callback

**Solução:**
- ✅ Remover handlers customizados conflitantes
- ✅ Usar apenas o Better Auth para processar callbacks
- ✅ Configurar corretamente o `redirectURI` no Better Auth

### 2. **Discovery URL Inacessível**

**Sintoma:** `https://sso.cakto.com.br/oauth/.well-known/openid-configuration` retorna página Cloudflare

**Causa:** Proteção Cloudflare ou configuração incorreta

**Soluções:**
1. **Verificar configuração do domínio:**
   ```bash
   # Testar se o domínio responde
   curl -I https://sso.cakto.com.br

   # Testar endpoint específico
   curl -I https://sso.cakto.com.br/oauth/.well-known/openid-configuration
   ```

2. **Configurar Cloudflare:**
   - Adicionar regra para permitir acesso ao endpoint OAuth
   - Configurar bypass para IPs confiáveis

3. **Verificar configuração Django:**
   ```bash
   cd cakto-backend
   python check_oauth_config.py
   ```

### 3. **Incompatibilidade de Scopes**

**Sintoma:** Erro de scope não suportado

**Causa:** Frontend solicita scopes não configurados no backend

**Solução:**
- ✅ **Backend Django:** Suporta `openid` e `user`
- ✅ **Frontend:** Configurado para usar `openid` e `user`

### 4. **Problemas de CORS**

**Sintoma:** Erro de CORS durante autenticação

**Causa:** Domínio do frontend não está na whitelist CORS

**Solução:**
1. **Verificar configuração CORS:**
   ```bash
   cd cakto-backend
   python check_cors_config.py
   ```

2. **Adicionar domínios à whitelist:**
   ```bash
   # .env do backend
   CORS_ORIGIN_WHITELIST=http://localhost:3000,http://localhost:3001,https://seu-dominio.com
   ```

### 5. **Aplicação OAuth2 Não Configurada**

**Sintoma:** Erro de client_id inválido

**Causa:** Aplicação OAuth2 não registrada no backend

**Solução:**
```bash
cd cakto-backend
python setup_oauth_app.py --setup
```

## Checklist de Configuração

### Backend (cakto-backend)

- [ ] **OAuth2 Provider configurado:**
  ```python
  OAUTH2_PROVIDER = {
      "OIDC_ENABLED": True,
      "OAUTH2_VALIDATOR_CLASS": "sso.validators.CustomOAuth2Validator",
      "OIDC_RSA_PRIVATE_KEY": os.getenv('OIDC_RSA_PRIVATE_KEY'),
      "SCOPES": {
          'openid': 'OPENID scope',
          'user': 'Acessar a informações básicas do usuário (E-mail)'
      },
  }
  ```

- [ ] **Aplicação OAuth2 registrada:**
  ```bash
  python setup_oauth_app.py --setup
  ```

- [ ] **CORS configurado:**
  ```bash
  python check_cors_config.py
  ```

- [ ] **Discovery URL acessível:**
  ```bash
  curl -s https://sso.cakto.com.br/oauth/.well-known/openid-configuration
  ```

### Frontend (cakto-members-v2)

- [ ] **Scopes corretos:**
  ```typescript
  scopes: ["openid", "user"]
  ```

- [ ] **Variáveis de ambiente:**
  ```bash
  CAKTO_CLIENT_ID=seu-client-id
  CAKTO_CLIENT_SECRET=seu-client-secret
  ```

- [ ] **URLs de callback configuradas:**
  ```typescript
  redirectURI: `${getBaseUrl()}/api/auth/oauth2/callback/django-sso`
  ```

## Comandos de Diagnóstico

### Verificar Configuração Backend
```bash
cd cakto-backend
python check_oauth_config.py
python check_cors_config.py
```

### Verificar Configuração Frontend
```bash
cd cakto-members-v2
# Verificar variáveis de ambiente
echo $CAKTO_CLIENT_ID
echo $CAKTO_CLIENT_SECRET
```

### Testar Discovery URL
```bash
curl -s https://sso.cakto.com.br/oauth/.well-known/openid-configuration | jq .
```

### Testar Endpoints OAuth2
```bash
# Authorization endpoint
curl -I https://sso.cakto.com.br/oauth/authorize/

# Token endpoint
curl -I https://sso.cakto.com.br/oauth/token/

# User info endpoint
curl -I https://sso.cakto.com.br/oauth/userinfo/
```

## Logs Importantes

### Backend Django
```bash
# Logs de OAuth2
tail -f cakto-backend/django_server.log | grep oauth

# Logs de CORS
tail -f cakto-backend/django_server.log | grep cors
```

### Frontend Next.js
```bash
# Logs do Better Auth
tail -f cakto-members-v2/.next/server.log | grep auth
```

## Problemas Comuns

### 1. **Erro de Rede**
- Verificar conectividade entre frontend e backend
- Verificar configuração de proxy/reverse proxy
- Verificar configuração de DNS

### 2. **Erro de Certificado SSL**
- Verificar certificados SSL do domínio
- Configurar certificados para desenvolvimento local

### 3. **Erro de Sessão**
- Verificar configuração de cookies
- Verificar configuração de domínios
- Verificar configuração de SameSite

### 4. **Erro de Timeout**
- Aumentar timeouts de rede
- Verificar performance do backend
- Verificar configuração de load balancer

## Próximos Passos

1. **Executar scripts de diagnóstico**
2. **Configurar aplicação OAuth2 se necessário**
3. **Verificar e corrigir configuração CORS**
4. **Testar fluxo completo de autenticação**
5. **Monitorar logs para identificar problemas**
6. **Configurar alertas para falhas de SSO**
