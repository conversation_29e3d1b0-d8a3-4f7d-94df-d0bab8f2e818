# Configuração para desenvolvimento local com domínios personalizados
# Este arquivo deve ser usado apenas em desenvolvimento

# Domínios locais configurados
VITE_LOCAL_DOMAIN=aluno.cakto.local
VITE_MEMBER_AREA_DOMAIN=aluno.cakto.local

# URLs base para desenvolvimento
VITE_API_URL=http://localhost:3000
VITE_FRONTEND_URL=http://aluno.cakto.local:5173

# Configuração de ambiente
VITE_ENVIRONMENT=development
VITE_DEBUG_DOMAIN=true

# Configuração de CORS para aceitar domínios locais
CORS_ORIGIN=http://localhost:5173,http://aluno.cakto.local:5173,http://empresax.aluno.cakto.local:5173,http://academy.aluno.cakto.local:5173,http://premium.aluno.cakto.local:5173,http://marketing.aluno.cakto.local:5173,http://techcorp.aluno.cakto.local:5173,http://afiliados.aluno.cakto.local:5173,http://edutech.aluno.cakto.local:5173,http://innovate.aluno.cakto.local:5173,http://cursosdocaio.local:5173,http://minhaareademembers.local:5173

# Configuração do banco de dados (ajuste conforme necessário)
DATABASE_URL="postgresql://username:password@localhost:5432/cakto_members_dev"

# JWT Secret (use uma chave segura em produção)
JWT_SECRET="your-jwt-secret-key-for-development"

# Configuração OAuth SSO com Cakto Backend
CAKTO_CLIENT_ID="8OSwfrDXekfVWBmmBWEspycBUMW2ytx2tcWOKFOt"
CAKTO_CLIENT_SECRET="pbkdf2_sha256$600000$nMBjohB04mddsjpD3pGXIg$5IaE25oDi6Y8iSqetwCU7+VBTac7wtKDhQ64AVCzgyc="
CAKTO_API_URL="http://localhost:8000"
NEXT_PUBLIC_CAKTO_API_URL="http://localhost:8000"
NEXT_PUBLIC_APP_URL="http://localhost:3000"

# Configuração de upload (ajuste conforme necessário)
UPLOAD_DIR="./uploads"
MAX_FILE_SIZE=10485760

# Configuração de email (ajuste conforme necessário)
SMTP_HOST="localhost"
SMTP_PORT=587
SMTP_USER=""
SMTP_PASS=""
SMTP_FROM="<EMAIL>"