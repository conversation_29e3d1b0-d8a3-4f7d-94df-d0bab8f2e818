#!/usr/bin/env python
import os
import django
import sys
from django.conf import settings

# Configurar o ambiente Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'cakto.settings')
django.setup()

from oauth2_provider.models import Application

def update_sso_callback_url():
    """
    Atualiza a URL de callback da aplicação OAuth2 existente
    """
    print("Atualizando URL de callback do SSO...")

    # Client ID usado no projeto
    client_id = 'Pa0FEHDMJCo6jIJ3DbhljUW6NUL1FUgp2z1FxXpN'
    new_redirect_uri = 'http://localhost:3000/api/auth/callback/django-sso'

    try:
        app = Application.objects.get(client_id=client_id)
        print(f"Aplicação encontrada: {app.name}")
        print(f"URL atual: {app.redirect_uris}")

        # Atualizar a URL de callback
        app.redirect_uris = new_redirect_uri
        app.save()

        print(f"URL atualizada para: {new_redirect_uri}")
        print("Atualização concluída com sucesso!")

        return True

    except Application.DoesNotExist:
        print(f"Aplicação com client_id '{client_id}' não encontrada.")
        print("Criando nova aplicação...")

        # Criar nova aplicação se não existir
        from django.contrib.auth import get_user_model
        User = get_user_model()

        try:
            admin_user = User.objects.filter(is_superuser=True).first()
            if not admin_user:
                print("Nenhum usuário admin encontrado. Criando usuário admin...")
                admin_user = User.objects.create_superuser(
                    username='admin',
                    email='<EMAIL>',
                    password='admin123'
                )

            app = Application.objects.create(
                user=admin_user,
                client_id=client_id,
                client_secret='KqLDBTCThIh82h2kOPRK7VBbpVyAOoQSTXSJDmdOeSvO5HeLrMX6PeuesRK6qcZGZJ8aoYcLOxy9htHzmBo57O1yPdcCXCCNlolJMt7P1NyiSs8ePOgT0NJXsjIT6O9f',
                name='Cakto Members SSO',
                redirect_uris=new_redirect_uri,
                client_type=Application.CLIENT_CONFIDENTIAL,
                authorization_grant_type=Application.GRANT_AUTHORIZATION_CODE,
                skip_authorization=True
            )

            print(f"Nova aplicação criada: {app.name}")
            print(f"Client ID: {app.client_id}")
            print(f"Redirect URI: {app.redirect_uris}")

            return True

        except Exception as e:
            print(f"Erro ao criar aplicação: {e}")
            return False

    except Exception as e:
        print(f"Erro ao atualizar aplicação: {e}")
        return False

if __name__ == "__main__":
    update_sso_callback_url()
